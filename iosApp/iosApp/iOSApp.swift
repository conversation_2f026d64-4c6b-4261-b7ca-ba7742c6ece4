import SwiftUI
import ComposeApp

@main
struct iOSApp: SwiftUI.App {
    init() {
        // 确保应用启动时设置默认语言
        let languageCode = ComposeApp.CacheManager.shared.getLanguage()
        UserDefaults.standard.set([languageCode], forKey: "AppleLanguages")
        UserDefaults.standard.set(languageCode, forKey: "AppleLocale")
        UserDefaults.standard.set(languageCode, forKey: "AppleLanguageCode")
        UserDefaults.standard.synchronize()
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}