import UIKit
import SwiftUI
import ComposeApp

struct ComposeView: UIViewControllerRepresentable {
    func makeUIViewController(context: Context) -> UIViewController {
        MainViewControllerKt.MainViewController()
    }

    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {}
}

struct ContentView: View {
    @State private var isActive = false
    @State private var currentSplashIndex = 0
    
    // 闪屏图片数组
    private let splashImages = ["SplashImage", "SplashImage2", "SplashImage3"]
    // 每张图片显示的时间（秒）
    private let displayDurations = [1.5, 2, 2]
    
    var body: some View {
        if isActive {
            ComposeView()
                .ignoresSafeArea(.keyboard) // 忽略所有安全区域，包括状态栏
                .edgesIgnoringSafeArea(.all) // 确保内容可以扩展到边缘
        } else {
            ZStack {
                Color.white
                
                Image(splashImages[currentSplashIndex])
                    .resizable()
                    .scaledToFill()
                    .edgesIgnoringSafeArea(.all)
                    .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
            }
            .ignoresSafeArea()
            .onAppear {
                startSplashSequence()
            }
        }
    }
    
    // 启动闪屏序列展示
    private func startSplashSequence() {
        if currentSplashIndex < splashImages.count {
            // 显示当前闪屏图片指定时间后，进入下一张
            DispatchQueue.main.asyncAfter(deadline: .now() + displayDurations[currentSplashIndex]) {
                if currentSplashIndex < splashImages.count - 1 {
                    // 显示下一张图片
                    currentSplashIndex += 1
                    startSplashSequence()
                } else {
                    // 所有图片显示完毕，进入主界面
                    isActive = true
                }
            }
        }
    }
}



