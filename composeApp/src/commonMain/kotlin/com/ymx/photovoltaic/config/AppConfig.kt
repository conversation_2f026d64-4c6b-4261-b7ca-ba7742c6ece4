package com.ymx.photovoltaic.config

import com.ymx.photovoltaic.data.local.CacheManager

object AppConfig {
    // 品牌类型
    const val BRAND_YMX = "ymx"
    const val BRAND_LJ = "lj"

    private const val BUILDTYPE_DEBUG = "debug"
    private const val BUILDTYPE_RELEASE = "release"
    
    // 默认品牌，可以通过平台特定代码设置
    private var currentBrand = BRAND_LJ

    private var currentBuildType = BUILDTYPE_DEBUG

    fun setBuildType(buildType: String) {
        currentBuildType = buildType
    }

    // 获取当前品牌
    fun getBuildType(): String {
        return currentBuildType
    }
    
    // 设置当前品牌
    fun setBrand(brand: String) {
        currentBrand = brand
    }
    
    // 获取当前品牌
    fun getBrand(): String {
        return currentBrand
    }
    
    // 根据当前品牌和语言获取联系信息URL
    fun getContactInfoUrl(): String {
        val language = CacheManager.getLanguage()
        return if (language == "zh") {
            if (currentBrand == BRAND_LJ) CONTACT_INFO_URL_LJ_CN else CONTACT_INFO_URL_YMX_CN
        } else {
            if (currentBrand == BRAND_LJ) CONTACT_INFO_URL_LJ_EN else CONTACT_INFO_URL_YMX_EN
        }
    }
    
    // 根据当前品牌和语言获取用户手册URL
    fun getUserManualUrl(): String {
        val language = CacheManager.getLanguage()
        return if (language == "zh") {
            if (currentBrand == BRAND_LJ) USER_MANUAL_URL_LJ_CN else USER_MANUAL_URL_YMX_CN
        } else {
            if (currentBrand == BRAND_LJ) USER_MANUAL_URL_LJ_EN else USER_MANUAL_URL_YMX_EN
        }
    }
    
    // 根据当前品牌和语言获取隐私政策URL
    fun getPrivacyPolicyUrl(): String {
        val language = CacheManager.getLanguage()
        return if (language == "zh") {
            if (currentBrand == BRAND_LJ) PRIVACY_POLICY_URL_LJ_CN else PRIVACY_POLICY_URL_YMX_CN
        } else {
            if (currentBrand == BRAND_LJ) PRIVACY_POLICY_URL_LJ_EN else PRIVACY_POLICY_URL_YMX_EN
        }
    }
    
    // 根据当前品牌和语言获取用户协议URL
    fun getUserAgreementUrl(): String {
        val language = CacheManager.getLanguage()
        return if (language == "zh") {
            if (currentBrand == BRAND_LJ) USER_AGREEMENT_URL_LJ_CN else USER_AGREEMENT_URL_YMX_CN
        } else {
            if (currentBrand == BRAND_LJ) USER_AGREEMENT_URL_LJ_EN else USER_AGREEMENT_URL_YMX_EN
        }
    }
    
    // YMX品牌URL
    const val CONTACT_INFO_URL_YMX_CN = "http://download.yimeixu.com:6273/ymx/cn/ymx_contact_info_cn.html"
    const val CONTACT_INFO_URL_YMX_EN = "http://download.yimeixu.com:6273/ymx/en/ymx_contact_info_en.html"
    const val USER_MANUAL_URL_YMX_CN = "http://download.yimeixu.com:6273/ymx/cn/ymx_user_manual_cn.html"
    const val USER_MANUAL_URL_YMX_EN = "http://download.yimeixu.com:6273/ymx/en/ymx_user_manual_en.html"
    const val PRIVACY_POLICY_URL_YMX_CN = "http://download.yimeixu.com:6273/ymx/cn/ymx_privacy_policy_cn.html"
    const val PRIVACY_POLICY_URL_YMX_EN = "http://download.yimeixu.com:6273/ymx/en/ymx_privacy_policy_en.html"
    const val USER_AGREEMENT_URL_YMX_CN = "http://download.yimeixu.com:6273/ymx/cn/ymx_user_agreement_cn.html"
    const val USER_AGREEMENT_URL_YMX_EN = "http://download.yimeixu.com:6273/ymx/en/ymx_user_agreement_en.html"
    
    // LJ品牌URL
    const val CONTACT_INFO_URL_LJ_CN = "http://download.yimeixu.com:6273/lj_new/cn/lj_contact_info_cn.html"
    const val CONTACT_INFO_URL_LJ_EN = "http://download.yimeixu.com:6273/lj_new/en/lj_contact_info_en.html"
    const val USER_MANUAL_URL_LJ_CN = "http://download.yimeixu.com:6273/lj_new/cn/lj_user_manual_cn.html"
    const val USER_MANUAL_URL_LJ_EN = "http://download.yimeixu.com:6273/lj_new/en/lj_user_manual_en.html"
    const val PRIVACY_POLICY_URL_LJ_CN = "http://download.yimeixu.com:6273/lj_new/cn/lj_privacy_policy_cn.html"
    const val PRIVACY_POLICY_URL_LJ_EN = "http://download.yimeixu.com:6273/lj_new/en/lj_privacy_policy_en.html"
    const val USER_AGREEMENT_URL_LJ_CN = "http://download.yimeixu.com:6273/lj_new/cn/lj_user_agreement_cn.html"
    const val USER_AGREEMENT_URL_LJ_EN = "http://download.yimeixu.com:6273/lj_new/en/lj_user_agreement_en.html"
} 