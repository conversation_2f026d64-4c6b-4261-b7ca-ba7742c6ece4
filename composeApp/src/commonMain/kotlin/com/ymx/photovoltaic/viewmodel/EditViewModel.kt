package com.ymx.photovoltaic.viewmodel

import com.ymx.photovoltaic.base.BaseViewModel
import com.ymx.photovoltaic.data.DataRepository
import com.ymx.photovoltaic.data.bean.Station
import com.ymx.photovoltaic.data.bean.WarningSetting
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class EditViewModel: BaseViewModel<String>() {

    private val _stationFlow = MutableStateFlow<Station?>(null)
    val stationFlow: StateFlow<Station?> = _stationFlow

    private val _warningSettingFlow = MutableStateFlow<WarningSetting?>(null)
    val warningSettingFlow: StateFlow<WarningSetting?> = _warningSettingFlow

    /** 请求电站详情 */
    fun fetchStation(powerStationId: String) {
        launch({
            handleRequest(DataRepository.queryPowerStationById(powerStationId)) {
                _stationFlow.value = it.reModel
            }
        })
    }

    /** 请求警报设置 */
    fun fetchWarningSetting(powerStationId: String) {
        launch({
            handleRequest(DataRepository.queryWarningSettingList(powerStationId)) {
                _warningSettingFlow.value = it.reModel.warningSettingModel
            }
        })
    }

    /** 请求警报设置 */
    fun updateWarningSettingList(
        powerStationId: String,
        componentTemperature: Int?,
        offValue: Int?,
        keepOutRate: Float?,
        keepOutTime: Int?,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequestWithNull(
                DataRepository.updateWarningSettingList(
                    powerStationId, componentTemperature, offValue,
                    keepOutRate, keepOutTime
                ), errorBlock = {
                    errorBlock()
                    true
                })
            {
                successCall.invoke()
            }
        })
    }

    /** 修改电站 */
    fun updatePowerStationModel(
        stationMap:Map<String,String>,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequestWithNull(
                DataRepository.updatePowerStationModel(stationMap),
                errorBlock = {
                    errorBlock()
                    true
                })
            {
                successCall.invoke()
            }
        })
    }
}