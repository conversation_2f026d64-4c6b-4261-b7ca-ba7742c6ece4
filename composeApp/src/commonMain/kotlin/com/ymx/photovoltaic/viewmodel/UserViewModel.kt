package com.ymx.photovoltaic.viewmodel

import com.ymx.photovoltaic.base.BaseViewModel
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.DataRepository
import com.ymx.photovoltaic.data.local.CacheManager

/**
 */
class UserViewModel  : BaseViewModel<String>() {

    /**
     * 登录
     * @param userName 用户名
     * @param pwd 密码
     */
    fun login(
        userName: String,
        pwd: String,
        registId: String,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        // launch 传参默认只传 tryBlock，catchBlock 和 finallyBlock 异常情况和最后处理默认是空实现
        // successBlock 处理返回成功情况 errorBlock 处理返回错误情况
        launch({
            handleRequestWithNull(DataRepository.login(userName, pwd, registId), errorBlock = {
                errorBlock()
                true
            }) {
                // 怎么校验服务器数据是否正确 如果it.reModel.data.mId为""怎么办
                it.reModel?.let {
                    it1 -> CacheManager.loginOk(it1.data)
                    AppGlobal.mId=it1.data.mId
                    AppGlobal.userName=it1.data.phone
                }
                successCall.invoke()
            }
        })
    }

    fun register(
        phone: String,
        pwd: String,
        loginCode: String? = null,
        smsCode: String? = null,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequestWithNull(DataRepository.register(phone, pwd, loginCode, smsCode), errorBlock = {
                errorBlock()
                true
            }) {
                successCall.invoke()
            }
        })
    }

    /**
     * 发送短信验证码
     * @param account 账号
     * @param type 验证码类型
     * @param errorBlock 错误回调
     * @param successCall 成功回调
     */
    fun sendSms(
        account: String,
        type: String,
        errorBlock: (String) -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequestWithNull(
                DataRepository.sendSms(account, type),
                errorBlock = { response ->
                    errorBlock(response.errMsg)
                    true
                }
            ) {
                successCall.invoke()
            }
        })
    }

    /**
     * 重置密码
     * @param account 账号
     * @param code 验证码
     * @param password 新密码
     * @param errorBlock 错误回调
     * @param successCall 成功回调
     */
    fun resetPassword(
        account: String,
        code: String,
        password: String,
        type: String,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequestWithNull(
                DataRepository.resetPassword(account, code, password,type),
                errorBlock = {
                    errorBlock()
                    true
                }
            ) {
                successCall.invoke()
            }
        })
    }
}