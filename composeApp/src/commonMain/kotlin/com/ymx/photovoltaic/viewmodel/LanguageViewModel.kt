package com.ymx.photovoltaic.viewmodel

import androidx.lifecycle.viewModelScope
import com.ymx.photovoltaic.base.BaseViewModel
import com.ymx.photovoltaic.data.local.CacheManager
import com.ymx.photovoltaic.language.Language
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class LanguageViewModel : BaseViewModel<String>() {
    // 初始化时从CacheManager获取当前语言设置
    private val _language = MutableStateFlow(
        when(CacheManager.getLanguage()) {
            "en" -> Language("en", "English")
            else -> Language("zh", "中文")
        }
    )
    val language = _language.asStateFlow()
    
    // 获取当前语言代码
    fun getCurrentLanguageCode(): String = _language.value.code
    
    // 设置新语言并通知UI更新
    fun setLanguage(newLanguage: Language) {
        // 保存到本地存储
        CacheManager.saveLanguage(newLanguage.code)
        
        // 更新StateFlow，通知所有订阅者
                viewModelScope.launch {
            _language.emit(newLanguage)
        }
    }
}