package com.ymx.photovoltaic.viewmodel

import androidx.lifecycle.viewModelScope
import com.ymx.photovoltaic.base.BaseViewModel
import com.ymx.photovoltaic.data.DataRepository
import com.ymx.photovoltaic.data.bean.Region
import com.ymx.photovoltaic.data.bean.Station
import com.ymx.photovoltaic.data.bean.Warning
import com.ymx.photovoltaic.data.bean.WarningStats
import com.ymx.photovoltaic.data.bean.Weather
import com.ymx.photovoltaic.ui.page.widget.RegionDataManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class HomeViewModel : BaseViewModel<String>() {

    private val _stationListFlow = MutableStateFlow<List<Station>>(emptyList())
    val stationListFlow: StateFlow<List<Station>> = _stationListFlow

    private val _warningListFlow = MutableStateFlow<List<Warning>>(emptyList())
    val warningListFlow: StateFlow<List<Warning>> = _warningListFlow

    private val _warningTypeCountFlow = MutableStateFlow<WarningStats?>(null)
    val warningTypeCountFlow: StateFlow<WarningStats?> = _warningTypeCountFlow

    private val _todayWarningDetailsFlow = MutableStateFlow<List<Warning>>(emptyList())
    val todayWarningDetailsFlow: StateFlow<List<Warning>> = _todayWarningDetailsFlow

    private val _regionsFlow = MutableStateFlow<List<Region>>(emptyList())
    val regionsFlow: StateFlow<List<Region>> = _regionsFlow
    
    // 添加天气状态流
    private val _weatherFlow = MutableStateFlow<Map<String, Weather>>(emptyMap())
    val weatherFlow: StateFlow<Map<String, Weather>> = _weatherFlow

    // 添加未读消息数量状态流
    private val _unreadMessageCountFlow = MutableStateFlow<Int>(0)
    val unreadMessageCountFlow: StateFlow<Int> = _unreadMessageCountFlow

    /** 请求电站列表 */
    fun fetchStationList(mId: String, userType: String) {
        launch({
            handleRequest(DataRepository.queryPowerStationList(mId, userType)) {
                _stationListFlow.value = it.reModel.data
            }
        })
    }

    /** 请求告警列表 */
    fun fetchWaringList(mId: String, pageNo: Int) {
        launch({
            handleRequest(DataRepository.queryWarningList(mId, pageNo)) {
                _warningListFlow.value = it.reModel.data
            }
        })
    }

    /** 查询警告类型数量 */
    fun fetchWarningTypeCount(powerIdList: List<String>) {
        launch({
            handleRequest(DataRepository.queryWarningTypeCount(powerIdList)) {
                _warningTypeCountFlow.value = it.reModel
            }
        })
    }

    /** 查询今日警告详情 */
    fun fetchTodayWarningDetails(powerIdList: List<String>) {
        launch({
            handleRequest(DataRepository.queryTodayWarningDetails(powerIdList)) {
                _todayWarningDetailsFlow.value = it.reModel
            }
        })
    }

    /** 查询未读消息数量 */
    fun fetchUnreadMessageCount(powerIdList: List<String>) {
        launch({
            handleRequest(DataRepository.queryUnreadMessageCount(powerIdList)) {
                _unreadMessageCountFlow.value = it.reModel
            }
        })
    }

    /** 请求地区数据 */
    private suspend fun queryRegion(pid: Int, level: Int, language: String): List<Region> {
        var regions = emptyList<Region>()
        handleRequest(
            DataRepository.queryRegion(pid, level, language)
        ) {
            regions = it.reModel
        }
        return regions
    }
    
    /** 获取天气信息 */
    fun getWeather(districtId: String, dataType: String) {
        launch({
            handleRequest(DataRepository.getWeather(districtId, dataType)) {
                val currentWeatherMap = _weatherFlow.value.toMutableMap()
                currentWeatherMap[districtId] = it.reModel
                _weatherFlow.value = currentWeatherMap
            }
        })
    }
    
    /** 获取多个地区的天气信息 */
    fun getMultipleWeather(districtIds: List<String>) {
        districtIds.forEach { districtId ->
            getWeather(districtId, "now")
        }
    }

    /** 获取地区数据 */
    fun fetchRegions(language: String) {
        viewModelScope.launch {
            try {
                // 获取三个层级的数据
                val level2Regions = queryRegion(0, 2, language)
                delay(100)
                val level3Regions = queryRegion(0, 3, language)
                delay(100)
                val level4Regions = queryRegion(0, 4, language)

                // 合并所有地区数据
                val allRegions = mutableListOf<Region>()
                allRegions.addAll(level2Regions)
                allRegions.addAll(level3Regions)
                allRegions.addAll(level4Regions)

                // 更新LiveData
                _regionsFlow.value = allRegions

                // 初始化RegionDataManager
                RegionDataManager.init(allRegions)
            } catch (e: Exception) {
                e.printStackTrace()
                RegionDataManager.reset()
            }
        }
    }
    
    /** 设置消息为已读 */
    fun setMessageRead(ids: String) {
        launch({
            handleRequestWithNull(
                DataRepository.setMessageRead(ids))
        })
    }
}