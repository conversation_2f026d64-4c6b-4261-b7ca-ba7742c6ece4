package com.ymx.photovoltaic.viewmodel

import com.ymx.photovoltaic.base.BaseViewModel
import com.ymx.photovoltaic.data.DataRepository
import com.ymx.photovoltaic.data.bean.StationView
import com.ymx.photovoltaic.data.bean.TendDayInfo
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class StationViewModel : BaseViewModel<String>() {

    private val _stationViewFlow = MutableStateFlow<StationView?>(null)
    val stationViewFlow: StateFlow<StationView?> = _stationViewFlow

    private val _tendYearFlow = MutableStateFlow<TendDayInfo?>(null)
    val tendYearFlow: StateFlow<TendDayInfo?> = _tendYearFlow

    /** 请求电站列表 */
    fun fetchComponentList(
        powerStationId: String,
        date: String,
        type: String,
        sunUpTime: String,
        sunDownTime: String,
        from: String,
        groupId: String?
    ) {
        launch({
            handleRequest(
                DataRepository.queryComponentList(
                    powerStationId, date, type, 
                    sunUpTime, sunDownTime, from, groupId
                )
            ) {
                _stationViewFlow.value = it.reModel
            }
        })
    }

    /** 远程控制确认 */
    fun remoteControllerAck(
        powerStationId: String?,
        groupId: String?,
        chipId: String?,
        flag: Int,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequestWithNull(
                DataRepository.remoteControllerAck(
                    powerStationId = powerStationId,
                    status = flag,
                    belongsGroupId = groupId,
                    id = chipId,
                    flag = flag
                ),
                errorBlock = {
                    errorBlock()
                    true
                }
            ) {
                successCall.invoke()
            }
        })
    }

    fun queryReportYearData(powerStationId: String, date: String) {
        launch({
            handleRequest(DataRepository.queryReportByMonth(powerStationId, date)) {
                _tendYearFlow.value = it.reModel
            }
        })
    }
}