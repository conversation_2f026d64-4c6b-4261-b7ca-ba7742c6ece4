package com.ymx.photovoltaic.viewmodel

import com.ymx.photovoltaic.base.BaseViewModel
import com.ymx.photovoltaic.data.DataRepository
import com.ymx.photovoltaic.data.bean.HistoryKwh
import com.ymx.photovoltaic.data.bean.PowerDayInfo
import com.ymx.photovoltaic.data.bean.SearchGroup
import com.ymx.photovoltaic.data.bean.TendDayInfo
import com.ymx.photovoltaic.data.bean.TendMinuteGroupInfo
import com.ymx.photovoltaic.data.bean.TendMinuteInfo
import com.ymx.photovoltaic.data.bean.Weather
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class ReportViewModel : BaseViewModel<String>() {

    private val _powerDayFlow = MutableStateFlow<PowerDayInfo?>(null)
    val powerDayFlow: StateFlow<PowerDayInfo?> = _powerDayFlow

    private val _groupFlow = MutableStateFlow<List<SearchGroup>>(emptyList())
    val groupFlow: StateFlow<List<SearchGroup>> = _groupFlow

    private val _tendMonthFlow = MutableStateFlow<TendDayInfo?>(null)
    val tendMonthFlow: StateFlow<TendDayInfo?> = _tendMonthFlow

    private val _tendYearFlow = MutableStateFlow<TendDayInfo?>(null)
    val tendYearFlow: StateFlow<TendDayInfo?> = _tendYearFlow

    private val _tendAllFlow = MutableStateFlow<TendDayInfo?>(null)
    val tendAllFlow: StateFlow<TendDayInfo?> = _tendAllFlow

    private val _tendMinuteFlow = MutableStateFlow<TendMinuteInfo?>(null)
    val tendMinuteFlow: StateFlow<TendMinuteInfo?> = _tendMinuteFlow

    private val _tendMinuteGroupFlow = MutableStateFlow<TendMinuteGroupInfo?>(null)
    val tendMinuteGroupFlow: StateFlow<TendMinuteGroupInfo?> = _tendMinuteGroupFlow

    private val _weatherFlow = MutableStateFlow<Weather?>(null)
    val weatherFlow: StateFlow<Weather?> = _weatherFlow

    private val _historyKwhFlow = MutableStateFlow<HistoryKwh?>(null)
    val historyKwhFlow: StateFlow<HistoryKwh?> = _historyKwhFlow

    /** 查询总的统计数据*/
    fun queryTotalReport(powerStationId: String) {
        launch({
            handleRequest(DataRepository.queryTotalReport(powerStationId)) {
                _powerDayFlow.value = it.reModel
            }
        })
    }

    fun queryReportByDate(powerStationId: String, date: String, flag: String) {
        launch({
            handleRequest(
                when(flag) {
                    "day" -> DataRepository.queryReportByDay(powerStationId, date)
                    "month" -> DataRepository.queryReportByMonth(powerStationId, date)
                    "year" -> DataRepository.queryReportByYear(powerStationId)
                    else -> DataRepository.queryReportByYear(powerStationId)
                }
            ) {
                when(flag) {
                    "day" -> _tendMonthFlow.value = it.reModel
                    "month" -> _tendYearFlow.value = it.reModel
                    "year" -> _tendAllFlow.value = it.reModel
                    else -> _tendMonthFlow.value = it.reModel
                }
            }
        })
    }

    fun queryGroup(powerStationId: String,groupName: String) {
        launch({
            handleRequest(
                DataRepository.queryGroup(powerStationId,groupName)
            )
            {
                _groupFlow.value = it.reModel.data
            }
        })
    }

    fun queryReportByMinuteGroup(powerStationId: String,
                                 day: String,
                                 groupName: String,
                                 groupId: String) {
        launch({
            handleRequest(
                DataRepository.queryReportByMinuteGroup(powerStationId,day,
                    groupName,groupId)
            )
            {
                _tendMinuteGroupFlow.value = it.reModel
            }
        })
    }

    fun queryReportByMinute(powerStationId: String,day: String) {
        launch({
            handleRequest(
                DataRepository.queryReportByMinute(powerStationId,day)
            )
            {
                _tendMinuteFlow.value = it.reModel
            }
        })
    }

    fun getWeather(districtId: String, dataType: String) {
        launch({
            handleRequest(DataRepository.getWeather(districtId, dataType)) {
                _weatherFlow.value = it.reModel
            }
        })
    }

    /** 查询历史电量 */
    fun queryHistoryKwh(powerStationId: String, day: String) {
        launch({
            handleRequest(DataRepository.queryHistoryKwh(powerStationId, day)) {
                _historyKwhFlow.value = it.reModel
            }
        })
    }
}