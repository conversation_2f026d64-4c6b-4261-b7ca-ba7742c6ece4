package com.ymx.photovoltaic.viewmodel

import com.ymx.photovoltaic.base.BaseViewModel
import com.ymx.photovoltaic.data.local.CacheManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

/**
 * <AUTHOR>  2022/4/21
 */
class AppScreenViewModel : BaseViewModel<Unit>() {

    private val _isFirstUse = MutableStateFlow(CacheManager.isFirstUse())
    val isFirstUse: StateFlow<Boolean> = _isFirstUse

    private val _isLogin = MutableStateFlow(CacheManager.isLogin())
    val isLogin: StateFlow<Boolean> = _isLogin

    fun loginOk() {
        // 假设这里是登录逻辑，登录成功后更新用户状态
        _isLogin.value = true
        // 可以在这里保存用户登录状态到本地存储，例如 SharedPreferences 或数据库
        CacheManager.saveIsLogin(true)
    }

    fun logout() {
        _isLogin.value = false
        CacheManager.saveIsLogin(false)
    }

    fun emitFirstUse(firstUse: Boolean) {
        _isFirstUse.value = firstUse
        CacheManager.saveFirstUse(firstUse)
    }
}