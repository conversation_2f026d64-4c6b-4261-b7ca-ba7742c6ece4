package com.ymx.photovoltaic.viewmodel

import com.ymx.photovoltaic.base.BaseViewModel
import com.ymx.photovoltaic.data.DataRepository
import com.ymx.photovoltaic.data.bean.Version
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

/**
 */

class MyViewModel : BaseViewModel<String>() {

    private val _versionFlow = MutableStateFlow<Version?>(null)
    val versionFlow: StateFlow<Version?> = _versionFlow

    /**
     * 修改密码
     */
    fun changePwd(
        userName: String,
        oldPwd: String,
        newPwd: String,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequestWithNull(DataRepository.changePwd(userName, oldPwd,newPwd), errorBlock = {
                errorBlock()
                true
            }) {
                successCall.invoke()
            }
        })
    }

    fun saveFeedback(
        information: String,
        content: String,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequest(DataRepository.saveFeedback(information, content), errorBlock = {
                errorBlock()
                true
            }) {
                successCall.invoke()
            }
        })
    }

    fun queryAppVersion(
        versionNumber: String,
        language: String,
        errorBlock: () -> Unit = {},
        successCall: () -> Unit = {}
    ) {
        launch({
            handleRequest(DataRepository.appVersionList(versionNumber,language), errorBlock = {
                errorBlock()
                true
            }) {
                _versionFlow.value = it.reModel
            }
        })
    }
}