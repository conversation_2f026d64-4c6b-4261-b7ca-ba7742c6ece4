package com.ymx.photovoltaic.platform

import kotlinx.coroutines.flow.Flow

/**
 * 文件工具类 - 跨平台共享接口
 */
expect object FileUtil {
    /**
     * 保存文本内容到临时文件
     * @param content 要保存的文本内容
     * @param fileName 文件名
     * @return 流，包含结果为文件路径或异常
     */
    suspend fun saveTextToTempFile(content: String, fileName: String): Flow<Result<String>>

    /**
     * 分享文件到微信
     * @param filePath 文件路径
     * @param mimeType 文件MIME类型，如果为null则自动检测
     * @return 流，包含结果为成功/失败或异常
     */
    suspend fun shareFileToWeChat(filePath: String, mimeType: String? = null): Flow<Result<Boolean>>
    
    /**
     * 分享文件到任意应用
     * @param filePath 文件路径
     * @param mimeType 文件MIME类型，如果为null则自动检测
     * @return 流，包含结果为成功/失败或异常
     */
    suspend fun shareFile(filePath: String, mimeType: String? = null): Flow<Result<Boolean>>

    /**
     * 检查微信是否已安装
     * @return 是否已安装微信
     */
    fun isWeChatInstalled(): Boolean
}