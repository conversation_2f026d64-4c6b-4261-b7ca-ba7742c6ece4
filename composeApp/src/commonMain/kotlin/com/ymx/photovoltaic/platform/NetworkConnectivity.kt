package com.ymx.photovoltaic.platform

import io.ktor.network.sockets.Socket

/**
 * 网络连接状态检测接口
 * 
 * 提供跨平台的网络连接状态检测功能，特别是WiFi连接状态检测
 * 该接口在各平台有特定实现
 */
expect object NetworkConnectivity {
    /**
     * 检查设备是否连接到WiFi网络
     * 
     * @return 如果连接到WiFi网络，则返回true；否则返回false
     */
    fun isWifiConnected(): Boolean
    
    /**
     * 尝试将Socket绑定到WiFi网络
     * 
     * 在某些平台上，如Android，这可以确保Socket通过WiFi网络发送数据，
     * 即使设备同时连接了移动数据。在不支持此功能的平台上，此方法不执行任何操作。
     * 
     * @param socket 要绑定的Socket
     * @return 是否成功绑定或不需要绑定
     */
    fun bindSocketToWifi(socket: Socket): Boolean
    
    /**
     * 获取当前WiFi网络信息
     * 
     * @return WiFi网络信息字符串，不同平台可能返回不同格式的信息
     */
    fun getWifiInfo(): String
    
    /**
     * 强制后续网络请求使用WiFi网络
     * 
     * 在某些平台上（如Android），可以设置进程级别的网络偏好，
     * 优先使用WiFi网络进行通信。在不支持此功能的平台上，此方法不执行任何操作。
     * 
     * 注意：此方法的效果可能会受到平台限制和系统版本的影响
     * 
     * @return 是否成功设置网络偏好
     */
    fun forceUseWifiForRequests(): Boolean
    
    /**
     * 解除强制使用WiFi网络的设置
     * 
     * 在调用forceUseWifiForRequests()后，可以使用此方法解除对WiFi网络的绑定。
     * 在不支持此功能的平台上，此方法不执行任何操作。
     * 
     * @return 是否成功解除网络偏好
     */
    fun releaseWifiForRequests(): Boolean
} 