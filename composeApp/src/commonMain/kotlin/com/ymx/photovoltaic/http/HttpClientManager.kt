package com.ymx.photovoltaic.http


import com.ymx.photovoltaic.platform.createPlatformEngine
import io.ktor.client.HttpClient

/**
 * HttpClient管理类
 */
object HttpClientManager {

    // 添加监听器列表
    private val listeners = mutableListOf<() -> Unit>()

    /**
     * 添加服务器变更监听
     */
    fun addServerChangeListener(listener: () -> Unit) {
        listeners.add(listener)
    }

    /**
     * 移除服务器变更监听
     */
    fun removeServerChangeListener(listener: () -> Unit) {
        listeners.remove(listener)
    }

    /**
     * 通知所有监听器服务器已变更
     */
    fun notifyServerChanged() {
        // 清除HttpClient缓存
        httpClientInstance = null
        listeners.forEach { it.invoke() }
//        LogUtil.d("Notified all listeners of server change")
    }

    /** 请求超时时间 */
    private const val TIME_OUT_SECONDS = 10000

    /** HttpClient实例 */
    private var httpClientInstance: HttpClient? = null

    /** 获取HttpClient实例 */
    val client: HttpClient
        get() = httpClientInstance ?: createHttpClient().also { httpClientInstance = it }

    /** 创建HttpClient实例 */
    private fun createHttpClient(): HttpClient {
        // 使用平台特定的引擎创建HttpClient
        return createPlatformEngine()
    }

    /**
     * 获取默认IP
     */
    fun getDefaultIP(): String {
        return IpManager.getDefaultIP()
    }

    /**
     * 清除HttpClient实例
     */
    fun clearClient() {
        httpClientInstance?.close()
        httpClientInstance = null
//        LogUtil.d("Cleared HttpClient instance")
    }
}