package com.ymx.photovoltaic.di


import com.ymx.photovoltaic.viewmodel.AppScreenViewModel
import com.ymx.photovoltaic.viewmodel.EditViewModel
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import com.ymx.photovoltaic.viewmodel.HomeViewModel
import com.ymx.photovoltaic.viewmodel.LanguageViewModel
import com.ymx.photovoltaic.viewmodel.MyViewModel
import com.ymx.photovoltaic.viewmodel.ReportViewModel
import com.ymx.photovoltaic.viewmodel.StationViewModel
import com.ymx.photovoltaic.viewmodel.UserViewModel
import org.koin.dsl.module

val appModule = module {
    // 定义ViewModel
    single { LanguageViewModel() }
    single { AppScreenViewModel() }
    single { EquipmentViewModel() }
    single { HomeViewModel() }
    single { EditViewModel() }
    single { ReportViewModel() }
    single { StationViewModel() }
    single { MyViewModel() }
    single { UserViewModel() }



    // 这里可以添加其他依赖，如Repository、DataSource等
} 