package com.ymx.photovoltaic.navigation

import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.StringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_edit_selected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_edit_unselected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_equipment_selected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_equipment_unselected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_home_selected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_home_unselected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_me_selected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_me_unselected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_message_selected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_message_unselected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_report_selected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_report_unselected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_view_selected
import photovoltaic_kmp_app.composeapp.generated.resources.bottom_view_unselected
import photovoltaic_kmp_app.composeapp.generated.resources.tab_edit
import photovoltaic_kmp_app.composeapp.generated.resources.tab_equipment
import photovoltaic_kmp_app.composeapp.generated.resources.tab_home
import photovoltaic_kmp_app.composeapp.generated.resources.tab_mine
import photovoltaic_kmp_app.composeapp.generated.resources.tab_operation
import photovoltaic_kmp_app.composeapp.generated.resources.tab_report
import photovoltaic_kmp_app.composeapp.generated.resources.tab_view


sealed class NavBarItem(
    val label: StringResource,
    val unSelectedIcon: DrawableResource, val selectedIcon: DrawableResource, val route: String) {
    data object Home : NavBarItem(
        Res.string.tab_home,
        Res.drawable.bottom_home_unselected, Res.drawable.bottom_home_selected, Route.HOME)
    data object Equipment : NavBarItem(
        Res.string.tab_equipment,
        Res.drawable.bottom_equipment_unselected, Res.drawable.bottom_equipment_selected, Route.EQUIPMENT)
    data object Message : NavBarItem(
        Res.string.tab_operation,
        Res.drawable.bottom_message_unselected, Res.drawable.bottom_message_selected, Route.MESSAGE)
    data object My : NavBarItem(
        Res.string.tab_mine,
        Res.drawable.bottom_me_unselected, Res.drawable.bottom_me_selected, Route.MY)
    data object View : NavBarItem(
        Res.string.tab_view,
        Res.drawable.bottom_view_unselected, Res.drawable.bottom_view_selected, Route.VIEW)
    data object Report : NavBarItem(
        Res.string.tab_report,
        Res.drawable.bottom_report_unselected, Res.drawable.bottom_report_selected, Route.REPORT)
    data object Edit : NavBarItem(
        Res.string.tab_edit,
        Res.drawable.bottom_edit_unselected, Res.drawable.bottom_edit_selected, Route.EDIT)
}