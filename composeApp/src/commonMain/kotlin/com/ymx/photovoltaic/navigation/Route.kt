package com.ymx.photovoltaic.navigation

object Route {
    const val SPLASH = "splash"
    const val HOME = "home"
    const val MESSAGE = "message"
    const val MESSAGE_DETAIL = "message_detail/{id}/{type}/{content}/{time}/{isRead}"
    const val EQUIPMENT = "equipment"
    const val MY = "my"
    const val SETTING = "setting"
    const val LOGIN = "login"
    const val REGISTER = "register"
    const val ACCOUNT_TYPE = "account_type"
    const val FEEDBACK = "feedback"
    const val CHANGE_PASSWORD = "change_password"
    const val EDIT = "edit"
    const val REPORT = "report"
    const val VIEW = "view"
    const val KEEP_WARN = "keep_warn"
    const val TEMP_WARN = "temp_warn"
    const val STATION_INFO = "station_info"
    const val OWNER_INFO = "owner_info"
    const val SET_WIFI = "set_wifi"
    const val CONFIG_LAYOUT = "config_layout"
    const val STATION_NEW = "station_new"
    const val COLLECTOR = "collector"
    const val COLLECTOR_NEW = "collector_new"
    const val COLLECTOR_EDIT = "collector_edit"
    const val GROUP = "group"
    const val GROUP_NEW = "group_new"
    const val GROUP_EDIT = "group_edit"
    const val OPTIMIZER = "optimizer"
    const val OPTIMIZER_EDIT = "optimizer_edit"
    const val OPTIMIZER_NEW = "optimizer_new"
    const val SCAN_PAGE = "scan_page"
    const val VERIFY_SCAN = "verify_scan"
    const val TEST_SCAN = "test_scan"
    const val RELAY = "relay"
    const val RELAY_NEW = "relay_new/{imei}"
    const val RELAY_EDIT = "relay_edit"
    const val RELAY_DETAIL = "relay_detail"
    const val OPTIMIZER_FOR_RELAY = "optimizer_for_relay"
    const val RELAY_OPTIMIZER_SELECT = "relay_optimizer_select"
    /** 忘记密码页面路由 */
    const val FORGOT_PASSWORD = "forgot_password"
    const val WEB_VIEW = "web_view?title={title}&url={url}"
    const val SET_COLLECTOR = "set_collector"
    const val SET_RELAY = "set_relay/{imei}"
    const val MULTI_SELECT = "multi_select/{title}/{flag}"
    const val SAMPLING_TEST = "sampling_test"
    const val OWNER_ONE = "owner_one"
    const val DEVICE_CONFIG = "device_config"
    const val RELAY_CONFIG = "relay_config"
    const val GROUP_CONFIG = "group_config"
    const val COLLECTOR_CONFIG = "collector_config"
    const val OPTIMIZER_CONFIG = "optimizer_config"
    const val SCAN = "scan"

}