package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.ui.page.theme.Grey_C4
import org.jetbrains.compose.resources.StringResource
import org.jetbrains.compose.resources.stringResource

@Composable
fun PlaceholderText(textResId: StringResource,
                    textColor: Color= Grey_C4,
                    textFontSize: TextUnit=14.sp,
                    textLineHeight:TextUnit=14.sp,
                    textAlign:TextAlign=TextAlign.Center,
                    modifier: Modifier=Modifier
                    ) {
    Text(
        modifier = modifier,
        text = stringResource(textResId),
        color = textColor,
        fontSize = textFontSize,
        lineHeight = textLineHeight,
        overflow = TextOverflow.Visible,
        textAlign = textAlign,
    )
}

