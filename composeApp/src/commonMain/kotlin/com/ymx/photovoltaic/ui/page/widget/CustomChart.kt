package com.ymx.photovoltaic.ui.page.widget


import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.multiplatform.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.multiplatform.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.multiplatform.cartesian.axis.VerticalAxis
import com.patrykandpatrick.vico.multiplatform.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.multiplatform.cartesian.data.CartesianValueFormatter
import com.patrykandpatrick.vico.multiplatform.cartesian.data.columnSeries
import com.patrykandpatrick.vico.multiplatform.cartesian.data.lineSeries
import com.patrykandpatrick.vico.multiplatform.cartesian.layer.ColumnCartesianLayer
import com.patrykandpatrick.vico.multiplatform.cartesian.layer.LineCartesianLayer
import com.patrykandpatrick.vico.multiplatform.cartesian.layer.rememberColumnCartesianLayer
import com.patrykandpatrick.vico.multiplatform.cartesian.layer.rememberLineCartesianLayer
import com.patrykandpatrick.vico.multiplatform.cartesian.rememberCartesianChart
import com.patrykandpatrick.vico.multiplatform.cartesian.rememberVicoScrollState
import com.patrykandpatrick.vico.multiplatform.common.component.rememberLineComponent
import com.patrykandpatrick.vico.multiplatform.common.data.ExtraStore
import com.patrykandpatrick.vico.multiplatform.common.fill
import com.patrykandpatrick.vico.multiplatform.common.shape.CorneredShape
import com.ymx.photovoltaic.data.bean.TendDayInfo
import com.ymx.photovoltaic.data.bean.TendMinuteInfo
import com.ymx.photovoltaic.ui.page.theme.ChartGradient
import com.ymx.photovoltaic.ui.page.theme.Report_Line_Color
import com.ymx.photovoltaic.util.DateTimeUtil
import kotlinx.coroutines.delay
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.all
import photovoltaic_kmp_app.composeapp.generated.resources.month
import photovoltaic_kmp_app.composeapp.generated.resources.year


@Composable
fun HandleColumnData(tendData: TendDayInfo?,selectStr:String)
{
    val modelProducer = remember { CartesianChartModelProducer() }
    val xMonthColumnList by remember { mutableStateOf(mutableListOf(0)) }
    val yMonthColumnList by remember { mutableStateOf(mutableListOf(0f)) }

    val xYearColumnList by remember { mutableStateOf(mutableListOf(0)) }
    val yYearColumnList by remember { mutableStateOf(mutableListOf(0f)) }

    var xAllColumnList by remember { mutableStateOf(mutableListOf(0)) }
    var yAllColumnList by remember { mutableStateOf(mutableListOf(0f)) }
    val options = listOf(
        stringResource(Res.string.month),stringResource(Res.string.year),
        stringResource(Res.string.all))

    LaunchedEffect(selectStr)
    {
        delay(300)
        when(selectStr)
        {
            options[0] ->
            {
                modelProducer.runTransaction {
                    columnSeries { series(x = xMonthColumnList, y = yMonthColumnList) }
                }

            }
            options[1] ->
            {
                modelProducer.runTransaction {
                    columnSeries { series(x = xYearColumnList, y = yYearColumnList) }
                }
            }
            options[2] ->
            {
                modelProducer.runTransaction {
                    columnSeries { series(x = xAllColumnList, y = yAllColumnList) }
                }
            }
        }
    }


    LaunchedEffect(tendData)
    {
        when(selectStr)
        {
            options[0] ->
            {

                tendData?.let {
                    xMonthColumnList.clear()
                    yMonthColumnList.clear()
                    if(it.maxDay!=0)
                    {

                    val kwhList= it.data.map { one ->one.kwh }
                    val timeList=it.data.map { one ->one.createTimeCh.toInt() }

                        for (i in 1..<it.maxDay+1)
                    {
                        xMonthColumnList.add(i)
                        if(!timeList.contains(i))
                        {
                            yMonthColumnList.add(0f)
                        }
                        else
                        {
                            val yIndex=timeList.indexOf(i)
                            yMonthColumnList.add(kwhList[yIndex])
                        }
                    }
                        if(xMonthColumnList.size==1)
                        {
                            xMonthColumnList.add(xMonthColumnList[0]+1)
                            yMonthColumnList.add(0f)
                            xMonthColumnList.add(xMonthColumnList[0]+1)
                            yMonthColumnList.add(0f)
                            xMonthColumnList.add(xMonthColumnList[0]+1)
                            yMonthColumnList.add(0f)
                        } else {

                        }


                    }
                    else
                    {
                        xMonthColumnList.add(0)
                        yMonthColumnList.add(0f)
                    }
                }
                delay(300)
                modelProducer.runTransaction {
                    columnSeries { series(x = xMonthColumnList, y = yMonthColumnList) }
                }
            }
            options[1] ->
            {
                tendData?.let {
                    xYearColumnList.clear()
                    yYearColumnList.clear()
                        if(it.maxDay!=0)
                        {
                            val kwhList= it.data.map { one ->one.kwh }
                            val timeList=it.data.map { one ->one.createTimeCh.toInt() }

                            for (i in 1..<it.maxDay+1)
                            {
                                xYearColumnList.add(i)
                                if(!timeList.contains(i))
                                {
                                    yYearColumnList.add(0f)
                                }
                                else
                                {
                                    val yIndex=timeList.indexOf(i)
                                    yYearColumnList.add(kwhList[yIndex])
                                }
                            }
                            if(xYearColumnList.size==1)
                            {
                                xYearColumnList.add(xYearColumnList[0]+1)
                                yYearColumnList.add(0f)
                                xYearColumnList.add(xYearColumnList[0]+1)
                                yYearColumnList.add(0f)
                                xYearColumnList.add(xYearColumnList[0]+1)
                                yYearColumnList.add(0f)
                            }
                            else
                            {

                            }
                        }
                    else
                    {
                        xYearColumnList.add(0)
                        yYearColumnList.add(0f)
                    }

                }
                modelProducer.runTransaction {
                    columnSeries { series(x = xYearColumnList, y = yYearColumnList) }
                }
            }
            options[2] ->
            {
                tendData?.let {
                    xAllColumnList.clear()
                    yAllColumnList.clear()
                    if(it.maxDay!=0)
                    {
                        xAllColumnList=it.data.map { one ->one.createTimeCh.toInt() }.toMutableList()
                        yAllColumnList= it.data.map { one ->one.kwh }.toMutableList()
                        if(xAllColumnList.size==1)
                        {
                            xAllColumnList.add(xAllColumnList[0]-1)
                            yAllColumnList.add(0f)
                            xAllColumnList.add(xAllColumnList[0]-2)
                            yAllColumnList.add(0f)
                        }
                        else {}
                    }
                    else
                    {
                        xAllColumnList.add(0)
                        yAllColumnList.add(0f)
                    }

                }
            }
        }
    }

    CustomColumnChart(modelProducer)
}



@Composable
fun CustomColumnChart(modelProducer:CartesianChartModelProducer)
{
    CartesianChartHost(
        // 初始化图表
        rememberCartesianChart(
            // 初始化图表图层 x轴 y轴
            rememberColumnCartesianLayer(
                ColumnCartesianLayer.ColumnProvider.series(
                    rememberLineComponent(
                       fill= fill(ChartGradient),
                        thickness = 2.dp,
                        shape = CorneredShape.rounded(allPercent = 30),
                    )
                )
            ),
            startAxis = VerticalAxis.rememberStart(
                title = "kwh",
            ),
            // 属性值有很多，可以分别设置，每个属性值都可能是一个对象,也可能是一个接口，可以实现接口进行自定义
            bottomAxis = HorizontalAxis.rememberBottom(
                guideline = null,
                itemPlacer = remember { HorizontalAxis.ItemPlacer.aligned(spacing = { _ -> 1 })},


            ),
        ),
        modelProducer,
        scrollState = rememberVicoScrollState(scrollEnabled = false),
        modifier = Modifier.fillMaxHeight(1f)

        )
}


fun getXStep(totalNum: Double, tendData: Any? = null): Double {
    val isToday = when (tendData) {
        is TendDayInfo -> {
            if (tendData.data.isNotEmpty()) {
                isToday(tendData.data.first().createTimeCh)
            } else true
        }
        is TendMinuteInfo -> {
            if (tendData.data.isNotEmpty()) {
                isToday(tendData.data.first().createTimeCh)
            } else true
        }
        else -> true // 默认为今天
    }
    
    return if (isToday) {
        val currentHour = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).hour
        if(currentHour <= 5) totalNum
        else if(currentHour<9) totalNum/(currentHour-5)
        else totalNum/4
    } else {
        totalNum/4
    }
}

private fun isToday(createTimeCh: String): Boolean {
    val today = DateTimeUtil.formatDate(DateTimeUtil.now())
    return createTimeCh.startsWith(today)
}

fun formatTime(timeString: String?): String {
    // 确保输入的字符串长度为4
    if (timeString==null||timeString.length != 4) {
        return ".."
    }

    // 获取小时和分钟部分
    val hour = timeString.substring(0, 2).toIntOrNull() ?: return ".."
    val minute = timeString.substring(2, 4).toIntOrNull() ?: return ".."
    
    // 显示整点时间
    val displayHour = hour
    
    // 格式化为 HH:00，使用字符串拼接
    val formattedHour = (displayHour % 24).toString().padStart(2, '0')
    val currentHour = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).hour
    if(currentHour<=6)
    {
        if(minute>30&&displayHour<=6) return "${formattedHour}:30"
        else return "${formattedHour}:00"
    }
    return "${formattedHour}:00"

}
@Composable
fun CustomLineChart(tendMinuteLiveData: TendMinuteInfo?)
{

    var xx=1.00
    val unitKey = remember{ ExtraStore.Key<MutableList<String>>() }

    val lineModelProducer = remember { CartesianChartModelProducer() }
    var xLineTimeList by remember { mutableStateOf(mutableListOf<String>())  }

    var xLineList by remember { mutableStateOf(mutableListOf(1))  }
    var yLineList by remember { mutableStateOf(mutableListOf(0f))  }

    LaunchedEffect(tendMinuteLiveData)
    {

        tendMinuteLiveData?.let {
            xLineList.clear()
            yLineList.clear()
            xLineTimeList.clear()
            if(it.outputCurrentMax!=0f)
            {
                yLineList= it.data.map { one ->one.power }.toMutableList()
                xLineList= (0..<yLineList.size).toMutableList()

                xLineTimeList= it.data.map{one->one.batchNo.substring(8,12)}.toMutableList()
            }
            else
            {
                xLineList.add(0)
                yLineList.add(0f)
                xLineTimeList.add("0")
            }


            delay(300)

            lineModelProducer.runTransaction {
                lineSeries { series(x = xLineList, y = yLineList) }
                extras {
                        extraStore->
                    extraStore[unitKey] = xLineTimeList
                }
            }
        }

    }
       val labelValueFormatter =
            remember {
                CartesianValueFormatter{x, value,_ ->
                    val extraStore= x.model.extraStore.getOrNull(unitKey)
                    if(extraStore!=null&&extraStore.size>0&&value.toInt()<extraStore.size&&value.toInt()>=0)
                    {
                        formatTime(extraStore[value.toInt()])
                    }
                    else
                    {
                      ".."
                    }
                }

    }

   // 总的点数  要显示几个点 然后设置步长 然后设置数值  变换600的值
    CartesianChartHost(
        // 初始化图表
        rememberCartesianChart(
            // 初始化图表图层 x轴 y轴
            rememberLineCartesianLayer(
                LineCartesianLayer.LineProvider.series(
                    LineCartesianLayer.Line(
                        fill=   LineCartesianLayer.LineFill.
                        single(fill(Report_Line_Color)),
                        areaFill =LineCartesianLayer.AreaFill.single(
                            fill(
                                Brush.verticalGradient(
                                    listOf(
                                        Report_Line_Color.copy(alpha = 0.4f),
                                        Color.Transparent
                                    )
                                )
                            ))
                    )
                ),

                ),
            startAxis = VerticalAxis.rememberStart(),
            bottomAxis = HorizontalAxis.rememberBottom(
                guideline = null,
                itemPlacer = remember { HorizontalAxis.ItemPlacer.aligned(spacing = { _ -> 1 })},
                valueFormatter = labelValueFormatter

            ),

            getXStep ={
                chatModel ->
                if(chatModel.models[0].maxX!=0.0)
                {
                    xx= getXStep(chatModel.models[0].maxX, tendMinuteLiveData)
                }
                xx
            }
        ),
        lineModelProducer,
//        zoomState = rememberVicoZoomState(zoomEnabled = true, maxZoom = Zoom.max()),
        scrollState = rememberVicoScrollState(scrollEnabled = false),

        placeholder = { LoadingDialog(loadingText = "加载中..."){}
        },
        modifier = Modifier.fillMaxHeight(1f)

    )
}