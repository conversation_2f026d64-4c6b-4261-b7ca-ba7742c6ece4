package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.DropdownMenu
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.PopupProperties
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.title_bar_more

/**
 * 带菜单的顶部栏组件
 * 
 * @param textStr 标题文本
 * @param backClick 返回按钮点击事件
 * @param isBackIcon 是否显示返回图标
 * @param topContainerColor 顶部栏背景颜色
 * @param showMenuButton 是否显示菜单按钮
 * @param menuItems 菜单项列表，每个菜单项包含文本和点击事件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MenuTopBar(
    textStr: String,
    backClick: () -> Unit,
    isBackIcon: Boolean = true,
    topContainerColor: Color = Color.White,
    showMenuButton: Boolean = false,
    menuItems: List<MenuItem> = emptyList()
) {
    var showMenu by remember { mutableStateOf(false) }
    
    CenterAlignedTopAppBar(
        title = {
            Text(
                text = textStr,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )
        },
        navigationIcon = {
            if (isBackIcon) {
                IconButton(onClick = { backClick() }) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.Black
                    )
                }
            }
        },
        actions = {
            if (showMenuButton && menuItems.isNotEmpty()) {
                Box(modifier = Modifier.wrapContentSize(Alignment.TopEnd)) {
                    IconButton(onClick = { showMenu = true }) {
                        Icon(
                            painter = painterResource(Res.drawable.title_bar_more),
                            contentDescription = "More Options",
                            tint = Color.Black
                        )
                    }
                    
                    CustomDropdownMenu(
                        expanded = showMenu,
                        onDismissRequest = { showMenu = false },
                        menuItems = menuItems
                    )
                }
            }
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
            containerColor = topContainerColor
        )
    )
}

/**
 * 菜单项数据类
 * 
 * @param text 菜单项文本
 * @param onClick 菜单项点击事件
 * @param textColor 菜单项文本颜色，默认为黑色
 */
data class MenuItem(
    val text: String,
    val onClick: () -> Unit,
    val textColor: Color = Color.Black
)

/**
 * 自定义下拉菜单组件
 * 
 * @param expanded 是否展开菜单
 * @param onDismissRequest 菜单关闭请求
 * @param menuItems 菜单项列表
 */
@Composable
private fun CustomDropdownMenu(
    expanded: Boolean,
    onDismissRequest: () -> Unit,
    menuItems: List<MenuItem>
) {
    DropdownMenu(
        expanded = expanded,
        onDismissRequest = onDismissRequest,
        modifier = Modifier
            .width(150.dp)
            .padding(end = 12.dp)
            .background(Color.White)
            .clip(RoundedCornerShape(26.dp)),
        properties = PopupProperties(focusable = true)
    ) {
        Column(
            modifier = Modifier
                .background(Color.White)
                .clip(RoundedCornerShape(26.dp))
        ) {
            menuItems.forEachIndexed { index, item ->
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            item.onClick()
                            onDismissRequest()
                        }
                        .padding(vertical = 12.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = item.text,
                        fontSize = 16.sp,
                        color = item.textColor,
                        textAlign = TextAlign.Center
                    )
                }
                
                if (index < menuItems.size - 1) {
                    Divider(
                        color = Color.LightGray,
                        thickness = 0.5.dp,
                        modifier = Modifier.padding(horizontal = 8.dp)
                    )
                }
            }
        }
    }
}
