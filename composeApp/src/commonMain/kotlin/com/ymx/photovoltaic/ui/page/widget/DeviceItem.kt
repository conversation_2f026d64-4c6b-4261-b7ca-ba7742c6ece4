package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconToggleButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.ui.page.theme.Blue_F9
import com.ymx.photovoltaic.ui.page.theme.LjRed
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.gray_unselected

@Composable
fun DeviceTextButtonRow(
    textStr: String,
    buttonText: String="",
    onButtonClick: () -> Unit={},
    isHaveButton:Boolean=true,
    isTextBold:Boolean=true,
    buttonBorderColor:Color= Blue_F9
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically, // 将项目对齐到此行的顶部
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        if(isTextBold)
        {
            Text(
                text = textStr,
                style = TextStyle(fontSize = 17.sp, fontWeight = FontWeight.Bold), // 黑体字
                modifier = Modifier.weight(1f).padding(end = 8.dp)
            )
        }
        else
        {
            Text(
                text = textStr,
                style = TextStyle(fontSize = 13.sp, color = Color.Gray),
                modifier = Modifier.weight(1f).padding(end = 8.dp)
            )
        }

        if(isHaveButton)
        {
            Button( // Device Config Button
                onClick = onButtonClick,
                shape = RoundedCornerShape(20.dp), // 圆形按钮
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.White,
                ),
                border = BorderStroke(1.dp, buttonBorderColor), // 蓝色线框
                contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp), // 字体和边框有间距，线框按钮
                modifier = Modifier.height(30.dp) //
            ) {
                Text(text = buttonText, fontSize = 13.sp, color = buttonBorderColor)
            }
        }

    }
}

@Composable
fun DeviceTwoTextRow(
    firstText: String,
    secondText: String,
    isTextBold:Boolean=false,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        if(isTextBold)
        {
            Text(
                text = firstText,
                style = TextStyle(fontSize = 17.sp, fontWeight = FontWeight.Bold), // 黑体字
                modifier = Modifier.weight(1f).padding(end = 8.dp)
            )
        }
        else
        {
            Text(
                text = firstText,
                style = TextStyle(fontSize = 13.sp, color = Color.Gray),
                modifier = Modifier.weight(1f).padding(end = 8.dp)
            )
        }

        Text(
            text = secondText,
            style = TextStyle(fontSize = 13.sp, color = Color.Gray)
        )
    }
}

@Composable
fun CheckboxToggleButton(
    isChecked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    IconToggleButton(
        checked = isChecked,
        onCheckedChange = onCheckedChange,
        modifier = modifier // Ensure vertical centering
    ) {
        Icon(
            painter =  painterResource(Res.drawable.gray_unselected),
            contentDescription =  "selected",
            tint = if (isChecked)  LjRed else Color.Gray, // Example colors
            modifier = Modifier.size(22.dp)

        )
    }
}

@Composable
fun DeviceItem(
    imagePainter: Painter,
    secondRowText: String,
    modifier: Modifier = Modifier.fillMaxWidth().padding(horizontal = 20.dp, vertical = 15.dp),
    showCheckbox: Boolean = false,
    isChecked: Boolean = false,
    onCheckedChange: (Boolean) -> Unit = {},
    firstRow: (@Composable () -> Unit)? = null,
    lastRow: (@Composable () -> Unit)? = null
) {
    Card(
        colors = CardDefaults.cardColors(containerColor = Color.White),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp, horizontal = 10.dp), // Added horizontal padding
        shape = RoundedCornerShape(16.dp), // Increased corner radius
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp) // Subtle elevation
    ) {
        Row(
            modifier = modifier,
            verticalAlignment = Alignment.CenterVertically // Align items vertically in the center
        ) {
            // Add Checkbox conditionally
            if (showCheckbox) {
                CheckboxToggleButton(
                    isChecked = isChecked,
                    onCheckedChange = onCheckedChange,
                     modifier= Modifier.align(Alignment.CenterVertically)
                )
            }

            // 左侧图片
            Image(
                painter = imagePainter,
                contentDescription = "image", // Use title for accessibility
                modifier = Modifier.height(70.dp).width(55.dp), // Padding inside the image card
                contentScale = ContentScale.Fit // Fit the image within bounds
            )

            Spacer(modifier = Modifier.width(16.dp))

            // 右侧的列
            Column(
                modifier = Modifier.weight(1f) // 占用剩余空间
            ) {
                // 第一行: 标题 和 按钮 - 调用提取的函数

                if(firstRow!=null)
                {
                    firstRow()
                }

                Spacer(modifier = Modifier.height(6.dp))

                // 第二行
                Text(
                    text = secondRowText,
                    style = TextStyle(fontSize = 13.sp, color = Color.Gray)
                )

                Spacer(modifier = Modifier.height(4.dp))

                // 最后一行: 两个text排列在两端 - 调用提取的函数

                if(lastRow!=null)
                {
                    lastRow()
                }
            }
        }
    }
}