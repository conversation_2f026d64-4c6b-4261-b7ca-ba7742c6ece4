package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.gestures.snapping.rememberSnapFlingBehavior
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.ui.page.theme.LjRed

@Composable
fun WePicker(
    visible: Boolean,
    ranges: Array<List<String>>,
    values: Array<Int>,
    title: String? = null,
    onCancel: () -> Unit,
    onColumnValueChange: ((column: Int, value: Int, values: Array<Int>) -> Unit)? = null,
    onValuesChange: (Array<Int>) -> Unit
) {
    val localValues = remember(visible) { values.copyOf() }

    WePopup(
        visible,
        title = title,
        enterTransition = fadeIn(tween(150)) + slideInVertically(tween(150)) { it / 3 },
        exitTransition = fadeOut(tween(150)) + slideOutVertically(tween(150)) { it / 3 },
        draggable = false,
        onClose = onCancel
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally) {
            Box(
                modifier = Modifier
                    .height(200.dp)
                    .drawIndicator(
                        Color.LightGray
                    )
            ) {
                // 可选列表
                Row(verticalAlignment = Alignment.CenterVertically) {
                    ranges.forEachIndexed { index, options ->
                        ColumnItem(
                            options = options,
                            index = localValues[index]
                        ) {
                            localValues[index] = it
                            onColumnValueChange?.invoke(index, it, localValues.copyOf())
                        }
                    }
                }
            }

            HorizontalDivider(thickness = 0.5.dp, color = Color.LightGray)

            BottomActionButtons(onCancel)
            {
                onValuesChange(localValues)
                onCancel()
            }
        }
    }
}

@Composable
private fun BottomActionButtons(
    onCancel: () -> Unit,
    onConfirm: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(IntrinsicSize.Min),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 取消按钮
        TextButton(
            onClick = onCancel,
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = "取消",
                style = TextStyle(
                    fontSize = 18.sp,
                    color = Color.Black,
                    fontWeight = FontWeight.Bold
                )
            )
        }

        VerticalDivider(
            color = Color.LightGray,
            thickness = 0.5.dp,
            modifier = Modifier.fillMaxHeight()
                .width(1.dp)
        )

        // 确认按钮
        TextButton(
            onClick = onConfirm,
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = "确定",
                style = TextStyle(
                    fontSize = 18.sp,
                    color = LjRed,
                    fontWeight = FontWeight.Bold
                )
            )
        }
    }
}

@Composable
private fun RowScope.ColumnItem(
    options: List<String>,
    index: Int,
    onChange: (Int) -> Unit
) {
    val itemHeight = 56.dp
    val verticalPadding = remember { (200.dp - itemHeight) / 2 }
    val listState = rememberLazyListState(index)

    LaunchedEffect(listState) {
        snapshotFlow { listState.firstVisibleItemIndex }
            .collect {
                onChange(it)
            }
    }

    LazyColumn(
        state = listState,
        modifier = Modifier.weight(1f),
        contentPadding = PaddingValues(vertical = verticalPadding),
        horizontalAlignment = Alignment.CenterHorizontally,
        flingBehavior = rememberSnapFlingBehavior(listState)
    ) {
        items(options) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(itemHeight),
                contentAlignment = Alignment.Center
            ) {
                Text(text = it, color = Color.Black, fontSize = 17.sp)
            }
        }
    }
}

@Composable
private fun Modifier.drawIndicator(color: Color) = this.drawBehind {
    val topY = size.height / 2 - 56.dp.toPx() / 2
    val bottomY = size.height / 2 + 56.dp.toPx() / 2
    
    // 绘制上边线
    drawLine(
        color = color,
        start = Offset(30.dp.toPx(), topY),
        end = Offset(size.width-30.dp.toPx(), topY),
        strokeWidth = 1.dp.toPx()
    )
    
    // 绘制下边线
    drawLine(
        color = color,
        start = Offset(30.dp.toPx(), bottomY),
        end = Offset(size.width-30.dp.toPx(), bottomY),
        strokeWidth = 1.dp.toPx()
    )
}
