package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.FabPosition
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.bean.Collector
import com.ymx.photovoltaic.data.bean.Group
import com.ymx.photovoltaic.data.bean.Optimizer
import com.ymx.photovoltaic.data.bean.Relay
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.theme.Grey_E8
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.DeviceItem
import com.ymx.photovoltaic.ui.page.widget.DeviceTextButtonRow
import com.ymx.photovoltaic.ui.page.widget.DeviceTwoTextRow
import com.ymx.photovoltaic.ui.page.widget.MenuItem
import com.ymx.photovoltaic.ui.page.widget.MenuTopBar
import com.ymx.photovoltaic.ui.page.widget.SimpleDialog
import com.ymx.photovoltaic.ui.page.widget.StationItem
import com.ymx.photovoltaic.ui.page.widget.rememberSingleColumnPickerState
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.Json
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.add_device
import photovoltaic_kmp_app.composeapp.generated.resources.belongs_to_station
import photovoltaic_kmp_app.composeapp.generated.resources.collector
import photovoltaic_kmp_app.composeapp.generated.resources.device_config
import photovoltaic_kmp_app.composeapp.generated.resources.device_config_button
import photovoltaic_kmp_app.composeapp.generated.resources.device_count
import photovoltaic_kmp_app.composeapp.generated.resources.group
import photovoltaic_kmp_app.composeapp.generated.resources.group_count
import photovoltaic_kmp_app.composeapp.generated.resources.imei_number
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer_count_text
import photovoltaic_kmp_app.composeapp.generated.resources.relay
import photovoltaic_kmp_app.composeapp.generated.resources.smart_gateway
import photovoltaic_kmp_app.composeapp.generated.resources.smart_optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.station1
import photovoltaic_kmp_app.composeapp.generated.resources.station2
import photovoltaic_kmp_app.composeapp.generated.resources.station3
import photovoltaic_kmp_app.composeapp.generated.resources.station4
import photovoltaic_kmp_app.composeapp.generated.resources.station_add
import photovoltaic_kmp_app.composeapp.generated.resources.station_group

/**
 * 设备配置屏幕，显示电站信息和关联的设备列表。
 *
 * 注意：导航到此屏幕时，需要传递相应的电站参数。
 */
@Composable
fun DeviceConfigScreen(
    navHostController: NavHostController,
    stationId: String,
    systemName: String,
    address: String,
    power: Int,
    createTime: String,
    daysSinceTime: Long,
    status: String,
    index: Int,
    equipmentViewModel: EquipmentViewModel = getKoin().get()
) {

    val stationDrawables = listOf(
        Res.drawable.station1,
        Res.drawable.station2,
        Res.drawable.station3,
        Res.drawable.station4
    )

    val collectorList by equipmentViewModel.collectorListFlow.collectAsState()
    val imei = collectorList.firstOrNull()?.imei ?: ""
    val collectorId=collectorList.firstOrNull()?.id ?: ""

    val relayList by equipmentViewModel.relayListFlow.collectAsState()
    val relayIdStr= if(relayList.size>2) relayList[0].relayId+"、"+relayList[1].relayId+"、..."
    else if(relayList.size==2) relayList[0].relayId+"、"+relayList[1].relayId
    else if(relayList.size==1) relayList[0].relayId
    else ""

    val groupList by equipmentViewModel.groupListFlow.collectAsState()

    val optimizerList by equipmentViewModel.optimizerListFlow.collectAsState()

    val optimizerIdStr= if(optimizerList.size>2) optimizerList[0].chipId+"/"+optimizerList[1].chipId+",..."
    else if(optimizerList.size==2) optimizerList[0].chipId+"/"+optimizerList[1].chipId
    else if(optimizerList.size==1) optimizerList[0].chipId
    else ""

    // 添加提示对话框状态
    var showGatewayRequiredDialog by remember { mutableStateOf(false) }
    
    // 添加设备类型选项列表
    val deviceTypeOptions = if (collectorId.isEmpty()) {
        listOf("智能网关")
    } else {
        listOf(
            "中继器",
            // "优化器",
            "组串"
        )
    }
    
    val picker = rememberSingleColumnPickerState()

    LaunchedEffect(Unit) {
        equipmentViewModel.fetchCollectorList(stationId)
        equipmentViewModel.fetchRelayList(AppGlobal.mId, "view", stationId)

        equipmentViewModel.fetchOptimizerList(
            powerStationId = stationId ,
            belongsGroupId = "",
            belongsGroupFlag = null,
            pageSize = 200
        )
    }
    
    // 当collectorId变化时立即执行
    LaunchedEffect(collectorId) {
        if(collectorId != "") {
            equipmentViewModel.fetchGroupList(collectorId)
        } else {
            equipmentViewModel.clearGroupList()
        }
    }


    Scaffold(
        topBar = {
            MenuTopBar(
                textStr = stringResource(Res.string.device_config),
                backClick = { navHostController.popBackStack() },
                showMenuButton = false,
                menuItems = listOf(
                    MenuItem("编辑电站", { /* edit action */ }),
                    MenuItem("分享电站", { /* share action */ }),
                    MenuItem("删除电站", { /* delete action */ }, Color.Red)
                )
            )
        },
        containerColor = Grey_F5,
        floatingActionButtonPosition = FabPosition.Center,
        floatingActionButton = {
            FloatingActionButton(
                onClick = {
                        // 显示操作选择器
                    picker.show(
                        title = "选择新增类型",
                        range = deviceTypeOptions,
                        value = 0
                    ) { selected ->
                        // 根据选择的设备类型执行对应操作
                        val option = deviceTypeOptions[selected]
                        when (option) {
                            "智能网关" -> {
                                // 导航到智能网关配置页面
                                navHostController.navigate(Route.COLLECTOR_NEW)
                            }
                            "中继器" -> {
                                // 导航到中继器添加页面并传递imei参数
                                navHostController.navigate(Route.RELAY_NEW+ "/"+imei)
                            }
                            "组串" -> {
                                // 导航到组串添加页面
                                navHostController.navigate(Route.GROUP_NEW+"/"+collectorId+"/"+imei)
                            }
                        }
                    }
                },
                containerColor = Color.Transparent,
                contentColor = Color.Transparent,
                elevation = FloatingActionButtonDefaults.elevation(0.dp, 0.dp, 0.dp, 0.dp),
            ) {
                Image(
                    painter = painterResource(Res.drawable.station_add),
                    modifier = Modifier.size(60.dp),
                    contentDescription = stringResource(Res.string.add_device)
                )
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState()) // Make content scrollable
        ) {
            val drawableRes = stationDrawables[index % stationDrawables.size]

            // 带有圆角背景的容器，仅包含到电站组串为止的内容
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp, vertical = 12.dp)
                    .background(
                        color = Grey_E8,
                        shape = RoundedCornerShape(16.dp)
                    )
                    .padding(top = 12.dp, bottom = 20.dp)
            ) {
                // 电站信息
                StationItem(
                    img = drawableRes,
                    name = systemName,
                    address = address,
                    capacity = power,
                    createTime = createTime,
                    days = daysSinceTime,
                    status = status,
                    onStationClick = { /* TODO: Handle station click */ },
                    cardColor = Color.Transparent
                )

                Spacer(modifier = Modifier.height(2.dp)) // Small spacer

                // 智能网关
                DeviceItem(
                    imagePainter = painterResource(Res.drawable.collector),
                    secondRowText = stringResource(Res.string.belongs_to_station).replace("{station}", systemName),
                    firstRow = {
                        DeviceTextButtonRow(
                            textStr = stringResource(Res.string.smart_gateway),
                            buttonText = stringResource(Res.string.device_config_button),
                            onButtonClick = {

                                if (collectorId.isEmpty()) {
                                    showGatewayRequiredDialog = true
                                }
                                else{
                                    // 将智能网关列表序列化为JSON字符串
                                    val collectorListJson = Json.encodeToString(ListSerializer(Collector.serializer()), collectorList)

                                    // 使用savedStateHandle传递数据
                                    navHostController.currentBackStackEntry?.savedStateHandle?.set("collectorList", collectorListJson)

                                    // 导航到智能网关配置页面
                                    navHostController.navigate(Route.COLLECTOR_CONFIG)
                                }
                            }
                        )
                    },
                    lastRow = { DeviceTwoTextRow(
                        firstText = stringResource(Res.string.imei_number).replace("{imei}", imei),
                        secondText = stringResource(Res.string.device_count).replace("{count}", collectorList.size.toString())
                    )
                    },
                )

                // 中继器
                DeviceItem(
                    imagePainter = painterResource(Res.drawable.relay),
                    secondRowText = stringResource(Res.string.belongs_to_station).replace("{station}", systemName),
                    firstRow = {
                        DeviceTextButtonRow(
                            textStr = stringResource(Res.string.relay),
                            buttonText = stringResource(Res.string.device_config_button),
                            onButtonClick = {
                                if (collectorId.isEmpty()) {
                                    showGatewayRequiredDialog = true
                                } else {
                                    // 将中继器列表序列化为JSON字符串
                                    val relayListJson = Json.encodeToString(ListSerializer(Relay.serializer()), relayList)
                                    
                                    // 使用savedStateHandle传递数据
                                    navHostController.currentBackStackEntry?.savedStateHandle?.set("relayList", relayListJson)
                                    
                                    // 传递imei参数
                                    navHostController.currentBackStackEntry?.savedStateHandle?.set("imei", imei)
                                    
                                    // 导航到中继器配置页面
                                    navHostController.navigate(Route.RELAY_CONFIG)
                                }
                            }
                        )
                    },
                    lastRow = { DeviceTwoTextRow(
                        firstText = stringResource(Res.string.imei_number).replace("{imei}", relayIdStr),
                        secondText = stringResource(Res.string.device_count).replace("{count}", relayList.size.toString())
                    )
                    },
                )

                // 智能优化器
                DeviceItem(
                    imagePainter = painterResource(Res.drawable.optimizer),
                    secondRowText = stringResource(Res.string.belongs_to_station).replace("{station}", systemName),
                    firstRow = {
                        DeviceTextButtonRow(
                            textStr = stringResource(Res.string.smart_optimizer),
                            buttonText = stringResource(Res.string.device_config_button),
                            onButtonClick = {
                                if (collectorId.isEmpty()) {
                                    showGatewayRequiredDialog = true
                                } else {
                                    // 将优化器列表序列化为JSON字符串
                                    val optimizerListJson = Json.encodeToString(ListSerializer(Optimizer.serializer()), optimizerList)
                                    
                                    // 使用savedStateHandle传递数据
                                    navHostController.currentBackStackEntry?.savedStateHandle?.set("optimizerList", optimizerListJson)
                                    
                                    // 导航到优化器配置页面
                                    navHostController.navigate(Route.OPTIMIZER_CONFIG)
                                }
                            }
                        )
                    },
                    lastRow = { DeviceTwoTextRow(
                        firstText = stringResource(Res.string.imei_number).replace("{imei}", optimizerIdStr),
                        secondText = stringResource(Res.string.device_count).replace("{count}", optimizerList.size.toString())
                    )
                    },
                )

                // 电站组串
                DeviceItem(
                    imagePainter = painterResource(Res.drawable.group),
                    secondRowText = stringResource(Res.string.belongs_to_station).replace("{station}", systemName),
                    firstRow = {
                        DeviceTextButtonRow(
                            textStr = stringResource(Res.string.station_group),
                            buttonText = stringResource(Res.string.device_config_button),
                            onButtonClick = {
                                if (collectorId.isEmpty()) {
                                    showGatewayRequiredDialog = true
                                } else {
                                    // 将组串列表序列化为JSON字符串
                                    val groupListJson = Json.encodeToString(ListSerializer(Group.serializer()), groupList)
                                    
                                    // 使用savedStateHandle传递数据
                                    navHostController.currentBackStackEntry?.savedStateHandle?.set("groupList", groupListJson)
                                    navHostController.currentBackStackEntry?.savedStateHandle?.set("stationName", systemName)
                                    // 传递collectorId参数
                                    navHostController.currentBackStackEntry?.savedStateHandle?.set("collectorId", collectorId)
                                    navHostController.currentBackStackEntry?.savedStateHandle?.set("imei", imei)


                                    // 导航到组串配置页面
                                    navHostController.navigate(Route.GROUP_CONFIG)
                                }
                            }
                        )
                    },
                    lastRow = { DeviceTwoTextRow(
                        firstText = stringResource(Res.string.optimizer_count_text).replace("{count}", optimizerList.size.toString()),
                        secondText = stringResource(Res.string.group_count).replace("{count}", groupList.size.toString())
                    )
                    },
                )
            }
        }
        
        // 显示需要先添加智能网关的提示对话框
        if (showGatewayRequiredDialog) {
            SimpleDialog(
                messageStr = "请先新增智能网关，再进行设备配置！",
                onConfirm = { showGatewayRequiredDialog = false }
            )
        }
    }
}