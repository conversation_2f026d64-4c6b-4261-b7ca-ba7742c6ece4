package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.ymx.photovoltaic.ui.page.theme.ButtonPrimaryColor
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.cancel
import photovoltaic_kmp_app.composeapp.generated.resources.close
import photovoltaic_kmp_app.composeapp.generated.resources.confirm
import photovoltaic_kmp_app.composeapp.generated.resources.operation_failed
import photovoltaic_kmp_app.composeapp.generated.resources.operation_success
import photovoltaic_kmp_app.composeapp.generated.resources.result_fail
import photovoltaic_kmp_app.composeapp.generated.resources.result_success

@Composable
fun CustomDialog(
    confirmStr:String,
    confirmButtonText: String = stringResource(Res.string.confirm),
    onConfirm: () -> Unit,   // 点击"确定"时的操作
    onCancel: () -> Unit,
    contextStr:String="",// 点击"取消"时的操作
) {
    Dialog(
        onDismissRequest = { onCancel() },  // 点击弹窗外部区域关闭弹窗
        properties = DialogProperties(dismissOnClickOutside = true)
    ) {
        Surface(
            shape = MaterialTheme.shapes.medium,
            color = Color.White
        ) {
            Column(
                modifier = Modifier.width(330.dp).wrapContentHeight(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 弹窗标题
                Text(
                    text = confirmStr,
                    fontSize = 18.sp,
                    color = Color.Black,
                    textAlign = TextAlign.Center,
                    modifier = if(contextStr.isNotEmpty())
                        Modifier.padding(top = 36.dp)
                    else
                        Modifier.padding(vertical = 36.dp)
                )

                if(contextStr.isNotEmpty())
                {
                    Text(
                        text = contextStr,
                        fontSize = 16.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(bottom = 36.dp)
                    )
                }

                HorizontalDivider(thickness = 0.5.dp, color = Color.LightGray)

                // 按钮区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(IntrinsicSize.Min),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 取消按钮
                    TextButton(
                        onClick = { onCancel() },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(text = stringResource(Res.string.cancel), fontSize = 17.sp, color = Color.Black)
                    }

                    VerticalDivider(
                        color = Color.LightGray,
                        thickness = 0.5.dp,
                        modifier = Modifier
                            .fillMaxHeight()
                            .width(1.dp)
                    )

                    // 确定按钮
                    TextButton(
                        onClick = { onConfirm() },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(text = confirmButtonText, fontSize = 17.sp, color = Color.Red)
                    }
                }
            }
        }
    }
}


@Composable
fun ResultDialog(isSuccess:Boolean,dialogMsg:String="",
                 onDismiss: () -> Unit,
) {

    Dialog(onDismissRequest = {onDismiss()},
     ) {
        Box(
            modifier = Modifier
                .size(150.dp)
                .background(Color(0xFF444444), shape = RoundedCornerShape(8.dp)),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // 对勾图标
                Icon(
                    painter = if (isSuccess) painterResource(Res.drawable.result_success)
                    else painterResource(Res.drawable.result_fail), // 使用自己的对勾图标资源
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(40.dp)
                )

                // 成功文本
                Text(
                    text = if(dialogMsg.isNotEmpty()) dialogMsg else
                        if (isSuccess) stringResource(Res.string.operation_success) 
                        else stringResource(Res.string.operation_failed),
                    color = Color.White,
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
    }
}


@Composable
fun AgreementDialog(title: String, content: String, onDismiss: () -> Unit) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(text = title, fontWeight = FontWeight.Bold, fontSize = 18.sp) },
        text = { Text(text = content, fontSize = 14.sp) },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(Res.string.close), color = Color.Blue)
            }
        }
    )
}

@Composable
fun ContentDialog(
    content: @Composable () -> Unit,
    onConfirm: () -> Unit
) {
    Dialog(
        onDismissRequest = { onConfirm() },
        properties = DialogProperties(dismissOnClickOutside = true)
    ) {
        Surface(
            shape = RoundedCornerShape(16.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier
                    .wrapContentHeight()
                    .padding(bottom = 8.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 自定义内容区域
                content()
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 确定按钮
                TextButton(
                    onClick = { onConfirm() },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                ) {
                    Text(
                        text = stringResource(Res.string.confirm), 
                        fontSize = 17.sp, 
                        color = ButtonPrimaryColor
                    )
                }
            }
        }
    }
}

@Composable
fun SimpleDialog(
    messageStr: String,
    confirmButtonText: String = stringResource(Res.string.confirm),
    onConfirm: () -> Unit  // 点击"确定"时的操作
) {
    Dialog(
        onDismissRequest = { onConfirm() },  // 点击弹窗外部区域关闭弹窗
        properties = DialogProperties(dismissOnClickOutside = true)
    ) {
        Surface(
            shape = MaterialTheme.shapes.medium,
            color = Color.White
        ) {
            Column(
                modifier = Modifier.width(330.dp).wrapContentHeight(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 弹窗消息
                Text(
                    text = messageStr,
                    fontSize = 16.sp,
                    color = Color.Black,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(vertical = 36.dp, horizontal = 24.dp)
                )

                // Horizontal Divider
                HorizontalDivider(thickness = 0.5.dp, color = Color.LightGray)

                // 确定按钮
                TextButton(
                    onClick = { onConfirm() },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(text = confirmButtonText, fontSize = 17.sp, color = ButtonPrimaryColor)
                }
            }
        }
    }
}