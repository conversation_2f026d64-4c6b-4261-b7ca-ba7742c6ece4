
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.PopupProperties
import com.ymx.photovoltaic.language.Language
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.cancel


@Composable
fun LanguageSelector(
    expanded: Boolean,
    onDismissRequest: () -> Unit,
    selectedLanguage: Language,
    onLanguageSelected: (Language) -> Unit,
    availableLanguages: List<Language>,
    modifier: Modifier = Modifier
) {
    DropdownMenu(
        expanded = expanded,
        onDismissRequest = onDismissRequest,
        modifier = modifier.background(Color.White),
        properties = PopupProperties(focusable = true)
    ) {
        availableLanguages.forEachIndexed { index, language ->
            DropdownMenuItem(
                text = {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(text=language.name,
                           fontSize = 16.sp ,
                            // 去除字体加黑加重效果，恢复成普通字体
                            style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Normal))
                        if (language.code == selectedLanguage.code) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "Selected",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                },
                onClick = { onLanguageSelected(language) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            )

            if (index < availableLanguages.size - 1) {
                HorizontalDivider()
            }
        }
        HorizontalDivider()

        DropdownMenuItem(
            text = {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center, // 水平居中
                    verticalAlignment = Alignment.CenterVertically // 垂直居中
                ) {
                    Text(text= stringResource(Res.string.cancel), fontSize = 16.sp,
                        style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Normal))
                }
                },
            onClick = onDismissRequest,
        )
    }
}
