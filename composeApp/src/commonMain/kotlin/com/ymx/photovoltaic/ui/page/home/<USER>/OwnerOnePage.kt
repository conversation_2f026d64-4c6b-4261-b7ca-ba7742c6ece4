package com.ymx.photovoltaic.ui.page.home.edit

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.PlaceholderText
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.ui.page.widget.rememberSingleColumnPickerState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.error_empty_gender
import photovoltaic_kmp_app.composeapp.generated.resources.error_empty_name
import photovoltaic_kmp_app.composeapp.generated.resources.error_empty_phone
import photovoltaic_kmp_app.composeapp.generated.resources.error_invalid_email
import photovoltaic_kmp_app.composeapp.generated.resources.error_invalid_phone
import photovoltaic_kmp_app.composeapp.generated.resources.female
import photovoltaic_kmp_app.composeapp.generated.resources.input_owner_email
import photovoltaic_kmp_app.composeapp.generated.resources.input_owner_name
import photovoltaic_kmp_app.composeapp.generated.resources.input_owner_phone
import photovoltaic_kmp_app.composeapp.generated.resources.male
import photovoltaic_kmp_app.composeapp.generated.resources.owner_email
import photovoltaic_kmp_app.composeapp.generated.resources.owner_gender
import photovoltaic_kmp_app.composeapp.generated.resources.owner_info
import photovoltaic_kmp_app.composeapp.generated.resources.owner_name
import photovoltaic_kmp_app.composeapp.generated.resources.owner_phone
import photovoltaic_kmp_app.composeapp.generated.resources.right_arrow
import photovoltaic_kmp_app.composeapp.generated.resources.select_gender
import photovoltaic_kmp_app.composeapp.generated.resources.select_gender_hint
import photovoltaic_kmp_app.composeapp.generated.resources.station_image
import photovoltaic_kmp_app.composeapp.generated.resources.submit_info
import photovoltaic_kmp_app.composeapp.generated.resources.upload_img
import photovoltaic_kmp_app.composeapp.generated.resources.upload_station_image

@Composable
fun OwnerOnePage(
    navHostController: NavHostController
) {
    // 字符串资源
    val ownerInfoText = stringResource(Res.string.owner_info)
    val ownerNameText = stringResource(Res.string.owner_name)
    val ownerGenderText = stringResource(Res.string.owner_gender)
    val ownerPhoneText = stringResource(Res.string.owner_phone)
    val ownerEmailText = stringResource(Res.string.owner_email)
    val selectGenderText = stringResource(Res.string.select_gender)
    val maleText = stringResource(Res.string.male)
    val femaleText = stringResource(Res.string.female)
    val saveText = stringResource(Res.string.submit_info)
    val errorEmptyNameText = stringResource(Res.string.error_empty_name)
    val errorEmptyGenderText = stringResource(Res.string.error_empty_gender)
    val errorEmptyPhoneText = stringResource(Res.string.error_empty_phone)
    val errorInvalidPhoneText = stringResource(Res.string.error_invalid_phone)
    val errorInvalidEmailText = stringResource(Res.string.error_invalid_email)

    // 状态变量
    var ownerName by remember { mutableStateOf("") }
    var ownerGender by remember { mutableStateOf("") }
    var ownerPhone by remember { mutableStateOf("") }
    var ownerEmail by remember { mutableStateOf("") }
    
    // 错误状态
    var nameError by remember { mutableStateOf<String?>(null) }
    var genderError by remember { mutableStateOf<String?>(null) }
    var phoneError by remember { mutableStateOf<String?>(null) }
    var emailError by remember { mutableStateOf<String?>(null) }
    
    // 性别选择器
    val genderPicker = rememberSingleColumnPickerState()
    
    // 性别选项
    val genderOptions = remember { listOf(maleText, femaleText) }
    
    // 选中的性别索引
    var genderIndex by remember { mutableStateOf(-1) }

    Scaffold(
        topBar = {
            TopBar(ownerInfoText,
                backClick = { navHostController.popBackStack() })
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 60.dp)
        ) {
            var showSuccessDialog by remember { mutableStateOf(false) }
            if (showSuccessDialog) {
                ResultDialog(true) { showSuccessDialog = false }
            }

            var showFailDialog by remember { mutableStateOf(false) }
            if (showFailDialog) {
                ResultDialog(false) { showFailDialog = false }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 业主姓名输入
            CommonTitleField(
                value = ownerName,
                onValueChange = { newValue ->
                    ownerName = newValue
                    nameError = null // 清除错误
                },
                titleText = ownerNameText,
                placeholderCom = {
                    PlaceholderText(Res.string.input_owner_name)
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                keyboardOptions = KeyboardOptions(
                    capitalization = KeyboardCapitalization.None,
                    autoCorrectEnabled = false,
                    keyboardType = KeyboardType.Text
                ),
                isError = nameError != null,
                errorText = nameError ?: ""
            )

            // 业主性别选择
            CommonTitleField(
                value = if (genderIndex >= 0) genderOptions[genderIndex] else "",
                onValueChange = { }, // 不需要，因为是只读的
                titleText = ownerGenderText,
                isSelected = genderIndex >= 0,
                placeholderCom = {
                    PlaceholderText(Res.string.select_gender_hint)
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                isReadOnly = true,
                isError = genderError != null,
                errorText = genderError ?: "",
                onBoxClick = {
                    genderPicker.show(
                        title = selectGenderText,
                        range = genderOptions,
                        value = if (genderIndex < 0) 0 else genderIndex
                    ) { index ->
                        genderIndex = index
                        ownerGender = genderOptions[index]
                        genderError = null // 清除错误
                    }
                },
                trailingIconCom = {
                    Image(
                        painter = painterResource(Res.drawable.right_arrow),
                        modifier = Modifier.size(18.dp),
                        contentDescription = "Select Gender"
                    )
                }
            )

            // 业主手机输入
            CommonTitleField(
                value = ownerPhone,
                onValueChange = { newValue ->
                    // 只允许输入数字
                    val validChars = setOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9')
                    ownerPhone = newValue.filter { it in validChars }
                    phoneError = null // 清除错误
                },
                titleText = ownerPhoneText,
                placeholderCom = {
                    PlaceholderText(Res.string.input_owner_phone)
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Number
                ),
                isError = phoneError != null,
                errorText = phoneError ?: ""
            )

            // 业主邮箱输入
            CommonTitleField(
                value = ownerEmail,
                onValueChange = { newValue ->
                    ownerEmail = newValue
                    emailError = null // 清除错误
                },
                titleText = ownerEmailText,
                placeholderCom = {
                    PlaceholderText(Res.string.input_owner_email)
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Email
                ),
                isError = emailError != null,
                errorText = emailError ?: ""
            )
            
            // 电站图片上传
            val stationImageText = stringResource(Res.string.station_image)
            var stationImage by remember { mutableStateOf("") }
            
            CommonTitleField(
                value = stationImage,
                onValueChange = { },  // 不需要文本输入变化，因为是图片上传
                titleText = stationImageText,
                placeholderCom = {
                    PlaceholderText(Res.string.upload_station_image)
                },
                leadingIconCom = {
                    Image(
                        painter = painterResource(Res.drawable.upload_img),
                        contentDescription = "Upload Image",
                        modifier = Modifier.size(100.dp).padding(start = 20.dp)
                    )
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 120,
                cornerRadius = 15,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                isReadOnly = true,
                onBoxClick = {
                    // 这里可以添加图片选择逻辑
                    // 例如打开图片选择器
                }
            )

            Spacer(modifier = Modifier.weight(1f))

            val coroutineScope = rememberCoroutineScope()

            ConfirmButton(saveText, true) {
                // 重置错误
                nameError = null
                genderError = null
                phoneError = null
                emailError = null
                var hasError = false

                // 验证姓名
                if (ownerName.trim().isEmpty()) {
                    nameError = errorEmptyNameText
                    hasError = true
                }

                // 验证性别
                if (genderIndex < 0) {
                    genderError = errorEmptyGenderText
                    hasError = true
                }

                // 验证手机号
                if (ownerPhone.trim().isEmpty()) {
                    phoneError = errorEmptyPhoneText
                    hasError = true
                } else if (ownerPhone.length != 11) {
                    phoneError = errorInvalidPhoneText
                    hasError = true
                }

                // 验证邮箱
                if (ownerEmail.trim().isNotEmpty()) {
                    val emailPattern = "[a-zA-Z0-9._-]+@[a-z]+\\.+[a-z]+"
                    if (!ownerEmail.matches(emailPattern.toRegex())) {
                        emailError = errorInvalidEmailText
                        hasError = true
                    }
                }

                if (hasError) {
                    return@ConfirmButton
                }

                // TODO: 保存业主信息到后端
                // 这里应该调用API保存数据
                
                // 显示成功对话框
                showSuccessDialog = true
                coroutineScope.launch {
                    delay(2000)
                    showSuccessDialog = false
                    navHostController.popBackStack()
                }
            }
        }
    }
}