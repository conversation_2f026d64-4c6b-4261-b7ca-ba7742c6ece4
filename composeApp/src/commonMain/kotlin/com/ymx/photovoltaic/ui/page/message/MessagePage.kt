package com.ymx.photovoltaic.ui.page.message

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.bean.Warning
import com.ymx.photovoltaic.ui.page.common.CommonBottomBar
import com.ymx.photovoltaic.ui.page.theme.Green_65
import com.ymx.photovoltaic.ui.page.theme.Grey_29
import com.ymx.photovoltaic.ui.page.theme.Grey_5E
import com.ymx.photovoltaic.ui.page.theme.Grey_82
import com.ymx.photovoltaic.ui.page.theme.Grey_B3
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonSearch
import com.ymx.photovoltaic.ui.page.widget.SegmentedButtonGroup
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.util.DateTimeUtil
import com.ymx.photovoltaic.viewmodel.HomeViewModel
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.auto_open
import photovoltaic_kmp_app.composeapp.generated.resources.blue_warning
import photovoltaic_kmp_app.composeapp.generated.resources.ic_right_arrow
import photovoltaic_kmp_app.composeapp.generated.resources.ic_search
import photovoltaic_kmp_app.composeapp.generated.resources.input_query_content
import photovoltaic_kmp_app.composeapp.generated.resources.message_alarm_unread
import photovoltaic_kmp_app.composeapp.generated.resources.message_resume_unread
import photovoltaic_kmp_app.composeapp.generated.resources.operation_info
import photovoltaic_kmp_app.composeapp.generated.resources.power_abnormal
import photovoltaic_kmp_app.composeapp.generated.resources.power_restored
import photovoltaic_kmp_app.composeapp.generated.resources.red_warning
import photovoltaic_kmp_app.composeapp.generated.resources.temp_shutdown
import photovoltaic_kmp_app.composeapp.generated.resources.temp_warning
import photovoltaic_kmp_app.composeapp.generated.resources.temp_warning_resolved
import photovoltaic_kmp_app.composeapp.generated.resources.warning

@Composable
fun MessagePage(
    navHostController: NavHostController,
    homeViewModel: HomeViewModel = getKoin().get()
) {
    val todayWarningDetails by homeViewModel.todayWarningDetailsFlow.collectAsState()

    // 使用 AppGlobal.powerIdList 调用新接口
    LaunchedEffect(Unit) {
        AppGlobal.powerIdList?.let { powerIdList ->
            homeViewModel.fetchTodayWarningDetails(powerIdList)
        }
    }

    var searchQuery by remember { mutableStateOf("") }
    var selectedFilterIndex by remember { mutableStateOf(0) } // 0: 全部, 1: 未读, 2: 已读
    val filterOptions = listOf("全部信息", "未读信息", "已读信息") // TODO: Use string resources

    // 过滤警告列表
    val filteredItems = todayWarningDetails.filter {
        // 先按搜索条件过滤
        it.warningContent.contains(searchQuery, ignoreCase = true)
    }.filter { warning ->
        // 再按已读/未读状态过滤
        when (selectedFilterIndex) {
            1 -> warning.isRead == 1 // 未读
            2 -> warning.isRead == 2 // 已读
            else -> true // 全部
        }
    }

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.operation_info), backClick = { }, isBackIcon = false)
        },
        containerColor = Grey_F5,
        bottomBar = {
            CommonBottomBar(navController = navHostController)
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier.padding(paddingValues)
        ) {
            CommonSearch(
                searchQuery = searchQuery,
                onQueryChange = { searchQuery = it },
                placeholderTextResId = Res.string.input_query_content,
                Modifier.
                padding(start = 10.dp, end = 10.dp, top = 16.dp, bottom = 10.dp).fillMaxWidth().height(48.dp)
                    .clip(RoundedCornerShape(25.dp))
            )

            // 添加 SegmentedButtonGroup
            SegmentedButtonGroup(
                options = filterOptions,
                initialSelectedIndex = selectedFilterIndex,
                onSelectionChanged = { _, index ->
                    selectedFilterIndex = index
                },
                modifier = Modifier.padding(horizontal = 10.dp, vertical = 8.dp)
            )

            if (todayWarningDetails.isNotEmpty()) {
                WarningList(warningList = filteredItems, onItemClick = { warning ->
                    // 导航到详情页，传递参数
                    val id = warning.id
                    val typeStr = warning.warningType.toString()
                    val contentStr = warning.warningContent
                    val timeStr = warning.createTime.toString()
                    val isReadStr = (warning.isRead == 2).toString()
                    
                    navHostController.navigate("message_detail/$id/$typeStr/$contentStr/$timeStr/$isReadStr")
                })
            }
        }
    }
}

@Composable
fun WarningList(warningList: List<Warning>, onItemClick: (Warning)->Unit)
{
    LazyColumn(
    ) {
        items(warningList) { warning ->
            AlertCard(
                type = warning.warningType,
                content = warning.warningContent,
                time = warning.createTime,
                isRead = warning.isRead==2,  // 1未读 2已读
                onClick = { onItemClick(warning) }
            )

        }
        }
    }

@Composable
fun AlertCard(type: Int, content: String, time: Long, isRead: Boolean, onClick: () -> Unit = {}) {
    Card(
        shape = RoundedCornerShape(12.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
            .clickable { 
                // 导航到详情页
                onClick()
            }
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Image(
                    painter = when(type)
                    {
                        1,2,6 -> if(isRead)
                            painterResource(Res.drawable.red_warning)
                        else painterResource(Res.drawable.message_alarm_unread)
                        4,5,7 -> if(isRead) painterResource(Res.drawable.blue_warning)
                        else painterResource(Res.drawable.message_resume_unread)
                        else -> painterResource(Res.drawable.red_warning)
                    },  // 这里可以替换成警告图标,
                    contentDescription = "warn",
                    modifier = if(isRead) Modifier.size(26.dp) else Modifier.size(30.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = when (type)
                {
                    1 -> stringResource(Res.string.temp_warning)
                    2 -> stringResource(Res.string.power_abnormal)
                    4 -> stringResource(Res.string.temp_warning_resolved)
                    5 -> stringResource(Res.string.power_restored)
                    6 -> stringResource(Res.string.temp_shutdown)
                    7 -> stringResource(Res.string.auto_open)
                    else -> stringResource(Res.string.warning)
                },

                    fontSize = 18.sp, color = Grey_29)
                Spacer(modifier = Modifier.weight(1f))
               Text(
                   text = if (isRead) "已读" else "未读",
                   fontSize = 14.sp,
                   color = if (isRead) Grey_82 else Green_65
               )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Text(text = "您有一条警告信息，位于${content}，请前往查看...", fontSize = 14.sp, color = Grey_5E)
            // 分割线
            HorizontalDivider(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 10.dp, bottom = 10.dp), // 使分割线垂直填充整个Row的高度
                thickness = 1.dp, // 分割线厚度
                color = Color.LightGray // 分割线颜色
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                TimeDisplay(time)

                Spacer(modifier = Modifier.weight(1f))
               Icon(
                   painter = painterResource(Res.drawable.ic_right_arrow),  // 这里可以替换成箭头图标
                   contentDescription ="arrow",
                   tint = Color.Gray,
                   modifier = Modifier.size(16.dp)
               )
            }
        }
    }
}

@Composable
fun TimeDisplay(timestamp: Long) {
    val formattedTime = remember(timestamp) {
        DateTimeUtil.formatTimestamp(timestamp)
    }
    Text(text = formattedTime, fontSize = 12.sp, color = Grey_B3)
}

@Composable
fun MessageSearch(searchQuery: String, onQueryChange: (String) -> Unit) {
    TextField(
        value = searchQuery,
        onValueChange = onQueryChange,
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(25.dp))
            .padding(start = 10.dp, end = 10.dp, top = 16.dp, bottom = 10.dp),
        colors = TextFieldDefaults.colors(
            focusedContainerColor = Color.White,
            unfocusedContainerColor = Color.White,
            focusedIndicatorColor = Color.White,
            unfocusedIndicatorColor = Color.White,
        ),
        placeholder = { androidx.compose.material3.Text(text = stringResource( Res.string.input_query_content)) },
        singleLine = true,
        maxLines = 1,
        textStyle = TextStyle(color = Color.Black),
        leadingIcon = {
            Icon(
                painter = painterResource(Res.drawable.ic_search),
                contentDescription = "Search Icon",
                tint = Color.Gray
            )
        },
        keyboardOptions = KeyboardOptions.Default.copy(imeAction = ImeAction.Search),
        keyboardActions = KeyboardActions(onSearch = {
            // 可以在此处处理额外的搜索逻辑
        })
    )
}
