package com.ymx.photovoltaic.ui.page.theme

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color

     val LjRed = Color(0xFFE60012)

//灰色 #F2F3F5(背景色)
//    val Grey_F5 = Color(0xFFF2F3F5)

   val Grey_F5 = Color(247,247,247,255)

    //红色 #E9443F
    val Red_3F = Color(0xFFE9443F)

    //绿色 #73C764
    val Green_64 = Color(0xFF73C764)

    // 蓝色 #3274F9
    val Blue_F9 = Color(0xFF3274F9)

    //灰色 #BCBCBE
    val Grey_Be = Color(0xFFBCBCBE)

    val DeepBlue_F7 = Color(0xFF245CF7)

    val Report_Line_Color =Color(0xFFED4242)

    // 浅黄色背景 - 用于收益统计卡片
    val LightYellow = Color(0xFFFFF9E5)

    // 浅绿色背景 - 用于发电统计卡片
    val LightGreen = Color(0xFFF2FFF5)

    val Font_128=Color(128, 128, 128, 1)

    // 灰色 #383838
    val Grey_38 = Color(0xFF383838)
    
    // 灰色 #696969
    val Grey_69 = Color(0xFF696969)
    
    // 灰色 #A6A6A6
    val Grey_A6 = Color(0xFFA6A6A6)
    
    // 绿色 #2EB865
    val Green_65 = Color(0xFF2EB865)
    
    // 深灰色 #292929
    val Grey_29 = Color(0xFF292929)
    
    // 灰色 #5E5E5E
    val Grey_5E = Color(0xFF5E5E5E)
    
    // 灰色 #C4C4C4
    val Grey_C4 = Color(0xFFC4C4C4)
    
    // 灰色 #808080
    val Grey_80 = Color(0xFF808080)

    val Grey_82 = Color(0xFF828282)

   // 深灰色 #B3B3B3
   val Grey_B3 = Color(0xFFB3B3B3)
    
    // 深灰色 #3B3B3B
    val Grey_3B = Color(0xFF3B3B3B)

    val Grey_D6 = Color(0xFFD6D6D6)

    val Grey_E8 = Color(0xFFE8E8E8)



  // rgba(230, 0, 18, 1)
val PinkMiddle = Color(0xFFEF5864)  // rgba(239, 88, 100, 1)
val WhiteEnd = Color(0xFFFFFFFF)

val ButtonGradient = Brush.linearGradient(
    0.0f to LjRed,
    0.4468f to PinkMiddle,
    // 对应 0%、44.68%、100% 位置
    start = Offset(0.0f, 50.0f),
    end = Offset(0.0f, 100.0f) // 180 度垂直方向
)


val BlueColor = Color(0xFF0078E8)

//val PrimaryColor = Color(0xff07C160)
val ButtonPrimaryColor = Color(0xFFEF5864)

val Grey_8A = Color(0xFF8A8A8A)

val DangerColorLight = Color(0xffFA5151)
val FontColorDark = Color.White.copy(0.8f)
val FontColorLight = Color(0f, 0f, 0f, 0.9f)

// rgba(89, 89, 89, 1)
val Grey_89 = Color(89, 89, 89, 255)

// rgba(255, 102, 102, 1)
val RedChart = Color(255, 102, 102, 255)

// rgba(255, 145, 28, 1)
val OrangeChart = Color(255, 145, 28, 255)

// 柱状图渐变色 - 从红色到橙色
val ChartGradient = Brush.linearGradient(
    0.0f to RedChart,
    1.0f to OrangeChart,
    start = Offset(0.0f, 0.0f),
    end = Offset(0.0f, 1000.0f) // 垂直方向渐变
)

// 社会贡献背景色 - 浅蓝色 #E6F8FF 透明度20%
val LightBlue_FF = Color(0xFFE6F8FF).copy(alpha = 0.8f)

// 社会贡献背景色 - 浅橙色 #FCEDDE 透明度80%
val LightOrange_DE = Color(0xFFFCEDDE).copy(alpha = 0.8f)

// 社会贡献背景色 - 浅绿色 #E0FFF0 透明度80%
val LightGreen_F0 = Color(0xFFE0FFF0).copy(alpha = 0.8f)
