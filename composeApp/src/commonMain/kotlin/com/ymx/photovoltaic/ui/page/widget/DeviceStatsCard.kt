package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.home_error
import photovoltaic_kmp_app.composeapp.generated.resources.home_offline
import photovoltaic_kmp_app.composeapp.generated.resources.home_ok
import photovoltaic_kmp_app.composeapp.generated.resources.home_total

/**
 * 设备统计卡片组件
 * 
 * @param totalCount 总设备数
 * @param normalCount 正常设备数
 * @param faultCount 故障设备数
 * @param offlineCount 离线设备数
 * @param modifier Modifier 修饰符
 */
@Composable
fun DeviceStatsCard(
    totalCount: Int,
    normalCount: Int,
    faultCount: Int,
    offlineCount: Int,
    modifier: Modifier = Modifier
) {
    Card(
        colors = CardDefaults.cardColors(containerColor = Color.White),
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 10.dp, vertical = 8.dp),
        shape = RoundedCornerShape(16.dp))
    {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp, vertical = 16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 总计
            StatsItem(
                iconRes = Res.drawable.home_total,
                count = totalCount,
                label = "总计",
                tintColor = Color(0xFF3F51B5)
            )
            
            // 分隔线
            HorizontalDivider(
                modifier = Modifier
                    .height(36.dp)
                    .width(1.dp),
                color = Color.LightGray
            )
            
            // 正常
            StatsItem(
                iconRes = Res.drawable.home_ok,
                count = normalCount,
                label = "正常",
                tintColor = Color(0xFF4CAF50)
            )
            
            // 故障
            StatsItem(
                iconRes = Res.drawable.home_error,
                count = faultCount,
                label = "异常",
                tintColor = Color(0xFFF44336)
            )
            
            // 离线
            StatsItem(
                iconRes = Res.drawable.home_offline,
                count = offlineCount,
                label = "离线",
                tintColor = Color(0xFF9E9E9E)
            )
        }
    }
}

@Composable
private fun StatsItem(
    iconRes: DrawableResource,
    count: Int,
    label: String,
    tintColor: Color,
    modifier: Modifier = Modifier
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.padding(horizontal = 2.dp)
    ) {
        // 图标容器
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .size(36.dp)
                .clip(RoundedCornerShape(21.dp))
                .background(tintColor.copy(alpha = 0.1f))
        ) {
            Image(
                painter = painterResource(iconRes),
                contentDescription = label,
                modifier = Modifier.size(20.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 文字内容列
        Column {
            // 标签
            Text(
                text = label,
                style = TextStyle(
                    fontSize = 13.sp,
                    color = Color.Gray
                )
            )
            
            // 数量
            Text(
                text = count.toString(),
                style = TextStyle(
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )
            )
        }
    }
}