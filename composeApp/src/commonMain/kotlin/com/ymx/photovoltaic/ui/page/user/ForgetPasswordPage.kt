package com.ymx.photovoltaic.ui.page.user

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.platform.ToastUtil
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.theme.LjRed
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.LoadingDialog
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.UserViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.account_verification
import photovoltaic_kmp_app.composeapp.generated.resources.confirm
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_new_password
import photovoltaic_kmp_app.composeapp.generated.resources.field_required
import photovoltaic_kmp_app.composeapp.generated.resources.forgot_password
import photovoltaic_kmp_app.composeapp.generated.resources.get_code_failed
import photovoltaic_kmp_app.composeapp.generated.resources.get_code_success
import photovoltaic_kmp_app.composeapp.generated.resources.input_new_password
import photovoltaic_kmp_app.composeapp.generated.resources.input_new_password_again
import photovoltaic_kmp_app.composeapp.generated.resources.input_phone_or_email
import photovoltaic_kmp_app.composeapp.generated.resources.new_password
import photovoltaic_kmp_app.composeapp.generated.resources.password_not_match_tip
import photovoltaic_kmp_app.composeapp.generated.resources.please_input_account
import photovoltaic_kmp_app.composeapp.generated.resources.please_input_verification_code
import photovoltaic_kmp_app.composeapp.generated.resources.reset_password_failed
import photovoltaic_kmp_app.composeapp.generated.resources.resetting_password
import photovoltaic_kmp_app.composeapp.generated.resources.seconds_later
import photovoltaic_kmp_app.composeapp.generated.resources.send_verification_code

@Composable
fun ForgetPasswordPage(
    navHostController: NavHostController,
    userViewModel: UserViewModel = getKoin().get()
) {
    val scope = rememberCoroutineScope()
    
    // 在组件顶部获取字符串资源
    val getCodeFailedText = stringResource(Res.string.get_code_failed)
    val getCodeSuccessText = stringResource(Res.string.get_code_success)
    val resetPasswordFailedText = stringResource(Res.string.reset_password_failed)
    val fieldRequiredText = stringResource(Res.string.field_required)
    val inputPhoneOrEmailText = stringResource(Res.string.input_phone_or_email)
    val pleaseInputAccountText = stringResource(Res.string.please_input_account)
    val sendVerificationCodeText = stringResource(Res.string.send_verification_code)
    val accountVerificationText = stringResource(Res.string.account_verification)
    val pleaseInputVerificationCodeText = stringResource(Res.string.please_input_verification_code)
    val newPasswordText = stringResource(Res.string.new_password)
    val inputNewPasswordText = stringResource(Res.string.input_new_password)
    val confirmNewPasswordText = stringResource(Res.string.confirm_new_password)
    val inputNewPasswordAgainText = stringResource(Res.string.input_new_password_again)
    val passwordNotMatchTipText = stringResource(Res.string.password_not_match_tip)
    val confirmText = stringResource(Res.string.confirm)
    val resettingPasswordText = stringResource(Res.string.resetting_password)

    var account by remember { mutableStateOf("") }
    var verificationCode by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var passwordVisibility by remember { mutableStateOf(false) }
    var passwordAgain by remember { mutableStateOf("") }
    var passwordVisibilityAgain by remember { mutableStateOf(false) }
    var showLoadingDialog by remember { mutableStateOf(false) }
    var showError by remember { mutableStateOf(false) }
    
    // 错误提示状态
    var accountError by remember { mutableStateOf(false) }
    var verificationCodeError by remember { mutableStateOf(false) }
    var passwordError by remember { mutableStateOf(false) }
    var passwordAgainError by remember { mutableStateOf(false) }
    
    // 验证码倒计时
    var countdown by remember { mutableIntStateOf(0) }
    var showFailDialog by remember { mutableStateOf(false) }
    var showSuccessDialog by remember { mutableStateOf(false) }
    var failMessage by remember { mutableStateOf("") }

    val coroutineScope = rememberCoroutineScope()

    fun sendVerificationCode() {
        if (account.isNotEmpty()) {
            countdown = 60
            userViewModel.sendSms(
                account = account,
                type = "reset_pwd",
                errorBlock = { errorMsg ->
                    val message = errorMsg.ifEmpty { getCodeFailedText }
                    ToastUtil.showShort(message)
                    countdown = 0
                }
            ) {
                ToastUtil.showShort(getCodeSuccessText)
            }
        } else {
            // Set error if account is empty when trying to send code
            accountError = true
        }
    }

    fun confirmResetPassword() {
        // 重置错误状态
        accountError = false
        verificationCodeError = false
        passwordError = false
        passwordAgainError = false
        showError = false

        // 检查字段是否为空
        var hasError = false

        if (account.isEmpty()) {
            accountError = true
            hasError = true
        }

        if (verificationCode.isEmpty()) {
            verificationCodeError = true
            hasError = true
        }

        if (password.isEmpty()) {
            passwordError = true
            hasError = true
        }

        if (passwordAgain.isEmpty()) {
            passwordAgainError = true
            hasError = true
        }

        // 检查密码是否一致
        if (password != passwordAgain && !passwordError && !passwordAgainError) {
            showError = true
            hasError = true
        }

        if (hasError) {
            return
        }

        // 如果没有错误，调用API
        showLoadingDialog = true
        userViewModel.resetPassword(
            account = account,
            code = verificationCode,
            password = password,
            type = "reset_pwd",
            errorBlock = {
                showLoadingDialog = false
                failMessage = resetPasswordFailedText
                showFailDialog=true
                coroutineScope.launch {
                    delay(2000)
                    showFailDialog = false
                }
            }
        ) {
            showLoadingDialog = false
            showSuccessDialog = true
            scope.launch {
                delay(2000)
                showSuccessDialog = false
                navHostController.navigate(Route.LOGIN) {
                    popUpTo(Route.FORGOT_PASSWORD) { inclusive = true }
                }
            }
        }
    }

    // 倒计时效果
    LaunchedEffect(countdown) {
        if (countdown > 0) {
            delay(1000)
            countdown--
        }
    }

    SetStatusBar(Color.White,true)

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Grey_F5)
    ) {
        TopBar(
            textStr = stringResource(Res.string.forgot_password).replace("%s", ""),
            backClick = { navHostController.popBackStack() }
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp)
        ) {
            Spacer(modifier = Modifier.height(20.dp))
            
            // 账号验证
            CommonTitleField(
                titleText = accountVerificationText,
                value = account,
                onValueChange = { 
                    account = it 
                    accountError = false
                },
                placeholderCom = { Text(inputPhoneOrEmailText) },
                isError = accountError,
                errorText = if (accountError) pleaseInputAccountText else "",
                isPassword = false,
                modifier = Modifier.fillMaxWidth(),
                trailingIconCom = {
                    OutlinedButton(
                        onClick = {
                            sendVerificationCode()
                        },
                        modifier = Modifier.padding(end = 4.dp),
                        shape = RoundedCornerShape(8.dp),
                        enabled = countdown == 0, // Disable while counting down
                        border = BorderStroke(1.dp, LjRed),
                    ) {
                        Text(
                            text = if (countdown == 0) sendVerificationCodeText else stringResource(Res.string.seconds_later, countdown),
                            color = LjRed
                        )
                    }
                }
            )

            Spacer(modifier = Modifier.height(12.dp))
            
            // 验证码输入框
            CommonTitleField(
                value = verificationCode,
                onValueChange = {
                    verificationCode = it
                    verificationCodeError = false
                },
                placeholderCom = { Text(pleaseInputVerificationCodeText) },
                isError = verificationCodeError,
                errorText = if (verificationCodeError) pleaseInputVerificationCodeText else "",
                isPassword = false
            )

            Spacer(modifier = Modifier.height(24.dp))


            // 新密码输入框
            CommonTitleField(
                titleText = newPasswordText,
                value = password,
                onValueChange = { 
                    password = it 
                    passwordError = false
                    showError = password != passwordAgain && passwordAgain.isNotEmpty()
                },
                placeholderCom = { Text(inputNewPasswordText) },
                passwordVisibility = passwordVisibility,
                onPasswordVisibilityChange = { passwordVisibility = !passwordVisibility },
                modifier = Modifier.fillMaxWidth(),
                isError = passwordError,
                errorText = if (passwordError) fieldRequiredText else "",
                isPassword = true
            )

            Spacer(modifier = Modifier.height(24.dp))


            // 确认密码输入框
            CommonTitleField(
                titleText = confirmNewPasswordText,
                value = passwordAgain,
                onValueChange = { 
                    passwordAgain = it 
                    passwordAgainError = false
                    showError = password != passwordAgain && password.isNotEmpty()
                },
                placeholderCom = { Text(inputNewPasswordAgainText) },
                passwordVisibility = passwordVisibilityAgain,
                onPasswordVisibilityChange = { passwordVisibilityAgain = !passwordVisibilityAgain },
                modifier = Modifier.fillMaxWidth(),
                isError = passwordAgainError || showError,
                errorText = if (passwordAgainError) fieldRequiredText else if (showError) passwordNotMatchTipText else "",
                isPassword = true
            )


            Spacer(modifier = Modifier.weight(1f))

            // 确认按钮
            Button(
                onClick = {
                    confirmResetPassword()
                },
                modifier = Modifier
                    .fillMaxWidth().height(50.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = LjRed
                ),
                shape = RoundedCornerShape(30.dp)
            ) {
                Text(
                    text = confirmText,
                    color = Color.White,
                    modifier = Modifier.padding(vertical = 4.dp),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                )
            }

            Spacer(modifier = Modifier.height(60.dp))
        }
    }

    // 显示加载对话框
    if (showLoadingDialog) {
        LoadingDialog(
            loadingText = resettingPasswordText
        ) { showLoadingDialog = false }
    }

    // 显示失败对话框
    if (showFailDialog) {
        ResultDialog(isSuccess = false, dialogMsg = failMessage) {
            showFailDialog = false
        }
    }

    // 显示成功对话框
    if (showSuccessDialog) {
        ResultDialog(true) {
            showSuccessDialog = false
        }
    }
}

