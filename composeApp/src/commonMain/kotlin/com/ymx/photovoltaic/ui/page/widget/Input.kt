package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.ui.page.theme.Grey_38
import com.ymx.photovoltaic.ui.page.theme.Grey_69

@Composable
fun CustomInput(
    value: String?,
    modifier: Modifier = Modifier,
    label: String? = null,//输入框的标签，通常显示在输入框的上方
    placeholder: String? = null,//输入框的占位符文本，
    // 通常显示在输入框内部，用于提示用户输入的格式
    disabled: Boolean = false,
    labelWidth: Dp = 68.dp,//标签的宽度
    textAlign: TextAlign = TextAlign.Unspecified,//输入框中文本的对齐方式
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,//键盘的选项,例如键盘类型、输入法
    keyboardActions: KeyboardActions = KeyboardActions.Default,//键盘的动作,例如点击回车键时的操作
    onClick: (() -> Unit)? = null,//这是一个回调函数，当输入框被点击时触发
    onChange: ((String) -> Unit)? = null //这是一个回调函数，当输入框的值发生变化时触发
) {
    val localValue = value ?: ""

    Column {
        Row(modifier = modifier.height(40.dp), verticalAlignment = Alignment.CenterVertically) {
            if (label?.isNotEmpty() == true) {
                Text(
                    text = label,
                    color = Grey_38,
                    fontSize = 16.sp,
                    modifier = Modifier.width(labelWidth)
                )
                Spacer(modifier = Modifier.width(16.dp))
            }
            BasicTextField(
                value = localValue,
                onValueChange = {
                    onChange?.invoke(it)
                },
                modifier = Modifier
                    .weight(1f)
                    .height(40.dp),
                readOnly = disabled,
                singleLine = true,
                textStyle = TextStyle(
                    color = Grey_69,
                    fontSize = 16.sp,
                    textAlign = textAlign
                ),
                keyboardOptions = keyboardOptions,
                keyboardActions = keyboardActions,
                visualTransformation = if (keyboardOptions.keyboardType == KeyboardType.Password)
                    PasswordVisualTransformation() else VisualTransformation.None, //根据键盘类型设置视觉转换，例如密码输入框的掩码。
                cursorBrush = SolidColor(Color.Black)//设置输入框中光标的颜色。
            ) { innerTextField ->
                Box(
                    //使用 Box 组件来创建一个容器，用于放置输入框的内部文本字段和占位符文本
                    modifier = Modifier
                        .fillMaxSize()
                        .then(onClick?.let {
                            Modifier.clickableWithoutRipple { it() }
                        } ?: Modifier),
                    contentAlignment = when (textAlign) {
                        TextAlign.Center -> Alignment.Center
                        TextAlign.Right -> Alignment.CenterEnd
                        else -> Alignment.CenterStart
                    }
                ) {
                    innerTextField()
                    if (localValue.isEmpty() && placeholder?.isNotEmpty() == true) {
                        Text(
                            text = placeholder,
                            color = Grey_69,
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
        WeDivider()
    }
}