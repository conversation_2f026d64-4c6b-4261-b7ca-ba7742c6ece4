package com.ymx.photovoltaic.ui.page.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.local.CacheManager
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.platform.ToastUtil
import com.ymx.photovoltaic.platform.rememberApplicationContext
import com.ymx.photovoltaic.platform.togglePlatformFlashlight
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.publicvalue.multiplatform.qrcode.CameraPosition
import org.publicvalue.multiplatform.qrcode.CodeType
import org.publicvalue.multiplatform.qrcode.ScannerWithPermissions
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.scan_box
import photovoltaic_kmp_app.composeapp.generated.resources.scan_flashlight
import photovoltaic_kmp_app.composeapp.generated.resources.scan_input
import photovoltaic_kmp_app.composeapp.generated.resources.scan_upload

// 10进制优化器正则
private val optimizerRegexDecimal = "^0{5}[1][0-9]{4}"

// 16进制优化器正则
private val optimizerRegexHex = "^0{5}[2|3][0-9a-f]{4}"

private val optimizerRegexHexNew = "^0{6}[2-9a-f][0-9a-f]{4}"

// 16进制优化器正则1（生产时弄错的编码的正则）
private val optimizerRegexHex1 = "^0{4}[2|3][0-9a-f]{5}"

// 采集器的正则
private val imeiRegex = "[0-9]{16}"

// 编译正则表达式模式
private val optimizerPatternHex = Regex(optimizerRegexHex)
private val optimizerPatternHex1 = Regex(optimizerRegexHex1)
private val optimizerPatternHexNew = Regex(optimizerRegexHexNew)
private val optimizerPatternDecimal = Regex(optimizerRegexDecimal)
private val imeiPattern = Regex(imeiRegex)

// 处理验证扫描的方法
private fun handleVerifyScan(scanResult: String, equipmentViewModel: EquipmentViewModel) {
    val language = CacheManager.getLanguage()
    val resultValue =
        if (optimizerPatternHex.matches(scanResult) || optimizerPatternHex1.matches(scanResult)
            || optimizerPatternHexNew.matches(scanResult)) {
            scanResult.replace("^0+".toRegex(), "")
        } else if (optimizerPatternDecimal.matches(scanResult)) {
            scanResult.replaceFirst("000".toRegex(), "")
        } else {
            scanResult
        }

    equipmentViewModel.fetchScanDeviceInfo(
        code = resultValue,
        language = language,
        errorBlock = {
            ToastUtil.showShort("$scanResult,该产品不存在")
        }
    ) {
        ToastUtil.showShort("$scanResult,该产品是正品")
    }
}

// 处理优化器扫描的方法
private fun handleOptimizerScan(
    scanResult: String, 
    groupId: String, 
    equipmentViewModel: EquipmentViewModel,
    onSuccess: () -> Unit
) {
    val resultValue =
        if (optimizerPatternHex.matches(scanResult) || optimizerPatternHex1.matches(scanResult)|| optimizerPatternHexNew.matches(scanResult)) {
            scanResult.replace("^0+".toRegex(), "")
        } else if (optimizerPatternDecimal.matches(scanResult)) {
            scanResult.replaceFirst("000".toRegex(), "")
        } else {
            ToastUtil.showShort("$scanResult,优化器id错误，请重新扫码！")
            return
        }

    val mutableMap = mutableMapOf(
        "chipId" to resultValue,
        "model" to "gd",
        "powerStationId" to AppGlobal.powerStationId,
        "createUserId" to AppGlobal.mId,
        "producers" to "ymx",
        "serialNo" to resultValue,
        "componentNo" to resultValue,
        "belongsGroupId" to groupId
    )

    // 调用添加优化器的API
    equipmentViewModel.addOrModOptimizer(
        mutableMap,
        errorBlock = {
            ToastUtil.showShort("$resultValue 添加失败！")
        }
    ) {
        ToastUtil.showShort("$resultValue 添加成功！")
        onSuccess()
    }
}

@Composable
fun ScanViewPage(
    navController: NavHostController,
    groupId: String = ""
) {
    // 保存应用上下文以便使用原生手电筒功能
    rememberApplicationContext()
    
    // 创建EquipmentViewModel实例
    val equipmentViewModel = remember { EquipmentViewModel() }
    
    // 最近添加的ID
    val lastAddedId = remember { mutableStateOf("") }
    
    SetStatusBar(Color.White,true)

    Scaffold(
        topBar={
            TopBar("",
                backClick = { navController.popBackStack() },
                topContainerColor = Color.Transparent,
                backButtonColor = Color.White
            )
        }
    ){
        val snackbarHostState = remember() { SnackbarHostState() }
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
        ) {
            // 扫码框
            val scope = rememberCoroutineScope()
            var isScanStop=false
            ScannerWithPermissions(
                modifier = Modifier.fillMaxSize(),
                onScanned = { scanResult ->
                    scope.launch {
                        // 获取当前路由
                        val currentRoute = navController.currentBackStackEntry?.destination?.route
                        
                        // 根据当前路由执行不同的处理逻辑
                        when {
                            // 如果路由包含SCAN并且有groupId参数，处理优化器扫描
                            currentRoute?.startsWith(Route.SCAN) == true && groupId.isNotEmpty() -> {
                                handleOptimizerScan(
                                    scanResult = scanResult,
                                    groupId = groupId,
                                    equipmentViewModel = equipmentViewModel
                                ) {
                                    lastAddedId.value = scanResult
                                }
                            }
                            // 验证扫描
                            currentRoute == Route.VERIFY_SCAN -> {
                                // 处理验证扫描
                                handleVerifyScan(scanResult, equipmentViewModel)
                            }

                            currentRoute == Route.TEST_SCAN -> {
                                // 处理验证扫描
                                if(!isScanStop){
                                    ToastUtil.showShort("扫描成功:$scanResult")
                                    // 将扫描结果传回上一个页面
                                    navController.previousBackStackEntry?.savedStateHandle?.set("scanResult", scanResult)
                                    // 返回上一页
                                    navController.popBackStack()
                                }
                                isScanStop=true
                            }
                            // 默认处理方式
                            else -> {
                                val resultValue =
                                    if (optimizerPatternHex.matches(scanResult) || optimizerPatternHex1.matches(scanResult)|| optimizerPatternHexNew.matches(scanResult)) {
                                        scanResult.replace("^0+".toRegex(), "")
                                    } else if (optimizerPatternDecimal.matches(scanResult)) {
                                        scanResult.replaceFirst("000".toRegex(), "")
                                    } else {
                                        scanResult
                                    }
                                // 避免重复扫描，返回错误的页面
                                if(!isScanStop){
                                    ToastUtil.showShort("扫描成功:$resultValue")
                                    // 将扫描结果传回上一个页面
                                    navController.previousBackStackEntry?.savedStateHandle?.set("scanResult", resultValue)
                                    // 返回上一页
                                    navController.popBackStack()
                                }
                                isScanStop=true
                            }
                        }
                    }
                    isScanStop
                },
                types = listOf(CodeType.QR,CodeType.Code128),
                cameraPosition = CameraPosition.BACK
            )

            // 扫码框边框
            Box(
                modifier = Modifier.fillMaxSize().padding(top = 100.dp),
                contentAlignment = Alignment.TopCenter
            ) {
                Image(
                    painter = painterResource(Res.drawable.scan_box),
                    contentDescription = "扫码框",
                    modifier = Modifier.size(240.dp)
                )
            }

            // 提示文字
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.Center)
                    .padding(top = 20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "请扫描设备二维码",
                    color = Color.White,
                    fontSize = 16.sp
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "如果没有二维码，请点击手动输入",
                    color = Color.White,
                    fontSize = 14.sp
                )
                
                // 显示最近添加的ID
                if (lastAddedId.value.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "最近添加的ID: ${lastAddedId.value}",
                        color = Color.Green,
                        fontSize = 16.sp
                    )
                }
            }

            // 底部手电筒按钮
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 150.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 添加手电筒状态
                var flashlightOn by remember { mutableStateOf(false) }
                Box(
                    modifier = Modifier
                        .size(60.dp)
                        .clip(CircleShape)
                        .background(if (flashlightOn) Color.White.copy(alpha = 0.3f) else Color.Transparent)
                        .clickable {
                            try {
                                // 开关手电筒
                                flashlightOn = !flashlightOn
                                togglePlatformFlashlight(flashlightOn)
                                if (flashlightOn) {
//                                    ToastUtil.showShort("手电筒已开启")
                                } else {
//                                    ToastUtil.showShort("手电筒已关闭")
                                }
                            } catch (e: Exception) {
                                // 处理可能的异常
                                ToastUtil.showShort("无法控制手电筒: ${e.message}")
                                // 恢复状态
                                flashlightOn = !flashlightOn
                            }
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painter = painterResource(
                            Res.drawable.scan_flashlight),
                        contentDescription = "手电筒",
                        modifier = Modifier.size(30.dp),
                        colorFilter = ColorFilter.tint(if (flashlightOn) Color.Yellow else Color.White)
                    )
                }
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = if (flashlightOn) "轻触关闭" else "轻触照亮",
                    color = Color.White,
                    fontSize = 14.sp
                )
            }

            // 底部按钮栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 50.dp),
                horizontalArrangement = Arrangement.SpaceAround
            ) {
                // 手动输入按钮
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(
                        modifier = Modifier
                            .size(50.dp)
                            .clip(RoundedCornerShape(25.dp))
                            .background(Color.White.copy(alpha = 0.2f))
                            .clickable {
                                // 获取当前路由
                                val currentRoute = navController.currentBackStackEntry?.destination?.route
                                // 如果是SCAN路由且有groupId参数，需要跳转到优化器新建页面
                                if (currentRoute?.startsWith(Route.SCAN) == true && groupId.isNotEmpty()) {
                                    // 然后导航到优化器新建页面
                                    navController.navigate(Route.OPTIMIZER_NEW + "/${groupId}/2")
                                } else {
                                    // 其他情况直接返回上一页
                                    navController.popBackStack()
                                }
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(
                                Res.drawable.scan_input),
                            contentDescription = "手动输入",
                            modifier = Modifier.size(26.dp),
                            colorFilter = ColorFilter.tint(Color.White)
                        )
                    }
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "手动输入",
                        color = Color.White,
                        fontSize = 12.sp,
                        textAlign = TextAlign.Center
                    )
                }

                // 相册导入按钮
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(
                        modifier = Modifier
                            .size(50.dp)
                            .clip(RoundedCornerShape(25.dp))
                            .background(Color.White.copy(alpha = 0.2f))
                            .clickable {
                                // 打开相册
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(
                                Res.drawable.scan_upload),
                            contentDescription = "相册导入",
                            modifier = Modifier.size(26.dp),
                            colorFilter = ColorFilter.tint(Color.White)
                        )
                    }
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "相册导入",
                        color = Color.White,
                        fontSize = 12.sp,
                        textAlign = TextAlign.Center
                    )
                }
            }
            SnackbarHost(
                modifier = Modifier.align(Alignment.BottomCenter).padding(bottom = 20.dp),
                hostState = snackbarHostState
            )
        }

    }

}