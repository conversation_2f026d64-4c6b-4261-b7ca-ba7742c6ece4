package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

@Composable
fun ConfirmButton( showText: String,
                   enabledBoolean: Boolean,
                   onItemClick: () -> Unit)
{
    Box(
        modifier = Modifier
            .height(50.dp)
            .background(
                brush = Brush.verticalGradient(
                    0.4468f to Color(0xFFEF5864),
                    100f to Color(0xFFE60012),
                ),
                shape = RoundedCornerShape(25.dp)
            )
    ){

        Button(
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.Transparent,
                contentColor = Color.Transparent,
                disabledContainerColor = Color.Transparent,
                disabledContentColor = Color.Transparent
            ),
            enabled = enabledBoolean,
            onClick = {
                onItemClick()}
        ) {
            Text(text = showText,
                color = Color.White
            )
        }
    }

}