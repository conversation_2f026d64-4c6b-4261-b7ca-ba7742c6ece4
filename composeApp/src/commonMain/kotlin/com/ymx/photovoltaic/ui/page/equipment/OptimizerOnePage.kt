package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.platform.ToastUtil
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.PlaceholderText
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.ui.page.widget.rememberSingleColumnPickerState
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.error_invalid_input
import photovoltaic_kmp_app.composeapp.generated.resources.error_optimizer_model_empty
import photovoltaic_kmp_app.composeapp.generated.resources.error_optimizer_sn_empty
import photovoltaic_kmp_app.composeapp.generated.resources.error_optimizer_sn_invalid_chars
import photovoltaic_kmp_app.composeapp.generated.resources.error_optimizer_sn_invalid_value
import photovoltaic_kmp_app.composeapp.generated.resources.error_optimizer_sn_starts_with_zero
import photovoltaic_kmp_app.composeapp.generated.resources.input_or_scan_optimizer_sn
import photovoltaic_kmp_app.composeapp.generated.resources.operation_failed
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer_info
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer_model
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer_sn
import photovoltaic_kmp_app.composeapp.generated.resources.please_select
import photovoltaic_kmp_app.composeapp.generated.resources.right_arrow
import photovoltaic_kmp_app.composeapp.generated.resources.save
import photovoltaic_kmp_app.composeapp.generated.resources.scan
import photovoltaic_kmp_app.composeapp.generated.resources.status
import photovoltaic_kmp_app.composeapp.generated.resources.turn_off
import photovoltaic_kmp_app.composeapp.generated.resources.turn_on

@Composable
fun OptimizerOnePage(
    navHostController: NavHostController,
    equipmentViewModel: EquipmentViewModel = getKoin().get(),
    id: String,
    chipId: String,
    model: String,
    status: String,
    groupId: String
) {
    var chipIdValue by remember { mutableStateOf(chipId) }
    var modelValue by remember { mutableStateOf(model) }
    val statusValue by remember { mutableStateOf(status) }
    
    // Error states
    var chipIdError by remember { mutableStateOf<String?>(null) }
    var modelError by remember { mutableStateOf<String?>(null) }

    val scanResult =  navHostController.currentBackStackEntry?.savedStateHandle?.
    getStateFlow("scanResult","")?.collectAsState()

    LaunchedEffect (scanResult?.value) {
        val  result=scanResult?.value.toString()
        if(result.isNotEmpty())
        {
            val validChars = setOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f')
             chipIdValue = result.lowercase().filter { it in validChars }
        }
    }

    // 添加型号选择器状态
    val modelPicker = rememberSingleColumnPickerState()
    
    // 型号列表
    val modelRange = remember { listOf("gd", "st") }
    
    // 选中的型号索引
    var modelIndex by remember { 
        mutableStateOf(if (model.equals("gd", ignoreCase = true)) 0 else if (model.equals("st", ignoreCase = true)) 1 else -1) 
    }
    // 如果 modelIndex 是 -1 (即 model 不是 "gd" 或 "st")，则 modelValue 初始为空字符串
    if (modelIndex == -1) {
        modelValue = ""
    }

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.optimizer_info),
                backClick = { navHostController.popBackStack() })
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 60.dp)
        ) {
            var showSuccessDialog by remember { mutableStateOf(false) }
            if (showSuccessDialog) {
                ResultDialog(true) { showSuccessDialog = false }
            }

            var showFailDialog by remember { mutableStateOf(false) }
            if (showFailDialog) {
                ResultDialog(false) { showFailDialog = false }
            }

            Spacer(modifier = Modifier.height(12.dp))


            val modelTitle = stringResource(Res.string.optimizer_model)
            // 型号选择
            CommonTitleField(
                value = if (modelValue.isEmpty()) "" else modelValue.uppercase(), // Display in uppercase
                onValueChange = { }, // Not needed as it's read-only
                titleText = modelTitle,
                isSelected = modelValue.isNotEmpty(),
                placeholderCom = {
                    PlaceholderText(Res.string.please_select)
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                isReadOnly = true,
                isError = modelError != null,
                errorText = modelError ?: "",
                onBoxClick = {
                    modelPicker.show(
                        title = modelTitle,
                        range = modelRange.map { it.uppercase() }, // Show options in uppercase
                        value = if (modelIndex < 0) 0 else modelIndex // Default to first if invalid
                    ) { index ->
                        modelIndex = index
                        modelValue = modelRange[index]
                        modelError = null // Clear error on selection
                    }
                },
                trailingIconCom = {
                    Image(
                        painter = painterResource(Res.drawable.right_arrow),
                        modifier = Modifier.size(18.dp),
                        contentDescription = "Select Model"
                    )
                }
            )

                // 优化器S/N输入
                CommonTitleField(
                    value = chipIdValue,
                    onValueChange = { newValue ->
                        // 只保留有效字符
                        val validChars = setOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                            'a', 'b', 'c', 'd', 'e', 'f')
                        chipIdValue = newValue.lowercase().filter { it in validChars }
                        chipIdError = null // Clear error when user types
                    },
                    titleText = stringResource(Res.string.optimizer_sn),
                    placeholderCom = {
                        PlaceholderText(Res.string.input_or_scan_optimizer_sn)
                    },
                    modifier = Modifier.padding(top = 5.dp),
                    textFieldHeight = 48,
                    cornerRadius = 30,
                    titleBottom = 10,
                    textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                    keyboardOptions = KeyboardOptions(
                        capitalization = KeyboardCapitalization.None,
                        autoCorrectEnabled = false,
                        keyboardType = KeyboardType.Text
                    ),
                    isError = chipIdError != null,
                    errorText = chipIdError ?: "",
                    trailingIconCom = {
                        Image(
                            painter = painterResource(Res.drawable.scan),
                            modifier = Modifier.size(18.dp).clickable {
                                // 跳转到扫描页面
                                navHostController.currentBackStackEntry?.savedStateHandle?.set("scanType", "optimizer")
                                navHostController.navigate(Route.SCAN)
                            },
                            contentDescription = "Scan SN"
                        )
                    }
                )



                // 添加状态显示，仅在编辑时显示（id不为空时）
                if (id.isNotEmpty()) {
                    CommonTitleField(
                        value = when(statusValue) {
                            "1" -> stringResource(Res.string.turn_on)
                            "2" -> stringResource(Res.string.turn_off)
                            else -> ""
                        },
                        onValueChange = { },  // 不允许修改
                        titleText = stringResource(Res.string.status),
                        placeholderCom = { },  // 空占位符
                        isReadOnly = true,
                        textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                        modifier = Modifier.padding(top = 5.dp),
                        textFieldHeight = 48,
                        cornerRadius = 30,
                        titleBottom = 10
                    )
                }

            Spacer(modifier = Modifier.weight(1f))

            val coroutineScope = rememberCoroutineScope()
            val errorMsgEmptySn = stringResource(Res.string.error_optimizer_sn_empty)
            val errorMsgZeroStart = stringResource(Res.string.error_optimizer_sn_starts_with_zero)
            val errorMsgInvalidChars = stringResource(Res.string.error_optimizer_sn_invalid_chars)
            val errorMsgInvalidValue = stringResource(Res.string.error_optimizer_sn_invalid_value)
            val errorMsgEmptyModel = stringResource(Res.string.error_optimizer_model_empty)
            val errorMsgInvalidInput = stringResource(Res.string.error_invalid_input)
            // 将操作失败的字符串资源移到 Composable 上下文
            val operationFailedMsg = stringResource(Res.string.operation_failed)


            ConfirmButton(stringResource(Res.string.save), true) {
                // Reset errors
                chipIdError = null
                modelError = null
                var hasError = false

                val trimmedChipId = chipIdValue.trim()
                val trimmedModel = modelValue.trim()

                // Validate Chip ID
                if (trimmedChipId.isEmpty()) {
                    chipIdError = errorMsgEmptySn
                    hasError = true
                } else {
                    val validChars = setOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                        'a', 'b', 'c', 'd', 'e', 'f')
                    // 检查是否以0开头
                    if (trimmedChipId.startsWith('0')) {
                        chipIdError = errorMsgZeroStart
                        hasError = true
                    }
                    // 检查是否包含无效字符 (already filtered, but double-check)
                    else if (trimmedChipId.any { it !in validChars }) {
                        chipIdError = errorMsgInvalidChars
                        hasError = true
                    }
                    // 检查16进制值
                    else {
                        try {
                            val decimalValue = trimmedChipId.toLong(16)
                            if (decimalValue <= 20000) {
                                chipIdError = errorMsgInvalidValue
                                hasError = true
                            }
                        } catch (e: NumberFormatException) {
                            chipIdError = errorMsgInvalidChars // Treat format error as invalid chars
                            hasError = true
                        }
                    }
                }

                // Validate Model
                if (trimmedModel.isEmpty()) {
                    modelError = errorMsgEmptyModel
                    hasError = true
                }

                if (hasError) {
                    return@ConfirmButton
                }

                val mutableMap = mutableMapOf(
                    "chipId" to trimmedChipId,
                    "model" to trimmedModel,
                    "status" to statusValue
                )

                if (id.isNotEmpty()) {
                    mutableMap["id"] = id
                } else {
                    mutableMap["powerStationId"] = AppGlobal.powerStationId
                    mutableMap["createUserId"] = AppGlobal.mId
                    mutableMap["producers"] = "ymx"
                    mutableMap["serialNo"] = trimmedChipId
                    mutableMap["componentNo"] = trimmedChipId
                    mutableMap["belongsGroupId"] = groupId
                }


                equipmentViewModel.addOrModOptimizer(mutableMap,
                    // 移除 error 参数，并使用预先获取的字符串
                    errorBlock = {
                        ToastUtil.showShort(operationFailedMsg) // 直接使用获取的字符串
                        showFailDialog = true
                        coroutineScope.launch {
                            delay(2000)
                            showFailDialog = false
                        }
                    }
                ) {
                    showSuccessDialog = true
                    coroutineScope.launch {
                        delay(2000)
                        showSuccessDialog = false
                        navHostController.popBackStack()
                    }
                }
            }
        }
    }
}