package com.ymx.photovoltaic.ui.page.user

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.ui.page.widget.DealerItem
import com.ymx.photovoltaic.ui.page.widget.TitleItem
import com.ymx.photovoltaic.ui.page.widget.TopBar
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.account_type
import photovoltaic_kmp_app.composeapp.generated.resources.account_type_installer
import photovoltaic_kmp_app.composeapp.generated.resources.account_type_installer_desc
import photovoltaic_kmp_app.composeapp.generated.resources.account_type_owner
import photovoltaic_kmp_app.composeapp.generated.resources.account_type_owner_desc
import photovoltaic_kmp_app.composeapp.generated.resources.register_account
import photovoltaic_kmp_app.composeapp.generated.resources.register_installer
import photovoltaic_kmp_app.composeapp.generated.resources.register_owner

/**
 * 账号类型选择页面
 * 
 * 该页面允许用户选择注册为业主或安装商账号类型
 */
@Composable
fun AccountTypePage(
    navHostController: NavHostController
) {
    // 设置状态栏
    SetStatusBar(Color.White, true)
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // 顶部导航栏
        TopBar(
            textStr = stringResource(Res.string.register_account),
            backClick = { navHostController.popBackStack() }
        )
        
        // 主要内容
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp)
        ) {
           
        
            // 账号类型标题
            TitleItem(
                text = stringResource(Res.string.account_type)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            
            // 安装商选项
            DealerItem(
                imagePainter = painterResource(Res.drawable.register_installer),
                title = stringResource(Res.string.account_type_installer),
                description = stringResource(Res.string.account_type_installer_desc),
                onClick = {
                    // 导航到注册页面，并传递账号类型参数
                    navHostController.navigate("${Route.REGISTER}?accountType=installer")
                }
            )

              // 业主选项
              DealerItem(
                imagePainter = painterResource(Res.drawable.register_owner),
                title = stringResource(Res.string.account_type_owner),
                description = stringResource(Res.string.account_type_owner_desc),
                onClick = {
                    // 导航到注册页面，并传递账号类型参数
                    navHostController.navigate("${Route.REGISTER}?accountType=owner")
                }
            )
        }
    }
}
