package com.ymx.photovoltaic.ui.page.home.edit

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.common.CommonBottomBar
import com.ymx.photovoltaic.ui.page.theme.Grey_38
import com.ymx.photovoltaic.ui.page.theme.Grey_69
import com.ymx.photovoltaic.ui.page.theme.Grey_D6
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.theme.LjRed
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.EditViewModel
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.contact_way
import photovoltaic_kmp_app.composeapp.generated.resources.delay_shutdown
import photovoltaic_kmp_app.composeapp.generated.resources.ic_right_arrow
import photovoltaic_kmp_app.composeapp.generated.resources.info_edit
import photovoltaic_kmp_app.composeapp.generated.resources.power
import photovoltaic_kmp_app.composeapp.generated.resources.power_station_system
import photovoltaic_kmp_app.composeapp.generated.resources.red_line
import photovoltaic_kmp_app.composeapp.generated.resources.region
import photovoltaic_kmp_app.composeapp.generated.resources.shade_ratio
import photovoltaic_kmp_app.composeapp.generated.resources.shade_warning
import photovoltaic_kmp_app.composeapp.generated.resources.street_name
import photovoltaic_kmp_app.composeapp.generated.resources.sunDowntime
import photovoltaic_kmp_app.composeapp.generated.resources.sunUptime
import photovoltaic_kmp_app.composeapp.generated.resources.system_name
import photovoltaic_kmp_app.composeapp.generated.resources.temp_shutdown
import photovoltaic_kmp_app.composeapp.generated.resources.temp_warning
import photovoltaic_kmp_app.composeapp.generated.resources.temp_warning_set
import photovoltaic_kmp_app.composeapp.generated.resources.type_2_4g
import photovoltaic_kmp_app.composeapp.generated.resources.type_plc
import kotlin.math.roundToInt


@Composable
fun EditPage(
    navHostController: NavHostController,
    editViewModel: EditViewModel = getKoin().get()
) {

    val station by editViewModel.stationFlow.collectAsState()
    val warningSetting by editViewModel.warningSettingFlow.collectAsState()

    LaunchedEffect(Unit) {
        editViewModel.fetchStation(AppGlobal.powerStationId)
        editViewModel.fetchWarningSetting(AppGlobal.powerStationId)
    }

    Scaffold(
        topBar = {
            TopBar(
                stringResource(Res.string.info_edit),
                backClick = { navHostController.navigate(Route.HOME) },
                true
            )
        },
        containerColor = Grey_F5,
        bottomBar = {
            CommonBottomBar(navController = navHostController, isFirstMenu = false)
        }
    ) {
            paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues).padding(start = 10.dp, end = 10.dp, top = 15.dp)
        ) {


            Spacer(modifier = Modifier.height(5.dp))

            AlertSection(
                title = stringResource(Res.string.power_station_system),
                items = listOf(
                    stringResource(Res.string.system_name) to station?.systemName.toString(),
                    stringResource(Res.string.region) to "${station?.countries}/${station?.provinceName}/${station?.cityName}",
                    stringResource(Res.string.street_name) to station?.streetName.toString(),
                    stringResource(Res.string.power) to station?.power.toString(),
                    stringResource(Res.string.contact_way) to if(station?.type==1) stringResource(Res.string.type_2_4g) else stringResource(Res.string.type_plc),
                    stringResource(Res.string.sunUptime) to station?.sunuptime.toString(),
                    stringResource(Res.string.sunDowntime) to station?.sundowntime.toString()
                )
            ){
                navHostController.navigate(Route.STATION_INFO)
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 第三部分：温度警报
            AlertSection(
                title = stringResource(Res.string.temp_warning),
                items = listOf(
                    stringResource(Res.string.temp_warning_set) to "≥ ${warningSetting?.componentTemperature}°C",
                    stringResource(Res.string.temp_shutdown) to "≥ ${warningSetting?.offValue}°C"
                )
            ){
                navHostController.navigate(Route.TEMP_WARN)
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 第四部分：遮挡警报
            AlertSection(
                title = stringResource(Res.string.shade_warning),
                items = listOf(
                    stringResource(Res.string.shade_ratio) to "≥ ${warningSetting?.keepOutRate?.roundToInt()}%",
                    stringResource(Res.string.delay_shutdown) to "≥ ${warningSetting?.keepOutTime}Min"
                )
            ) {
                navHostController.navigate(Route.KEEP_WARN)
            }
        }

    }

}



@Composable
fun EditTitleItem(
    text: String,
    onClick: () -> Unit
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.clickable { onClick() }
    ) {
        Icon(
            painter = painterResource(Res.drawable.red_line),
            contentDescription = "",
            modifier = Modifier.size(12.dp),
            tint = LjRed
        )
        Spacer(modifier = Modifier.width(4.dp))

        Text(text = text, fontSize = 18.sp, color = Grey_38)

        Spacer(modifier = Modifier.weight(1f))
        Icon(
            painter = painterResource(Res.drawable.ic_right_arrow),
            contentDescription = "箭头",
            tint = Color.Gray,
            modifier = Modifier.size(12.dp)
        )
    }
}


@Composable
fun InfoItem(label: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(text = label, fontSize = 16.sp,color = Grey_38)
        Text(text = value, fontSize = 16.sp, color = Grey_69)
    }

    HorizontalDivider(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 3.dp, bottom = 8.dp), // 使分割线垂直填充整个Row的高度
        thickness = 1.dp, // 分割线厚度
        color = Grey_D6 // 分割线颜色
    )
}

@Composable
fun AlertSection(title: String, items: List<Pair<String, String>>, onClick: () -> Unit) {
    Column(
        modifier = Modifier
            .background(Color.White, shape = RoundedCornerShape(16.dp))
            .fillMaxWidth()
            .padding(start = 16.dp, end = 16.dp, top = 16.dp)
    ) {
        EditTitleItem(
            text = title,
            onClick = onClick
        )

        Spacer(modifier = Modifier.height(12.dp))

        items.forEach { (label, value) ->
            InfoItem(label = label, value = value)
        }

        Spacer(modifier = Modifier.height(16.dp))

    }
}

