package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.bean.Optimizer
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonSearch
import com.ymx.photovoltaic.ui.page.widget.DeviceItem
import com.ymx.photovoltaic.ui.page.widget.DeviceTextButtonRow
import com.ymx.photovoltaic.ui.page.widget.FloatingButton
import com.ymx.photovoltaic.ui.page.widget.TopBar
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer_config
import photovoltaic_kmp_app.composeapp.generated.resources.input_optimizer_sn

/**
 * 优化器配置页面
 */
@Composable
fun OptimizerConfigScreen(
    navHostController: NavHostController,
    optimizerList: List<Optimizer>
) {

    var searchQuery by remember { mutableStateOf("") } // 用于存储搜索框中的输入

    val filteredItems = optimizerList.filter {
        it.chipId.contains(searchQuery, ignoreCase = true)
        // 只过滤chipId字段
    }

    Scaffold(
        topBar = {
            TopBar(
                textStr = stringResource(Res.string.optimizer_config),
                backClick = { navHostController.popBackStack() },
            )
        },
        containerColor = Grey_F5,
        floatingActionButton = {
//            FloatingButton(
//                onAddClicked = {
//                    navHostController.navigate(Route.OPTIMIZER_NEW)
//                }
//            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 0.dp)
        ) {

            CommonSearch(searchQuery, onQueryChange = { searchQuery = it }, 
                placeholderTextResId = Res.string.input_optimizer_sn,
                modifier = Modifier.
                padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 10.dp).
                fillMaxWidth().height(48.dp)
                .clip(RoundedCornerShape(25.dp)) )

            LazyColumn {
                items(filteredItems) { optimizer ->
                    DeviceItem(
                        imagePainter = painterResource(Res.drawable.optimizer),
                        secondRowText = "型号: ${optimizer.model}",
                        firstRow = {
                            DeviceTextButtonRow(
                                textStr = "ID#:${optimizer.chipId}",
                                isHaveButton = false
                            )
                        },
                        lastRow = {
                            DeviceTextButtonRow(
                                textStr = "创建时间:${optimizer.createTimeCh}",
                                isHaveButton = false,
                                isTextBold = false
                            )
                        }
                    )
                }
            }
        }
    }
} 