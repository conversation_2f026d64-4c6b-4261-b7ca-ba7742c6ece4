package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.common.CommonBottomBar
import com.ymx.photovoltaic.ui.page.home.PowerStationList
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonSearch
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.equipment_management


@Composable
fun EquipmentListPage(
    navHostController: NavHostController,
    equipmentViewModel: EquipmentViewModel = getKoin().get()
) {
    val stationList by equipmentViewModel.stationListFlow.collectAsState()
    val warningTypeCount by equipmentViewModel.warningTypeCountFlow.collectAsState()

    LaunchedEffect(Unit) {
        equipmentViewModel.fetchStationList(AppGlobal.mId, "2")
    }

    // 获取电站ID列表并查询警告类型计数
    LaunchedEffect(stationList) {
        val powerIdList = stationList.map { it.id }
        if (powerIdList.isNotEmpty()) {
            equipmentViewModel.fetchWarningTypeCount(powerIdList)
        }
    }

    var searchQuery by remember { mutableStateOf("") } // 用于存储搜索框中的输入

    val filteredItems = stationList.filter {
        it.systemName.contains(searchQuery, ignoreCase = true)
        // 只过滤名字字段
    }

    // 获取异常电站ID列表
    val exceptionStationIds = warningTypeCount?.stationIds ?: emptyList()

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.equipment_management), backClick = {},false)
        },
        containerColor = Grey_F5,
        bottomBar = {
            CommonBottomBar(navController = navHostController)
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier.
            padding(paddingValues)
        ) {
            Spacer(modifier = Modifier.height(20.dp))
            CommonSearch(searchQuery, onQueryChange = { searchQuery = it }, modifier = Modifier.
            padding(start = 10.dp, end = 10.dp).fillMaxWidth().height(48.dp)
                .clip(RoundedCornerShape(25.dp)) )
            Spacer(modifier = Modifier.height(10.dp))
            PowerStationList(
                stationList = filteredItems,
                navHostController = navHostController, 
                page = Route.EQUIPMENT,
                exceptionStationIds = exceptionStationIds,
                from = "equipment"
            )
        }
    }
}



