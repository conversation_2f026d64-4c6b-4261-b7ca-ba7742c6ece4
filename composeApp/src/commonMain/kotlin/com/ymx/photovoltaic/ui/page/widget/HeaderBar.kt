package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.add
import photovoltaic_kmp_app.composeapp.generated.resources.alarm

/**
 * 渐变背景顶部导航栏
 * 
 * @param title 标题
 * @param temperature 温度
 * @param location 位置
 * @param searchQuery 搜索框文本
 * @param onSearchQueryChange 搜索框文本变化回调
 * @param onAddClick 添加按钮点击回调
 * @param onAlarmClick 警报按钮点击回调
 * @param districtIds 区域ID列表
 * @param onLocationChange 位置变化回调
 */
@Composable
fun HeaderBar(
    title: String,
    temperature: String,
    location: String,
    searchQuery: String = "",
    onSearchQueryChange: (String) -> Unit = {},
    onAddClick: () -> Unit = {},
    onAlarmClick: () -> Unit = {},
    districtIds: List<String> = emptyList(),
    onLocationChange: (String) -> Unit = {}
) {
    // 渐变背景颜色 - 根据第二个截图中的颜色
    val gradientBackground = Brush.verticalGradient(
        colors = listOf(
            Color(0xFFFEB9A9),  // rgba(255, 87, 51, 1) - 渐变开始颜色
            Color(0x00FFFFFF)   // 完全透明的白色 - 渐变结束颜色
        ),
        startY = 0f,
        endY = 300f
    )
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(gradientBackground).statusBarsPadding()
    ) {
        // 顶部信息区域
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 10.dp, end = 5.dp, top = 10.dp, bottom =20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧区域（位置和温度）
            Column(
//                modifier = Modifier.padding(start = 10.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = location,
                        color = Color.Black,
                        fontSize = 15.sp
                    )
                    
                    // Dropdown menu state
                    var expanded by remember { mutableStateOf(false) }
                    
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "下拉菜单",
                        modifier = Modifier
                            .padding(start = 4.dp)
                            .size(15.dp)
                            .clickable { expanded = true }
                    )
                    
                    // Dropdown menu
                    DropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false },
                        modifier = Modifier
                            .background(Color.White)
                            .heightIn(max = 200.dp) // 固定最大高度
                    ) {
                        districtIds.forEachIndexed { index, districtId ->
                            DropdownMenuItem(
                                text = { 
                                    Text(
                                        text = districtId,
                                        textAlign = TextAlign.Center,
                                        modifier = Modifier.fillMaxWidth()
                                    )
                                },
                                onClick = { 
                                    expanded = false
                                    onLocationChange(districtId) // 更新位置
                                }
                            )
                            
                            // 添加分隔线，但最后一项后不添加
                            if (index < districtIds.size - 1) {
                                HorizontalDivider(
                                    modifier = Modifier.padding(horizontal = 16.dp),
                                    thickness = 0.5.dp,
                                    color = Color.LightGray
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = temperature,
                    color = Color.Gray,
                    fontSize = 14.sp
                )
            }

                CommonSearchWithIconAndText(
                    searchQuery = searchQuery,
                    onQueryChange = onSearchQueryChange,
                    modifier = Modifier
                        .padding(start = 7.dp, end = 5.dp)
                        // 移除固定宽度，使用.weight(1f) 保证宽度尽可能宽
                        .weight(1f)
                        .height(42.dp)
                        .clip(RoundedCornerShape(25.dp)),
                    isTrailingIcon = false,
                    placeholderTextSize = 14.sp
                )
                
                // 告警图标
                Icon(
                    painter = painterResource(Res.drawable.alarm),
                    contentDescription = "告警",
                    modifier = Modifier
                        .size(24.dp)
                        .clickable { onAlarmClick() },
                    tint = Color(89,89,89,255)
                )

            Spacer(modifier = Modifier.width(5.dp))

            Icon(
                    painter = painterResource(Res.drawable.add),
                    contentDescription = "添加",
                    modifier = Modifier
                        .size(24.dp)
                        .clickable { onAddClick() },
                tint = Color(89,89,89,255)
                )
            }
    }
}