package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.ui.page.theme.Grey_3B
import com.ymx.photovoltaic.ui.page.theme.Grey_80
import org.jetbrains.compose.resources.StringResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.ic_search
import photovoltaic_kmp_app.composeapp.generated.resources.input_power_name
import photovoltaic_kmp_app.composeapp.generated.resources.search



@Composable
fun CommonSearch(
    searchQuery: String,
    onQueryChange: (String) -> Unit,
    placeholderTextResId: StringResource = Res.string.input_power_name,
    modifier: Modifier,
    isTrailingIcon:Boolean=true,
    placeholderTextSize: TextUnit=15.sp,
    placeholderTextAlign: TextAlign=TextAlign.Center
) {
    var inputText by remember(searchQuery) { mutableStateOf(searchQuery) } // Local state for input

    val keyboardController = LocalSoftwareKeyboardController.current

    TextField(
        value = inputText, // Use local state for value
        onValueChange = { inputText = it }, // Update local state on value change
        modifier = modifier,
        colors = TextFieldDefaults.colors(
            focusedContainerColor = Color.White,
            unfocusedContainerColor = Color.White,
            focusedIndicatorColor = Color.Transparent,
            unfocusedIndicatorColor = Color.Transparent,
            disabledIndicatorColor = Color.Transparent
        ),
        placeholder = {
            PlaceholderText(
                textResId = placeholderTextResId,
                textFontSize = placeholderTextSize,
                textColor = Grey_80,
                textAlign = placeholderTextAlign,
                textLineHeight = placeholderTextSize
            )
        },
        singleLine = true,
        maxLines = 1,
        textStyle = TextStyle(
            color = Color.Black
        ),
        leadingIcon = {
            Icon(
                painter = painterResource( Res.drawable.ic_search),
                contentDescription = "Search Icon",
                tint = Color.Gray
            )
        },
        trailingIcon = {
            if(isTrailingIcon)
            {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Divider(
                        modifier = Modifier
                            .height(20.dp)
                            .width(1.dp),
                        color = Color.LightGray
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = stringResource(Res.string.search),
                        color = Grey_3B,
                        fontSize = 16.sp,
                        modifier = Modifier
                            .clickable { onQueryChange(inputText) } // Trigger search ONLY on click, pass current input
                            .padding(end = 20.dp) // Padding for touch target
                    )
                }
            }

        },
        keyboardOptions = KeyboardOptions.Default.copy(imeAction = ImeAction.Search),
        keyboardActions = KeyboardActions(onSearch = {
            // 可以在此处处理额外的搜索逻辑
            onQueryChange(inputText)
            keyboardController?.hide()
        })
    )
}

@Composable
fun CommonSearchWithIconAndText(
    searchQuery: String,
    onQueryChange: (String) -> Unit,
    placeholderTextResId: StringResource = Res.string.input_power_name,
    modifier: Modifier,
    isTrailingIcon: Boolean = true,
    placeholderTextSize: TextUnit = 15.sp,
    placeholderTextColor: Color = Grey_80,
    placeholderTextAlign: TextAlign = TextAlign.Start
) {
    var inputText by remember(searchQuery) { mutableStateOf(searchQuery) }

    val keyboardController = LocalSoftwareKeyboardController.current

    // 定义文本样式
    val textStyle = TextStyle(
        color = Color.Black,
        textAlign = TextAlign.Left,
        fontSize = 13.sp,
        lineHeight = 18.sp  // 增加行高以确保文本完整显示
    )
    
    // 使用BasicTextField并自定义装饰
    BasicTextField(
        value = inputText,
        onValueChange = { inputText = it },
        modifier = modifier,
        textStyle = textStyle,
        singleLine = true,
        keyboardOptions = KeyboardOptions.Default.copy(imeAction = ImeAction.Search),
        keyboardActions = KeyboardActions(onSearch = {
            onQueryChange(inputText)
            keyboardController?.hide()
        }),
        decorationBox = { innerTextField ->
            // 创建自定义装饰框
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, RoundedCornerShape(4.dp))
                    .padding(top = 12.dp, bottom = 8.dp) // 使用相同的上下内边距确保垂直居中
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically, // 确保行内所有元素垂直居中
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 前导图标
                    Icon(
                        painter = painterResource(Res.drawable.ic_search),
                        contentDescription = "Search Icon",
                        tint = Color.Gray,
                        modifier = Modifier.padding(start = 12.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    // 文本字段和占位符的容器
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.CenterStart // 确保内容左对齐且垂直居中
                    ) {
                        innerTextField()
                        
                        // 如果文本框中没有输入内容，则显示占位文本
                        if (inputText.isEmpty()) {
                            PlaceholderText(
                                textResId = placeholderTextResId,
                                textFontSize = placeholderTextSize,
                                textColor = placeholderTextColor,
                                textAlign = placeholderTextAlign,
                                textLineHeight = placeholderTextSize
                            )
                        }
                    }
                    
                    // 尾部图标
                    if (isTrailingIcon) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Divider(
                                modifier = Modifier
                                    .height(20.dp)
                                    .width(1.dp),
                                color = Color.LightGray
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = stringResource(Res.string.search),
                                color = Grey_3B,
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .clickable { onQueryChange(inputText) }
                                    .padding(end = 20.dp)
                            )
                        }
                    }
                }
            }
        }
    )
}
