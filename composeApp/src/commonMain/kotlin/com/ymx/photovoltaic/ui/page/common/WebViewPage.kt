package com.ymx.photovoltaic.ui.page.common

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.navigation.NavHostController
import com.multiplatform.webview.web.WebView
import com.multiplatform.webview.web.rememberWebViewState
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.TopBar

@Composable
fun WebViewPage(
    navController: NavHostController,
    title: String,
    url: String
) {
    // 确保URL不为空
    val state = rememberWebViewState(url)
    SetStatusBar(Color.White,true)

    Scaffold(
        topBar={
            TopBar(title,
                backClick = { navController.popBackStack() },
            )
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        WebView(state, modifier = Modifier.padding(paddingValues).fillMaxSize())
    }
} 