package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.ui.page.theme.Blue_F9
import com.ymx.photovoltaic.ui.page.theme.Green_64
import com.ymx.photovoltaic.ui.page.theme.Grey_Be
import com.ymx.photovoltaic.ui.page.theme.LjRed
import com.ymx.photovoltaic.ui.page.theme.Red_3F
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.capacity_format
import photovoltaic_kmp_app.composeapp.generated.resources.collector_config
import photovoltaic_kmp_app.composeapp.generated.resources.little_photovoltaic
import photovoltaic_kmp_app.composeapp.generated.resources.red_line
import photovoltaic_kmp_app.composeapp.generated.resources.relay_config
import photovoltaic_kmp_app.composeapp.generated.resources.running_days_format
import photovoltaic_kmp_app.composeapp.generated.resources.scan
import photovoltaic_kmp_app.composeapp.generated.resources.station
import photovoltaic_kmp_app.composeapp.generated.resources.status_abnormal
import photovoltaic_kmp_app.composeapp.generated.resources.status_normal
import photovoltaic_kmp_app.composeapp.generated.resources.status_offline
import photovoltaic_kmp_app.composeapp.generated.resources.value_unit_format

// 运行天数
@Composable
private fun RunningDaysText(days: Long) {
    val formatString = stringResource(Res.string.running_days_format)
    val placeholder = "%d"
    val daysText = days.toString()
    val startIndex = formatString.indexOf(placeholder)
    val fullText = formatString.replace(placeholder, daysText)
    val endIndex = if (startIndex != -1) startIndex + daysText.length else -1

    val annotatedText = buildAnnotatedString {
        append(fullText)
        if (startIndex != -1) { // Apply blue color only if placeholder was found
            addStyle(
                style = SpanStyle(color = Blue_F9), // Apply blue color
                start = startIndex,
                end = endIndex
            )
        }
    }

    Text(
        text = annotatedText, // Use the annotated string
        modifier = Modifier
            .padding(horizontal = 6.dp, vertical = 2.dp),
        style = TextStyle(fontSize = 12.sp, color = Color.Gray) // Base style remains Gray
    )
}


@Composable
fun EditImeiItem(
    label: String, value: String, onValueChange: (String) -> Unit,
    onClick: () -> Unit,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    rowHeight: Dp =56.dp
) {
    Row(
        modifier = Modifier.fillMaxWidth().height(rowHeight),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label, fontSize = 16.sp,
            fontWeight = FontWeight.Medium, color = Color.Black,
            modifier = Modifier.wrapContentWidth()
        )

        Spacer(modifier = Modifier.weight(1f))

        EditBasicTextField(value, onValueChange,keyboardOptions,keyboardActions)

        Spacer(modifier = Modifier.width(18.dp))

        Image(painter = painterResource(Res.drawable.scan),
            contentDescription = "",
            modifier = Modifier.clickable {
                onClick()
            })

    }

    HorizontalDivider(
        modifier = Modifier
            .fillMaxWidth().padding(top = 10.dp, bottom = 16.dp),
        // 使分割线垂直填充整个Row的高度
        thickness = 1.dp, // 分割线厚度
        color = Color(245, 245, 245, 255) // 分割线颜色
    )
}

@Composable
fun TitleItem(
    text: String,
    textColor: Color = Color.Black,
) {

    Row(verticalAlignment = Alignment.CenterVertically)
    {
        Icon(
            painter = painterResource(Res.drawable.red_line),
            contentDescription = "",
            modifier = Modifier.size(10.dp),
            tint = LjRed
        )
        Spacer(modifier = Modifier.width(8.dp))

        Text(text = text, fontSize = 15.sp,
            fontWeight = FontWeight.Bold, color = textColor)
    }
}


@Composable
fun ContributionItem(value: String, unit: String, description: String) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {

        Column {
            Text(text = stringResource(Res.string.value_unit_format).replace("%1\$s", value).replace("%2\$s", unit), fontWeight = FontWeight.Bold, fontSize = 20.sp)
            Spacer(modifier = Modifier.height(4.dp))
            Text(text = description, fontSize = 14.sp, color = Color.Gray)
        }
    }
}


@Composable
fun StationItem(cardColor:Color,
                img: DrawableResource, name: String, address: String, capacity: Int, createTime: String,
                days: Long, status: String, onStationClick: ()->Unit
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = cardColor),
//        elevation = CardDefaults.cardElevation(4.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding( top = 8.dp,bottom = 6.dp),
        shape = RoundedCornerShape(8.dp),
    ){
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp).clickable {
                    onStationClick()
                },

            ) {
            Image(
                painter = painterResource(img),
                contentDescription = null,
                modifier = Modifier
                    .size(80.dp)
                    .clip(RoundedCornerShape(8.dp)),
                contentScale = ContentScale.Crop
            )

            Spacer(modifier = Modifier.width(16.dp))

            Column(
                modifier = Modifier
                    .weight(1f)
                    .align(Alignment.CenterVertically)
            ) {
                Row(
                    horizontalArrangement = Arrangement.SpaceBetween,
                    modifier = Modifier.fillMaxWidth()
                ){

                    Text(
                        text = name,
                        style = TextStyle(fontSize = 16.sp)
                    )
                    if(status.isNotEmpty())
            {
                Text(
                    text = status,
                    style = TextStyle(
                        fontSize = 14.sp,
                        color = when (status) {
                            stringResource(Res.string.status_normal) -> Green_64
                            stringResource(Res.string.status_abnormal)  -> Red_3F
                            stringResource(Res.string.status_offline)  -> Grey_Be
                            else -> Green_64
                        }

                    ),
                    modifier = Modifier
                        .align(Alignment.CenterVertically)
                        .clip(RoundedCornerShape(4.dp))
                        .background(
                            color = when (status) {
                                stringResource(Res.string.status_normal) -> Green_64.copy(alpha = 0.2f)
                                stringResource(Res.string.status_abnormal) -> Red_3F.copy(alpha = 0.2f)
                                stringResource(Res.string.status_offline) -> Grey_Be.copy(alpha = 0.2f)
                                else -> Green_64.copy(alpha = 0.2f)
                            }
                        )
                        .padding(horizontal = 2.dp, vertical = 2.dp)
                )
            }
                }

                Spacer(modifier = Modifier.height(6.dp))
                Text(
                    text = address,
                    style = TextStyle(fontSize = 14.sp, color = Color.Gray)
                )
                Spacer(modifier = Modifier.height(6.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                )
                {
                    Row {
                        Image(
                            painter = painterResource(Res.drawable.little_photovoltaic),
                            contentDescription = null,
                            modifier = Modifier
                                .size(20.dp),
                            contentScale = ContentScale.Fit
                        )
                        Text(
                            text = stringResource(Res.string.capacity_format).replace("%d", capacity.toString()),
                            modifier = Modifier.padding(vertical = 4.dp, horizontal = 4.dp),
                            style = TextStyle(fontSize = 12.sp, color = Color.Gray)
                        )
                        VerticalDivider(color = Color.Gray, thickness = 1.dp, modifier = Modifier.padding(top = 6.dp,
                            start = 4.dp, end = 4.dp).
                        height(10.dp))
                        Text(
                            text = createTime,
                            modifier = Modifier.padding(vertical = 4.dp, horizontal = 4.dp),
                            style = TextStyle(fontSize = 12.sp, color = Color.Gray)
                        )
                    }
                    
                    // Call the extracted function, now right-aligned
                    RunningDaysText(days = days)
                }

            }
        }

    }

}


data class CustomMenuItem(
    val title: String,
    val onClick: () -> Unit
)

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ComponentItem(
    type: String,
    titleOne: String,
    titleTwo: String,
    onClick: () -> Unit,
    menuItems: List<CustomMenuItem>,
    onFirstConfigClick: (() -> Unit)? = null,
    onSecondConfigClick: (() -> Unit)? = null
) {
    var showMenu by remember { mutableStateOf(false) }
    Card(
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(4.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding( top = 8.dp,bottom = 6.dp),
        shape = RoundedCornerShape(8.dp),
    ){
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp).combinedClickable (
                    onClick = {onClick()},
                    onLongClick = {
                        showMenu = true
                    }
                )
            ) {
            Image(
                painter = painterResource( Res.drawable.station),
                contentDescription = null,
                modifier = Modifier
                    .size(80.dp)
                    .clip(RoundedCornerShape(8.dp)),
                contentScale = ContentScale.Crop
            )

            Spacer(modifier = Modifier.width(16.dp))

            Column(
                modifier = Modifier
                    .weight(1f)
                    .align(Alignment.CenterVertically)
            ) {
                Text(
                    text = titleOne,
                    style = TextStyle(fontSize = 16.sp)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = titleTwo,
                    style = TextStyle(fontSize = 14.sp, color = Color.Gray)
                )
                
                if (type == "collector") {
                    Spacer(modifier = Modifier.height(16.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Start
                    ) {
                        Text(
                            text = stringResource(Res.string.collector_config),
                            style = TextStyle(fontSize = 14.sp, color = Color(0xFF275ADB)),
                            modifier = Modifier.clickable { 
                                onFirstConfigClick?.invoke()
                            }
                        )
                        Text(
                            text = " | ",
                            style = TextStyle(fontSize = 14.sp, color = Color.Gray)
                        )
                        Text(
                            text = stringResource(Res.string.relay_config),
                            style = TextStyle(fontSize = 14.sp, color = Color(0xFF275ADB)),
                            modifier = Modifier.clickable {
                                onSecondConfigClick?.invoke()
                            }
                        )
                    }
                }
                if (type == "relay") {
                    Spacer(modifier = Modifier.height(16.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Start
                    ) {
                        Text(
                            text = "新增优化器",
                            style = TextStyle(fontSize = 14.sp, color = Color(0xFF275ADB)),
                            modifier = Modifier.clickable {
                                onFirstConfigClick?.invoke()
                            }
                        )
                        Text(
                            text = " | ",
                            style = TextStyle(fontSize = 14.sp, color = Color.Gray)
                        )
                        Text(
                            text = "删除优化器",
                            style = TextStyle(fontSize = 14.sp, color = Color(0xFF275ADB)),
                            modifier = Modifier.clickable {
                                onSecondConfigClick?.invoke()
                            }
                        )
                    }
                }
            }
        }

        DropdownMenu(
            expanded = showMenu,
            onDismissRequest = { showMenu = false },
            offset = DpOffset(x = 200.dp, y = 0.dp),
            containerColor = Color.White,
            modifier = Modifier.wrapContentSize(align = Alignment.Center)

            ) {

           menuItems.forEach { item ->

               DropdownMenuItem(
                   text = { Text(
                       text = item.title,
                       fontSize = 15.sp,
                       textAlign = TextAlign.Center,
                   )

                          },
                   onClick = {
                       showMenu = false
                       item.onClick()
                   }
               )
           }


        }


    }

}
