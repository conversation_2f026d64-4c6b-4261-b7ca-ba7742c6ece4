package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

@Composable
fun WeSingleColumnPicker(
    visible: Bo<PERSON>an,
    title: String? = null,
    range: List<String>,
    value: Int,
    onChange: (Int) -> Unit,
    onCancel: () -> Unit
) {
    WePicker(
        visible,
        arrayOf(range),
        arrayOf(value),
        title,
        onCancel
    ) {
        onChange(it.first())
    }
}

@Composable
private fun CarPickDemo() {
    var visible by remember { mutableStateOf(false) }
    var values by remember { mutableStateOf(arrayOf(0, 0)) }

    val sourceMap = remember {
        arrayOf(
            "小米" to listOf("SU7"),
            "小鹏" to listOf("G9", "G6", "P7i"),
            "理想" to listOf("Mega", "L9", "L8", "L7")
        )
    }
    var tmpValues by remember { mutableStateOf(values) }
    val ranges by remember {
        derivedStateOf {
            arrayOf(
                sourceMap.map { it.first },
                sourceMap[tmpValues.first()].second
            )
        }
    }

    WePicker(
        visible,
        ranges,
        values,
        title = "选择汽车",
        onCancel = { visible = false },
        onColumnValueChange = { _, _, newValues ->
            tmpValues = newValues
        }
    ) {
        values = it
    }

    CustomInput(
        value = remember(values) {
            arrayOf(
                ranges[0].getOrElse(values[0]) { 0 },
                ranges[1].getOrElse(values[1]) { 0 }
            ).joinToString(" ")
        },
        textAlign = TextAlign.Center,
        disabled = true
    )
    Spacer(modifier = Modifier.height(20.dp))
    WeButton(text = "选择汽车") {
        visible = true
    }
}

@Composable
private fun CountryPickDemo() {
    val picker = rememberSingleColumnPickerState()
    var value by remember { mutableIntStateOf(0) }
    val range = remember {
        listOf(
            "中国",
            "美国",
            "德国",
            "法国",
            "英国",
            "瑞士",
            "希腊",
            "西班牙",
            "荷兰"
        )
    }

    CustomInput(
        value = range[value],
        textAlign = TextAlign.Center,
        disabled = true
    )
    Spacer(modifier = Modifier.height(20.dp))
    WeButton(text = "选择国家") {
        picker.show(
            title = "选择国家",
            range,
            value
        ) {
            value = it
        }
    }
}

@Stable
interface SingleColumnPickerState {
    val visible: Boolean

    fun show(
        title: String? = null,
        range: List<String>,
        value: Int,
        onChange: (Int) -> Unit
    )

    fun hide()
}

@Composable
fun rememberSingleColumnPickerState(): SingleColumnPickerState {
    val state = remember { SingleColumnPickerStateImpl() }

    state.props?.let { props ->
        WeSingleColumnPicker(
            visible = state.visible,
            title = props.title,
            range = props.range,
            value = props.value,
            onChange = props.onChange,
            onCancel = { state.hide() }
        )
    }

    return state
}

private class SingleColumnPickerStateImpl : SingleColumnPickerState {
    override var visible by mutableStateOf(false)
    var props by mutableStateOf<SingleColumnPickerProps?>(null)
        private set

    override fun show(
        title: String?,
        range: List<String>,
        value: Int,
        onChange: (Int) -> Unit
    ) {
        props = SingleColumnPickerProps(title, range, value, onChange)
        visible = true
    }

    override fun hide() {
        visible = false
    }
}

private data class SingleColumnPickerProps(
    val title: String? = null,
    val range: List<String>,
    val value: Int,
    val onChange: (Int) -> Unit
)

// 创建一个对象来存储地区数据
object RegionData {
    // 地区数据源
    val sourceMap = arrayOf(
        "中国" to arrayOf(
            "北京" to listOf("东城区", "西城区", "朝阳区"),
            "上海" to listOf("黄浦区", "徐汇区", "浦东新区"),
            "广东" to listOf("广州市", "深圳市", "珠海市")
        ),
        "美国" to arrayOf(
            "纽约州" to listOf("纽约市", "布法罗", "罗切斯特"),
            "加利福尼亚州" to listOf("洛杉矶", "旧金山", "圣地亚哥")
        )
    )
}

@Composable
fun WeRegionPicker(
    visible: Boolean,
    title: String? = null,
    values: Array<Int>,
    onCancel: () -> Unit,
    onConfirm: (Array<Int>) -> Unit
) {
    var tmpValues by remember { mutableStateOf(values) }
    
    val level2Regions = RegionDataManager.getRegionsByLevel(2)
    val level3Regions = remember(tmpValues[0], level2Regions) {
        if (tmpValues[0] >= 0 && tmpValues[0] < level2Regions.size) {
            RegionDataManager.getChildren(level2Regions[tmpValues[0]].id)
        } else emptyList()
    }
    val level4Regions = remember(tmpValues[1], level3Regions, level2Regions) {
        if (tmpValues[1] >= 0 && tmpValues[1] < level3Regions.size) {
            RegionDataManager.getChildren(level3Regions[tmpValues[1]].id)
        } else emptyList()
    }
    
    // 监听 level2Regions 的变化
    LaunchedEffect(level2Regions) {
        if (tmpValues[0] >= level2Regions.size) {
            val newValues = arrayOf(0, 0, 0)
            tmpValues = newValues
            onConfirm(newValues)
        }
    }
    
    // 监听 level3Regions 的变化
    LaunchedEffect(level3Regions) {
        if (tmpValues[1] >= level3Regions.size) {
            val newValues = arrayOf(tmpValues[0], 0, 0)
            tmpValues = newValues
            // 立即触发回调，更新显示值
            onConfirm(newValues)
        }
    }
    
    // 监听 level4Regions 的变化
    LaunchedEffect(level4Regions) {
        if (tmpValues[2] >= level4Regions.size) {
            val newValues = arrayOf(tmpValues[0], tmpValues[1], 0)
            tmpValues = newValues
            // 立即触发回调，更新显示值
            onConfirm(newValues)
        }
    }
    
    val ranges by remember(level2Regions, level3Regions, level4Regions) {
        derivedStateOf {
            arrayOf(
                level2Regions.map { it.name },
                level3Regions.map { it.name },
                level4Regions.map { it.name }
            )
        }
    }

    WePicker(
        visible,
        ranges,
        tmpValues,
        title = title,
        onCancel = onCancel,
        onColumnValueChange = { column, _, newValues ->
            tmpValues = when (column) {
                0 -> arrayOf(newValues[0], 0, 0)
                1 -> arrayOf(newValues[0], newValues[1], 0)
                else -> newValues
            }
        }
    ) {
        onConfirm(tmpValues)
    }
}

@Stable
interface RegionPickerState {
    val visible: Boolean
    
    fun show(
        title: String? = null,
        values: Array<Int>,
        onChange: (Array<Int>) -> Unit
    )
    
    fun hide()
}

@Composable
fun rememberRegionPickerState(): RegionPickerState {
    val state = remember { RegionPickerStateImpl() }
    
    state.props?.let { props ->
        WeRegionPicker(
            visible = state.visible,
            title = props.title,
            values = props.values,
            onCancel = { state.hide() },
            onConfirm = props.onChange
        )
    }
    
    return state
}

private class RegionPickerStateImpl : RegionPickerState {
    override var visible by mutableStateOf(false)
    var props by mutableStateOf<RegionPickerProps?>(null)
        private set
        
    override fun show(
        title: String?,
        values: Array<Int>,
        onChange: (Array<Int>) -> Unit
    ) {
        props = RegionPickerProps(title, values, onChange)
        visible = true
    }
    
    override fun hide() {
        visible = false
    }
}

private data class RegionPickerProps(
    val title: String?,
    val values: Array<Int>,
    val onChange: (Array<Int>) -> Unit
)