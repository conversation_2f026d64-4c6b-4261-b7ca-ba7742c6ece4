package com.ymx.photovoltaic.ui.page.my

import LanguageSelector
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.language.Language
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.platform.getAppVersion
import com.ymx.photovoltaic.platform.updateLanguage
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.MenuItem
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.LanguageViewModel
import com.ymx.photovoltaic.viewmodel.MyViewModel
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.current_version_short
import photovoltaic_kmp_app.composeapp.generated.resources.language_setting
import photovoltaic_kmp_app.composeapp.generated.resources.lj_icon
import photovoltaic_kmp_app.composeapp.generated.resources.set_lanauage
import photovoltaic_kmp_app.composeapp.generated.resources.set_version
import photovoltaic_kmp_app.composeapp.generated.resources.settings
import photovoltaic_kmp_app.composeapp.generated.resources.version_check


@Composable
fun SettingPage(
    navHostController: NavHostController,
    myViewModel: MyViewModel = getKoin().get(),
    languageViewModel: LanguageViewModel = getKoin().get()
) {
    // 进入页面就请求版本信息接口，如果有新版本就显示new 点击new条目弹出确认更新弹窗，确认更新进行文件下载，
    // 下载完成后进行安装
//    val context = LocalContext.current
//
//    val nowVersionNum=AppUtil.getAppVersionName(context)
//    val version by myViewModel.versionFlow.collectAsState()

//    val appUpdateManager = remember { AppUpdateManager(context) }

    // 获取当前版本号
    val appVersion = getAppVersion()
    
    LaunchedEffect(Unit)
    {
//        myViewModel.queryAppVersion(nowVersionNum,"ch")
    }

//    var isHaveNewVersion by remember { mutableStateOf(false) }

//    version?.let {
//        if(it.versionNum!=nowVersionNum)
//        {
//            isHaveNewVersion=true
//        }
//    }

    val coroutineScope = rememberCoroutineScope()

    val languages = listOf(
        Language("zh", "中文"),
        Language("en", "English"),
    )

    // 使用collectAsState订阅ViewModel中的language StateFlow
    val languageState = languageViewModel.language.collectAsState()
    // 使用rememberUpdatedState确保UI在语言变化时更新
    val currentLanguageState by rememberUpdatedState(
        when(languageState.value.code) {
            "en" -> languages[1]
            else -> languages[0]
        }
    )
    
    var currentLanguage by remember {
        mutableStateOf(currentLanguageState)
    }
    
    // 当language StateFlow更新时，自动更新currentLanguage
    LaunchedEffect(languageState.value) {
        currentLanguage = when(languageState.value.code) {
            "en" -> languages[1]
            else -> languages[0]
        }
    }

    var expanded by remember { mutableStateOf(false) }
    var settingItemSize by remember { mutableStateOf(IntSize.Zero) }

    SetStatusBar(Color.White,true)

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.settings), backClick = {navHostController.popBackStack()})
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues).padding(start = 10.dp, end = 10.dp, top = 16.dp)
        ) {
            // App图标和版本号
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 24.dp),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // App图标
                    Image(
                        painter = painterResource(Res.drawable.lj_icon),
                        contentDescription = "App Icon",
                        modifier = Modifier.size(80.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 版本号
                    Text(
                        text = "v$appVersion",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Normal
                    )
                }
            }

            // 菜单项
            Column {
                MenuItem(
                    icon = painterResource(Res.drawable.set_lanauage), 
                    text = stringResource(Res.string.language_setting),
                    onItemClick = {
                        expanded = !expanded
                    },
                    modifier = Modifier.onGloballyPositioned { coordinates ->
                        // 保存MenuItem的尺寸，以便正确定位LanguageSelector
                        settingItemSize = coordinates.size
                    }
                )

                if (expanded) { // Only show the dropdown when expanded is true
                    LanguageSelector(
                        expanded = expanded,
                        onDismissRequest = { expanded = false },
                        selectedLanguage = currentLanguage,
                        onLanguageSelected = { language ->
                            currentLanguage = language
                            expanded = false

                            // 设置新语言，ViewModel会通过StateFlow通知UI更新
                            languageViewModel.setLanguage(currentLanguage)
                            
                            // 使用协程执行语言更新操作
                            coroutineScope.launch {
                                // 更新系统语言设置
                                updateLanguage()
                            }
                            
                            // 注意：Android平台需要重启Activity，iOS平台依赖Compose状态管理刷新UI
                            // 平台特定代码在各自的LanguageViewModel实现中处理
                        },
                        availableLanguages = languages,
                        modifier = Modifier.width(with(LocalDensity.current) { settingItemSize.width.toDp() })
                    )
                }
            }

            MenuItem(
                icon = painterResource(Res.drawable.set_version),
                text = stringResource(Res.string.version_check),
                versionText = stringResource(Res.string.current_version_short) + appVersion,
                onItemClick = {}
            )
        }
    }
}



