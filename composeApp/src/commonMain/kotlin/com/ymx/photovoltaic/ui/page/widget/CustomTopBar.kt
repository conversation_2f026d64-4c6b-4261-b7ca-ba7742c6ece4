package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.ui.page.theme.Grey_38

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TopBar(
    textStr: String,
    backClick: () -> Unit,
    isBackIcon: Boolean = true,
    topContainerColor: Color = Color.White,
    backButtonColor: Color=Color.Black
) {
    CenterAlignedTopAppBar(
        title = {
            Text(
                text = textStr,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Grey_38,
                modifier = Modifier.padding(vertical = 2.dp)
            )
        },
        navigationIcon = {
            if (isBackIcon) {
                IconButton(onClick = { backClick() }) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "Back",
                        tint = backButtonColor
                    )
                }
            }
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
            containerColor = topContainerColor
        ),
        expandedHeight = 45.dp
    )
}