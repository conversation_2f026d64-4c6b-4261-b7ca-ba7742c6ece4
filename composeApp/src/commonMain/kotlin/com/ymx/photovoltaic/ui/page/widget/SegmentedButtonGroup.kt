package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.ui.page.theme.Blue_F9

@Composable
fun SegmentedButtonGroup(
    options: List<String>,
    initialSelectedIndex: Int = 0,
    onSelectionChanged: (String, Int) -> Unit = { _, _ -> },
    modifier: Modifier = Modifier,
    selectedColor: Color = Blue_F9,
    unselectedColor: Color = Color.Gray
) {
    if (options.isEmpty()) return
    
    var selectedIndex by remember { mutableStateOf(initialSelectedIndex) }
    
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(6.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        options.forEachIndexed { index, option ->
            val isSelected = index == selectedIndex
            val buttonColor = if (isSelected) selectedColor else unselectedColor
            
            Button(
                onClick = {
                    selectedIndex = index
                    onSelectionChanged(option, index)
                },
                shape = RoundedCornerShape(15.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.White,
                ),
                border = BorderStroke(0.5.dp, buttonColor),
                contentPadding = PaddingValues(
                    horizontal = 16.dp,
                    vertical = 0.dp
                ),
                modifier = Modifier
                    .height(28.dp)
            ) {
                Text(
                    text = option,
                    fontSize = 12.sp,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                    color = buttonColor,
                    lineHeight = 12.sp,
                    overflow = TextOverflow.Visible,
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )
            }
        }
    }
}
