package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonSearch
import com.ymx.photovoltaic.ui.page.widget.ComponentItem
import com.ymx.photovoltaic.ui.page.widget.FloatingButton
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.create_time
import photovoltaic_kmp_app.composeapp.generated.resources.input_optimizer_id
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer_management


@Composable
fun OptimizerListPage(
    navHostController: NavHostController,
    groupId: String,
    equipmentViewModel: EquipmentViewModel = getKoin().get()
) {
//    val scanLauncher = rememberLauncherForActivityResult(
//        contract = ActivityResultContracts.StartActivityForResult()
//    ) { result ->
//        if (result.resultCode == Activity.RESULT_OK) {
//            // 扫描成功后刷新数据
//            equipmentViewModel.fetchOptimizerList(
//                powerStationId = "",
//                belongsGroupId = groupId,
//                belongsGroupFlag = "detailComponent",
//                pageSize = 30
//            )
//        }
//    }
    
    // 监听生命周期事件，在恢复时刷新数据
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                equipmentViewModel.fetchOptimizerList(
                    powerStationId = "",
                    belongsGroupId = groupId,
                    belongsGroupFlag = "detailComponent",
                    pageSize = 30
                )
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.optimizer_management),
                backClick = { navHostController.popBackStack() })
        },
        containerColor = Grey_F5,
        floatingActionButton = {
            FloatingButton(
                onScanClicked = {
//                    val intent = Intent(navHostController.context, ScanActivity::class.java).apply {
//                        putExtra("groupId", groupId)
//                        putExtra("from", "optimizerScan")
//                    }
//                    scanLauncher.launch(intent)
                },
                onAddClicked = { navHostController.navigate(Route.OPTIMIZER_NEW + "/$groupId/2") }
            )
        }
    ) { paddingValues ->
        OptimizerContent(
            navHostController = navHostController,
            groupId = groupId,
            equipmentViewModel = equipmentViewModel,
            modifier = Modifier.padding(paddingValues)
        )
    }
}

@Composable
fun OptimizerContent(
    navHostController: NavHostController,
    groupId: String? = null,
    powerStationId: String? = null,
    belongsGroupFlagStr :String ="detailComponent",
    equipmentViewModel: EquipmentViewModel,
     modifier: Modifier = Modifier
) {
    val optimizerList by equipmentViewModel.optimizerListFlow.collectAsState()
    var searchQuery by remember { mutableStateOf("") }

    val filteredOptimizers = optimizerList.filter {
        it.chipId.contains(searchQuery, ignoreCase = true)
    }

    LaunchedEffect(Unit) {
        equipmentViewModel.fetchOptimizerList(
            powerStationId = powerStationId ?: "",
            belongsGroupId = groupId ?: "",
            belongsGroupFlag = belongsGroupFlagStr,
            pageSize = 30
        )
    }

    Column(modifier = modifier) {
        Spacer(modifier = Modifier.height(5.dp))
        CommonSearch(
            searchQuery = searchQuery,
            onQueryChange = { searchQuery = it },
            placeholderTextResId = Res.string.input_optimizer_id,
            Modifier.
            padding(start = 10.dp, end = 10.dp).fillMaxWidth().height(55.dp)
                .clip(RoundedCornerShape(25.dp))
        )

        val newOptimizerList = filteredOptimizers ?: emptyList()

        LazyColumn(modifier = Modifier.padding(start = 10.dp, end = 10.dp)) {
            items(newOptimizerList) { optimizer ->
                ComponentItem(
                    "optimizer",
                    optimizer.chipId,
                    stringResource(Res.string.create_time) + optimizer.createTimeCh,
                    menuItems = emptyList(),
                    onClick = {
                        navHostController.navigate(
                            Route.OPTIMIZER_EDIT + "/${optimizer.id}/${optimizer.chipId}/${optimizer.model}/${optimizer.status}"
                        )
                    }
                )
            }
        }
    }
}



