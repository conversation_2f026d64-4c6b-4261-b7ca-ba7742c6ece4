package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.platform.ToastUtil
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.PlaceholderText
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.collector_info
import photovoltaic_kmp_app.composeapp.generated.resources.collector_name
import photovoltaic_kmp_app.composeapp.generated.resources.error_collector_name_empty
import photovoltaic_kmp_app.composeapp.generated.resources.error_imei_empty
import photovoltaic_kmp_app.composeapp.generated.resources.error_imei_invalid_chars
import photovoltaic_kmp_app.composeapp.generated.resources.imei
import photovoltaic_kmp_app.composeapp.generated.resources.input_collector_name
import photovoltaic_kmp_app.composeapp.generated.resources.input_or_scan_imei
import photovoltaic_kmp_app.composeapp.generated.resources.operation_failed
import photovoltaic_kmp_app.composeapp.generated.resources.save
import photovoltaic_kmp_app.composeapp.generated.resources.scan

@Composable
fun CollectorOnePage(
    navHostController: NavHostController,
    equipmentViewModel: EquipmentViewModel = getKoin().get(),
    collectorId: String,
    collectorName: String,
    imei: String
) {
    var collectorNameValue by remember { mutableStateOf(collectorName) }
    var imeiValue by remember { mutableStateOf(imei) }
    
    // Error states
    var collectorNameError by remember { mutableStateOf<String?>(null) }
    var imeiError by remember { mutableStateOf<String?>(null) }
    

    val scanResult =  navHostController.currentBackStackEntry?.savedStateHandle?.
    getStateFlow("scanResult","")?.collectAsState()

    LaunchedEffect (scanResult?.value) {
        val  result=scanResult?.value.toString()
        if(result.isNotEmpty()&& result.all { it.isDigit() })
        {
            imeiValue=result
        }
    }

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.collector_info), backClick = {navHostController.popBackStack()})
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 60.dp)
        ) {
            var showSuccessDialog by remember { mutableStateOf(false) }
            if (showSuccessDialog) {
                ResultDialog(true) { showSuccessDialog = false }
            }

            var showFailDialog by remember { mutableStateOf(false) }
            if (showFailDialog) {
                ResultDialog(false) { showFailDialog = false }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 采集器名称输入
            CommonTitleField(
                value = collectorNameValue,
                onValueChange = { 
                    collectorNameValue = it 
                    collectorNameError = null // 清除错误提示
                },
                titleText = stringResource(Res.string.collector_name),
                placeholderCom = {
                    PlaceholderText(Res.string.input_collector_name)
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                isError = collectorNameError != null,
                errorText = collectorNameError ?: ""
            )

            // IMEI输入
            CommonTitleField(
                value = imeiValue,
                onValueChange = { newValue ->
                    if (newValue.all { it.isDigit() }) {
                        imeiValue = newValue
                        imeiError = null // 清除错误提示
                    }
                },
                titleText = stringResource(Res.string.imei),
                placeholderCom = {
                    PlaceholderText(Res.string.input_or_scan_imei)
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Number
                ),
                isError = imeiError != null,
                errorText = imeiError ?: "",
                trailingIconCom = {
                    Image(
                        painter = painterResource(Res.drawable.scan),
                        modifier = Modifier.size(18.dp).clickable {
                            // 跳转到扫描页面
                            navHostController.currentBackStackEntry?.savedStateHandle?.set("scanType", "imei")
                            navHostController.navigate(Route.SCAN)
                        },
                        contentDescription = "Scan IMEI"
                    )
                }
            )

            Spacer(modifier = Modifier.weight(1f))

            val coroutineScope = rememberCoroutineScope()
            val errorMsgEmptyName = stringResource(Res.string.error_collector_name_empty)
            val errorMsgEmptyImei = stringResource(Res.string.error_imei_empty)
            val errorMsgInvalidImei = stringResource(Res.string.error_imei_invalid_chars)
            val operationFailedMsg = stringResource(Res.string.operation_failed)

            ConfirmButton(stringResource(Res.string.save), true) {
                // 重置错误状态
                collectorNameError = null
                imeiError = null
                var hasError = false

                val trimmedName = collectorNameValue.trim()
                val trimmedImei = imeiValue.trim()

                // 验证采集器名称
                if (trimmedName.isEmpty()) {
                    collectorNameError = errorMsgEmptyName
                    hasError = true
                }

                // 验证IMEI
                if (trimmedImei.isEmpty()) {
                    imeiError = errorMsgEmptyImei
                    hasError = true
                } else if (!trimmedImei.all { it.isDigit() }) {
                    imeiError = errorMsgInvalidImei
                    hasError = true
                }

                if (hasError) {
                    return@ConfirmButton
                }

                val mutableMap = mutableMapOf(
                    "cloudName" to trimmedName,
                    "imei" to trimmedImei
                )

                if (collectorId != "") {
                    mutableMap["id"] = collectorId
                } else {
                    mutableMap["powerStationId"] = AppGlobal.powerStationId
                    mutableMap["createUserId"] = AppGlobal.mId
                    mutableMap["producers"] = "ymx"
                    mutableMap["model"] = "gd"
                }

                equipmentViewModel.addOrModCollector(mutableMap, errorBlock = {
                    ToastUtil.showShort(operationFailedMsg)
                    showFailDialog = true
                    coroutineScope.launch {
                        delay(2000)
                        showFailDialog = false
                    }
                }) {
                    showSuccessDialog = true
                    coroutineScope.launch {
                        delay(2000)
                        showSuccessDialog = false
                        navHostController.popBackStack()
                    }
                }
            }
        }
    }
}