package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.home_error
import photovoltaic_kmp_app.composeapp.generated.resources.home_offline
import photovoltaic_kmp_app.composeapp.generated.resources.home_ok
import photovoltaic_kmp_app.composeapp.generated.resources.report_location
import photovoltaic_kmp_app.composeapp.generated.resources.report_sun
import photovoltaic_kmp_app.composeapp.generated.resources.status_abnormal
import photovoltaic_kmp_app.composeapp.generated.resources.status_normal
import photovoltaic_kmp_app.composeapp.generated.resources.status_offline

/**
 * A composable function that displays a weather status bar with location, sun time, and status indicator.
 *
 * @param location The location text to display (e.g., "嘉兴・明天")
 * @param temperature The temperature range text to display (e.g., "10-24°C")
 * @param weather The weather text to display (e.g., "晴")
 * @param sunTime The sun time text to display (e.g., "日落 06:09-19:17")
 * @param powerStatus The status text of the power station
 * @param modifier Optional modifier for the entire component
 */
@Composable
fun WeatherStatusBar(
    location: String,
    temperature: String,
    weather: String,
    sunTime: String,
    powerStatus: String,
    modifier: Modifier = Modifier
) {
    // 根据状态判断状态文本、颜色和图标
    val (statusText, statusColor, iconRes) = when (powerStatus) {
        stringResource(Res.string.status_offline) -> Triple(stringResource(Res.string.status_offline), Color(0xFF9E9E9E), Res.drawable.home_offline)
        stringResource(Res.string.status_abnormal) -> Triple(stringResource(Res.string.status_abnormal), Color(0xFFF44336), Res.drawable.home_error)
        else -> Triple(stringResource(Res.string.status_normal), Color(0xFF4CAF50), Res.drawable.home_ok)
    }
    
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(Color(0xFFF5F5F5))
            .padding(start = 30.dp, end = 30.dp,top=10.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // Location section
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(Res.drawable.report_location),
                contentDescription = "Location",
                modifier = Modifier.size(16.dp),
                tint = Color.Gray
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "$location·$weather $temperature",
                style = TextStyle(
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            )
        }
        
        // Sun time section
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(Res.drawable.report_sun),
                contentDescription = "Sun time",
                modifier = Modifier.size(16.dp),
                tint = Color.Gray
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = sunTime,
                style = TextStyle(
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            )
        }

        // Status indicator with icon
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .clip(RoundedCornerShape(12.dp))
                .background(statusColor.copy(alpha = 0.2f))
                .padding(horizontal = 8.dp, vertical = 4.dp)
        ) {
            // Status icon
            Icon(
                painter = painterResource(iconRes),
                contentDescription = statusText,
                modifier = Modifier.size(16.dp),
                tint = statusColor
            )
            
            Spacer(modifier = Modifier.width(4.dp))
            
            // Status text
            Text(
                text = statusText,
                style = TextStyle(
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = statusColor
                )
            )
        }
    }
}



