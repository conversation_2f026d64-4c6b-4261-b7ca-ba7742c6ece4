package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.cancel
import photovoltaic_kmp_app.composeapp.generated.resources.confirm
import photovoltaic_kmp_app.composeapp.generated.resources.selected_region

// 定义数据类
data class Region(
    val id: String,
    val name: String,
    val cities: List<City> = emptyList()
)

data class City(
    val id: String,
    val name: String,
    val districts: List<District> = emptyList()
)

data class District(
    val id: String,
    val name: String
)

private val regions = listOf(
    Region("35", "福建省", listOf(
        City("3501", "福州市", listOf(
            District("350101", "鼓楼区"),
            District("350102", "台江区"),
            District("350103", "仓山区"),
            District("350104", "马尾区"),
            District("350105", "晋安区"),
            District("350111", "闽侯县"),
            District("350112", "连江县"),
            District("350113", "罗源县"),
            District("350114", "闽清县"),
            District("350115", "永泰县"),
            District("350116", "平潭县"),
            District("350117", "福清市"),
            District("350118", "长乐市")
        )),
        City("3502", "厦门市", listOf(
            District("350201", "思明区"),
            District("350202", "海沧区"),
            District("350203", "湖里区"),
            District("350204", "集美区"),
            District("350205", "同安区"),
            District("350206", "翔安区")
        )),
        City("3503", "莆田市", listOf(
            District("350301", "城厢区"),
            District("350302", "涵江区"),
            District("350303", "荔城区"),
            District("350304", "秀屿区"),
            District("350305", "仙游县")
        )),
        City("3504", "三明市", listOf(
            District("350401", "梅列区"),
            District("350402", "三元区"),
            District("350403", "明溪县"),
            District("350404", "清流县"),
            District("350405", "宁化县")
        )),
        City("3505", "泉州市", listOf(
            District("350501", "鲤城区"),
            District("350502", "丰泽区"),
            District("350503", "洛江区"),
            District("350504", "泉港区"),
            District("350505", "惠安县")
        )),
        City("3506", "漳州市", listOf(
            District("350601", "芗城区"),
            District("350602", "龙文区"),
            District("350603", "云霄县"),
            District("350604", "漳浦县"),
            District("350605", "诏安县")
        ))
    )),
    Region("44", "广东省", listOf(
        City("4401", "广州市", listOf(
            District("440101", "荔湾区"),
            District("440102", "越秀区"),
            District("440103", "海珠区"),
            District("440104", "天河区"),
            District("440105", "白云区")
        )),
        City("4402", "深圳市", listOf(
            District("440201", "罗湖区"),
            District("440202", "福田区"),
            District("440203", "南山区"),
            District("440204", "宝安区"),
            District("440205", "龙岗区")
        ))
    )),
    Region("33", "浙江省", listOf(
        City("3301", "杭州市", listOf(
            District("330101", "上城区"),
            District("330102", "下城区"),
            District("330103", "江干区"),
            District("330104", "拱墅区"),
            District("330105", "西湖区")
        )),
        City("3302", "宁波市", listOf(
            District("330201", "海曙区"),
            District("330202", "江东区"),
            District("330203", "江北区"),
            District("330204", "北仑区"),
            District("330205", "镇海区")
        ))
    ))
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RegionPickerDialog(
    showDialog: Boolean,
    onDismiss: () -> Unit,
    onRegionSelected: (region: String, regionId: String, city: String, cityId: String, district: String) -> Unit
) {
    if (!showDialog) return

    var selectedRegion by remember { mutableStateOf<Region?>(null) }
    var selectedCity by remember { mutableStateOf<City?>(null) }
    var selectedDistrict by remember { mutableStateOf<District?>(null) }

    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = rememberModalBottomSheetState(),
        dragHandle = null
    ) {
        Column(
            modifier = Modifier
                .background(Color.White)
                .fillMaxWidth()
        ) {
            // 顶部操作栏
            TopBar(
                onCancel = onDismiss,
                onConfirm = {
                    if (selectedRegion != null && selectedCity != null && selectedDistrict != null) {
                        onRegionSelected(
                            selectedRegion!!.name,
                            selectedRegion!!.id,
                            selectedCity!!.name,
                            selectedCity!!.id,
                            selectedDistrict!!.name
                        )
                    }
                    onDismiss()
                }
            )

            // 已选择区域显示
            SelectedRegionBar(
                selectedRegion = selectedRegion,
                selectedCity = selectedCity,
                selectedDistrict = selectedDistrict
            )

            // 区域选择列表
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                // 省份列表
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxHeight()
                ) {
                    RegionList(
                        items = regions,
                        selectedItem = selectedRegion,
                        onItemSelected = {
                            selectedRegion = it
                            selectedCity = null
                            selectedDistrict = null
                        }
                    )
                }

                // 城市列表
                selectedRegion?.let { region ->
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .background(Color(0xFFF5F5F5))
                    ) {
                        RegionList(
                            items = region.cities,
                            selectedItem = selectedCity,
                            onItemSelected = {
                                selectedCity = it
                                selectedDistrict = null
                            }
                        )
                    }
                }

                // 区县列表
                selectedCity?.let { city ->
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                    ) {
                        RegionList(
                            items = city.districts,
                            selectedItem = selectedDistrict,
                            onItemSelected = {
                                selectedDistrict = it
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TopBar(
    onCancel: () -> Unit,
    onConfirm: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = stringResource(Res.string.cancel),
            color = Color.Gray,
            modifier = Modifier.clickable { onCancel() }
        )
        Text(
            text = stringResource(Res.string.confirm),
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.clickable { onConfirm() }
        )
    }
}

@Composable
private fun SelectedRegionBar(
    selectedRegion: Region?,
    selectedCity: City?,
    selectedDistrict: District?
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFFF5F5F5))
            .padding(16.dp),
        horizontalArrangement = Arrangement.Start,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(Res.string.selected_region),
            color = Color.Gray,
            fontSize = 14.sp
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = buildString {
                append(selectedRegion?.name ?: "")
                if (selectedCity != null) {
                    append(" ${selectedCity.name}")
                }
                if (selectedDistrict != null) {
                    append(" ${selectedDistrict.name}")
                }
            },
            color = MaterialTheme.colorScheme.primary,
            fontWeight = FontWeight.Bold,
            fontSize = 14.sp
        )
    }
}

@Composable
private fun <T> RegionList(
    items: List<T>,
    selectedItem: T?,
    onItemSelected: (T) -> Unit
) {
    LazyColumn {
        items(items) { item ->
            RegionItem(
                text = when(item) {
                    is Region -> item.name
                    is City -> item.name
                    is District -> item.name
                    else -> ""
                },
                isSelected = item == selectedItem,
                onClick = { onItemSelected(item) }
            )
        }
    }
}

@Composable
private fun RegionItem(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .background(if (isSelected) Color(0xFFE3F2FD) else Color.Transparent)
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = text,
            color = if (isSelected) MaterialTheme.colorScheme.primary else Color.Black,
            fontSize = 14.sp
        )
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
} 