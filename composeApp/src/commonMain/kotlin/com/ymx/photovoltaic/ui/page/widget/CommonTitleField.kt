package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.ic_pwd_hide
import photovoltaic_kmp_app.composeapp.generated.resources.ic_pwd_show

/**
 * 通用文本字段组件，支持普通文本输入和密码输入
 *
 * @param value 当前输入值
 * @param onValueChange 值变化的回调
 * @param placeholderCom 占位符文本
 * @param titleText 标题文本，为空则不显示标题
 * @param isError 是否显示错误状态
 * @param errorText 错误提示文本
 * @param modifier 修饰符
 * @param isPassword 是否为密码输入模式
 * @param passwordVisibility 密码是否可见（仅在密码模式下生效）
 * @param onPasswordVisibilityChange 密码可见性变化的回调（仅在密码模式下生效）
 * @param backgroundColor 背景颜色，默认为白色
 * @param cornerRadius 圆角大小，默认为16dp
 * @param titleBottom 标题底部间距，默认为10dp
 * @param textFieldHeight 文本字段高度，默认为55dp
 * @param textStyle 文本样式
 * @param errorColor 错误颜色，默认为红色
 */
@Composable
fun CommonTitleField(
    value: String,
    onValueChange: (String) -> Unit,
    placeholderCom: @Composable (() -> Unit)? = null,
    trailingIconCom: @Composable (() -> Unit)? = null,
    leadingIconCom: @Composable (() -> Unit)? = null,
    onBoxClick: (() -> Unit?)? =null,
    titleText: String = "",
    isError: Boolean = false,
    errorText: String = "",
    modifier: Modifier = Modifier,
    isPassword: Boolean = false,
    passwordVisibility: Boolean = false,
    onPasswordVisibilityChange: (() -> Unit)? = null,
    backgroundColor: Color = Color.White,
    cornerRadius: Int = 16,
    titleBottom: Int = 10,
    textFieldHeight: Int = 55,
    textStyle: TextStyle = TextStyle.Default,
    errorColor: Color = Color.Red,
    isReadOnly: Boolean = false,
    isSelected: Boolean = false,
    isSingleLine:Boolean= true,
    keyboardOptions: KeyboardOptions = KeyboardOptions. Default,
    interactionSource: MutableInteractionSource? = null,
    scrollState: ScrollState? =null,
) {

    Column(modifier = modifier) {
        // 如果有标题，显示标题
        if (titleText.isNotEmpty()) {
            Box(modifier = Modifier.padding(top = 10.dp, bottom = titleBottom.dp)) {
                TitleItem(text = titleText)
            }
        }

        // 文本字段容器
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clickable
                {
                    if (onBoxClick != null) {
                        onBoxClick()
                    }
                }

        ) {
            OutlinedTextField(
                value = value,
                singleLine = isSingleLine,
                readOnly = isReadOnly,
                enabled = !isReadOnly,
                onValueChange = onValueChange,
                placeholder = placeholderCom,
                modifier = Modifier.fillMaxWidth().height(textFieldHeight.dp).then(
                    if(scrollState!=null){
                        Modifier.verticalScroll(scrollState)
                    }else{
                        Modifier
                    }
                ),
                shape = RoundedCornerShape(cornerRadius.dp),
                textStyle = textStyle,
                keyboardOptions=keyboardOptions,
                interactionSource=interactionSource,
                colors = TextFieldDefaults.colors(
                    focusedContainerColor = backgroundColor,
                    unfocusedContainerColor = backgroundColor,
                    focusedIndicatorColor =  backgroundColor,
                    unfocusedIndicatorColor = backgroundColor,
                    errorContainerColor = backgroundColor,
                    errorIndicatorColor = backgroundColor,
                    disabledContainerColor = backgroundColor,
                    disabledTextColor = if (isReadOnly) {
                        if (isSelected) Color.Black else Color.Gray
                    } else Color.Black,
                    disabledIndicatorColor = backgroundColor,
                ),
                // 密码模式相关配置
                visualTransformation = if (isPassword && !passwordVisibility) 
                    PasswordVisualTransformation() else VisualTransformation.None,
                leadingIcon = leadingIconCom,
                trailingIcon = if (isPassword && onPasswordVisibilityChange != null) {
                    {
                        IconButton(onClick = { onPasswordVisibilityChange() }) {
                            Icon(
                                painter = if (passwordVisibility) 
                                    painterResource(Res.drawable.ic_pwd_show)
                                else 
                                    painterResource(Res.drawable.ic_pwd_hide),
                                modifier = Modifier.size(25.dp),
                                contentDescription = null
                            )
                        }
                    }
                } else trailingIconCom,
                isError = isError
            )
        }

        // 错误提示
        if (isError && errorText.isNotEmpty()) {
            Text(
                text = errorText,
                color = errorColor,
                modifier = Modifier.padding(start = 12.dp, top = 4.dp)
            )
        }
    }
} 