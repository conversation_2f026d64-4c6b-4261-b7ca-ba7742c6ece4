package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.theme.LjRed
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.CustomInput
import com.ymx.photovoltaic.ui.page.widget.LoadingDialog
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.ui.page.widget.WifiConfigCard
import com.ymx.photovoltaic.util.KtorSocketUtils
import io.ktor.utils.io.charsets.Charsets
import io.ktor.utils.io.core.toByteArray
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.collector_config
import photovoltaic_kmp_app.composeapp.generated.resources.query
import photovoltaic_kmp_app.composeapp.generated.resources.query_window
import photovoltaic_kmp_app.composeapp.generated.resources.relay_1
import photovoltaic_kmp_app.composeapp.generated.resources.relay_2
import photovoltaic_kmp_app.composeapp.generated.resources.relay_3
import photovoltaic_kmp_app.composeapp.generated.resources.relay_4
import photovoltaic_kmp_app.composeapp.generated.resources.relay_5
import photovoltaic_kmp_app.composeapp.generated.resources.relay_6
import photovoltaic_kmp_app.composeapp.generated.resources.relay_7
import photovoltaic_kmp_app.composeapp.generated.resources.relay_8
import photovoltaic_kmp_app.composeapp.generated.resources.save_relay
import photovoltaic_kmp_app.composeapp.generated.resources.save_server
import photovoltaic_kmp_app.composeapp.generated.resources.sending
import photovoltaic_kmp_app.composeapp.generated.resources.server_ip
import photovoltaic_kmp_app.composeapp.generated.resources.server_port
import photovoltaic_kmp_app.composeapp.generated.resources.tab_query
import photovoltaic_kmp_app.composeapp.generated.resources.tab_setting

@Composable
fun SetCollectorPage(
    navHostController: NavHostController
) {
    val wifiMap = remember { mutableStateMapOf<String, String>() }
    var wifiStatus by remember { mutableStateOf("") }
    val relayMap = remember { mutableStateMapOf<String, String>() }
    var selectedTabIndex by remember { mutableStateOf(0) }
    val coroutineScope = rememberCoroutineScope()


    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.collector_config), backClick = { navHostController.popBackStack() })
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // TabRow部分
            TabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.background(Color.White),
                // 移除指示器以隐藏选定标签底部的彩色线条
                indicator = { },
                // 设置分隔符为空，使用等权重居中标签
                divider = { }
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.White),
                    horizontalArrangement = Arrangement.Center
                ) {
                    // 第一个标签
                    Tab(
                        selected = selectedTabIndex == 0,
                        onClick = { selectedTabIndex = 0 },
                        text = {
                            Text(
                                text = stringResource(Res.string.tab_setting),
                                color = if (selectedTabIndex == 0) LjRed else Color(0xFF8A8A8A)
                            )
                        },
                        modifier = Modifier
                            .background(Color.White)
                            .weight(1f)
                    )

                    // 标签之间的分隔符 - 较小的高度并居中
                    VerticalDivider(
                        modifier = Modifier
                            .fillMaxHeight(0.3f)
                            .background(Color.White)
                            .align(Alignment.CenterVertically),
                        thickness = 1.dp,
                        color = Color.LightGray
                    )

                    // 第二个标签
                    Tab(
                        selected = selectedTabIndex == 1,
                        onClick = { selectedTabIndex = 1 },
                        text = {
                            Text(
                                text = stringResource(Res.string.tab_query),
                                color = if (selectedTabIndex == 1) LjRed else Color(0xFF8A8A8A)
                            )
                        },
                        modifier = Modifier
                            .background(Color.White)
                            .weight(1f)
                    )
                }
            }

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 60.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                var showSuccessDialog by remember { mutableStateOf(false) }
                if (showSuccessDialog) {
                    ResultDialog(true) { showSuccessDialog = false }
                }

                var showFailDialog by remember { mutableStateOf(false) }
                if (showFailDialog) {
                    ResultDialog(false) { showFailDialog = false }
                }

                var showLoadingDialog by remember { mutableStateOf(false) }
                if (showLoadingDialog) {
                    LoadingDialog(loadingText = stringResource(Res.string.sending)) { showLoadingDialog = false }
                }

                when (selectedTabIndex) {
                    0 -> {
                        // 设置采集器的UI
                            WifiConfigCard(
                                onJoinClick = { ssid, password ->
                                    wifiMap["wifiName"] = ssid
                                    wifiMap["wifiPassword"] = password
                                    
                                    showLoadingDialog = true
                                    setCollectorWifi(
                                        wifiName = ssid.trim(),
                                        wifiPassword = password.trim()
                                    ) { isSuccess ->
                                        showLoadingDialog = false
                                        handleOperationResult(isSuccess, coroutineScope, showSuccessDialog = { showSuccessDialog = it }, showFailDialog = { showFailDialog = it })
                                    }
                                }
                            )

                        Spacer(modifier = Modifier.height(10.dp))


                        // 中继器设置部分
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(Color.White, shape = RoundedCornerShape(8.dp))
                                .padding(top = 16.dp, start = 16.dp, end = 16.dp, bottom = 16.dp)
                        ) {
//                            Text(
//                                text = stringResource(Res.string.relay_settings),
//                                modifier = Modifier.padding(bottom = 16.dp)
//                            )

                            // 中继器1-8的输入框
                            (1..8).forEach { index ->
                                CustomInput(
                                    value = relayMap["relay$index"],
                                    labelWidth = 80.dp,
                                    label = stringResource(when(index) {
                                        1 -> Res.string.relay_1
                                        2 -> Res.string.relay_2
                                        3 -> Res.string.relay_3
                                        4 -> Res.string.relay_4
                                        5 -> Res.string.relay_5
                                        6 -> Res.string.relay_6
                                        7 -> Res.string.relay_7
                                        else -> Res.string.relay_8
                                    }),
                                    textAlign = TextAlign.Right,
                                    onChange = { relayMap["relay$index"] = it }
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(16.dp))

                        // 中继器保存按钮
                        val isRelayEnabled = relayMap.any { it.value.trim().isNotEmpty() }
                        
                        ConfirmButton(stringResource(Res.string.save_relay), isRelayEnabled) {
                            showLoadingDialog = true
                            sendRelaySettings(
                                relayMap = relayMap
                            ) { isSuccess ->
                                showLoadingDialog = false
                                handleOperationResult(isSuccess, coroutineScope, showSuccessDialog = { showSuccessDialog = it }, showFailDialog = { showFailDialog = it })
                            }
                        }

                        Spacer(modifier = Modifier.height(16.dp))

                        // 服务器设置部分
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(Color.White, shape = RoundedCornerShape(8.dp))
                                .padding(top = 16.dp, start = 16.dp, end = 16.dp, bottom = 16.dp)
                        ) {
//                            Text(
//                                text = stringResource(Res.string.server_settings),
//                                modifier = Modifier.padding(bottom = 16.dp)
//                            )

                            CustomInput(
                                value = wifiMap["serverIp"],
                                labelWidth = 80.dp,
                                label = stringResource(Res.string.server_ip),
                                textAlign = TextAlign.Right,
                                onChange = { wifiMap["serverIp"] = it }
                            )

                            CustomInput(
                                value = wifiMap["serverPort"],
                                labelWidth = 80.dp,
                                label = stringResource(Res.string.server_port),
                                textAlign = TextAlign.Right,
                                onChange = { wifiMap["serverPort"] = it }
                            )
                        }

                        Spacer(modifier = Modifier.height(10.dp))

                        // 服务器设置保存按钮
                        val isServerEnabled = (wifiMap["serverIp"]?.trim()?.isNotEmpty() ?: false) && 
                                           (wifiMap["serverPort"]?.trim()?.isNotEmpty() ?: false)

                        ConfirmButton(stringResource(Res.string.save_server), isServerEnabled) {
                            showLoadingDialog = true
                            setServerConfig(
                                serverIp = wifiMap["serverIp"]?.trim() ?: "",
                                serverPort = wifiMap["serverPort"]?.trim() ?: ""
                            ) { isSuccess ->
                                showLoadingDialog = false
                                handleOperationResult(isSuccess, coroutineScope, showSuccessDialog = { showSuccessDialog = it }, showFailDialog = { showFailDialog = it })
                            }
                        }
                    }
                    1 -> {
                        val scrollState = rememberScrollState()

                        LaunchedEffect(wifiStatus) {
                            scrollState.animateScrollTo(scrollState.maxValue)
                        }

                        CommonTitleField(
                            value = wifiStatus,
                            onValueChange = { },
                            titleText = stringResource(Res.string.query_window),
                            placeholderCom = {
//                                PlaceholderText(Res.string.query_window_placeholder)
                            },
                            modifier = Modifier
                                .padding(top = 5.dp),
                            isReadOnly = true,
                            textFieldHeight = 350,
                            cornerRadius = 20,
                            isSingleLine = false,
                            interactionSource = remember { MutableInteractionSource() },
                            scrollState= scrollState
                        )

                        Spacer(modifier = Modifier.height(20.dp))

                        // 查询按钮
                        ConfirmButton(stringResource(Res.string.query), true) {
                            showLoadingDialog = true
                            queryCollectorInfo() { status ->
                                showLoadingDialog = false
                                wifiStatus = status
                            }
                        }
                    }
                }
            }
        }
    }
}

// 修改 setCollectorWifi 方法
fun setCollectorWifi(wifiName: String, wifiPassword: String, callback: (Boolean) -> Unit) {
    val byteArray = ByteArray(97)
    byteArray[0] = 0x0c
    
    val wifiNameBytes = wifiName.toByteArray(Charsets.UTF_8)
    val wifiPasswordBytes = wifiPassword.toByteArray(Charsets.UTF_8)
    
    wifiNameBytes.copyInto(
        destination = byteArray,
        destinationOffset = 1,
        startIndex = 0,
        endIndex = minOf(64, wifiNameBytes.size)
    )
    
    wifiPasswordBytes.copyInto(
        destination = byteArray,
        destinationOffset = 65,
        startIndex = 0,
        endIndex = minOf(32, wifiPasswordBytes.size)
    )

    KtorSocketUtils.sendSocketCommand(byteArray) { result ->
        when (result) {
            is KtorSocketUtils.SocketResult.Success -> callback(result.response.contains("OK"))
            is KtorSocketUtils.SocketResult.Error -> callback(false)
        }
    }
}

// 修改 queryCollectorInfo 方法
fun queryCollectorInfo( callback: (String) -> Unit) {
    val byteArray = ByteArray(1)
    byteArray[0] = 0x11

    KtorSocketUtils.sendSocketCommand( byteArray) { result ->
        when (result) {
            is KtorSocketUtils.SocketResult.Success -> callback(result.response)
            is KtorSocketUtils.SocketResult.Error -> {
                CoroutineScope(Dispatchers.Main).launch {
                    callback("采集器查询错误")
                }
            }
        }
    }
}

// 修改 sendRelaySettings 方法
fun sendRelaySettings( relayMap: Map<String, String>, callback: (Boolean) -> Unit) {
    val validRelays = relayMap.filter { (_, value) -> value.trim().isNotEmpty() }
    val relayCount = validRelays.size
    
    val byteArray = ByteArray(42)
    byteArray[0] = 0x0d
    byteArray[1] = relayCount.toByte()
    
    validRelays.entries.forEachIndexed { index, entry ->
        val relayId = entry.value.trim()
        try {
            val bytes = KtorSocketUtils.hexStringToBytes(relayId)
            if (bytes != null) {
                bytes.copyInto(
                    destination = byteArray,
                    destinationOffset = 2 + (index * 5),
                    startIndex = 0,
                    endIndex = minOf(5, bytes.size)
                )
            }
        } catch (e: Exception) {
//            Log.e("wifi11", "Error converting relayId: ${e.message}")
        }
    }

//    val hexString = byteArray.joinToString("") { "%02X".format(it) }
//    Log.d("wifi11", "Final byteArray: $hexString")

    KtorSocketUtils.sendSocketCommand( byteArray) { result ->
        if(result is KtorSocketUtils.SocketResult.Success) {
            callback(result.response.contains("OK"))
        } else {
            callback(false)
        }
    }
}

// 添加新的服务器配置发送方法
fun setServerConfig( serverIp: String, serverPort: String, callback: (Boolean) -> Unit) {
    val byteArray = ByteArray(22)  // 与SSID结构体大小相同
    byteArray[0] = 0x0E  // 设置命令为0x0E

    val serverIpBytes = serverIp.toByteArray(Charsets.UTF_8)
    val serverPortBytes = serverPort.toByteArray(Charsets.UTF_8)

    serverIpBytes.copyInto(
        destination = byteArray,
        destinationOffset = 1,
        startIndex = 0,
        endIndex = minOf(16, serverIpBytes.size)
    )
    
    serverPortBytes.copyInto(
        destination = byteArray,
        destinationOffset = 17,
        startIndex = 0,
        endIndex = minOf(5, serverPortBytes.size)
    )

    KtorSocketUtils.sendSocketCommand( byteArray) { result ->
        if(result is KtorSocketUtils.SocketResult.Success) {
            callback(result.response.contains("OK"))
        } else {
            callback(false)
        }
    }
}

/**
 * 处理操作结果的辅助函数，显示成功或失败对话框并在延迟后自动关闭
 *
 * @param isSuccess 操作是否成功
 * @param coroutineScope 协程作用域，用于启动延迟关闭对话框的协程
 * @param showSuccessDialog 控制成功对话框显示的函数
 * @param showFailDialog 控制失败对话框显示的函数
 */
fun handleOperationResult(
    isSuccess: Boolean,
    coroutineScope: CoroutineScope,
    showSuccessDialog: (Boolean) -> Unit,
    showFailDialog: (Boolean) -> Unit
) {
    if (isSuccess) {
        showSuccessDialog(true)
        coroutineScope.launch {
            delay(2000)
            showSuccessDialog(false)
        }
    } else {
        showFailDialog(true)
        coroutineScope.launch {
            delay(2000)
            showFailDialog(false)
        }
    }
}