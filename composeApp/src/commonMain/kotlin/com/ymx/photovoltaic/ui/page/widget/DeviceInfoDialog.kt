package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.collector
import photovoltaic_kmp_app.composeapp.generated.resources.collector_name
import photovoltaic_kmp_app.composeapp.generated.resources.group
import photovoltaic_kmp_app.composeapp.generated.resources.group_name
import photovoltaic_kmp_app.composeapp.generated.resources.imei
import photovoltaic_kmp_app.composeapp.generated.resources.inverter
import photovoltaic_kmp_app.composeapp.generated.resources.inverter_name
import photovoltaic_kmp_app.composeapp.generated.resources.power

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceInfoDialog(
    showDialog: Boolean,
    onDismiss: () -> Unit,
    deviceData: DeviceNodeData
) {
    if (showDialog) {
        ModalBottomSheet(
            onDismissRequest = onDismiss,
            sheetState = rememberModalBottomSheetState(),
            dragHandle = null
        ) {
            Column(
                modifier = Modifier
                    .background(Color.White)
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                when (deviceData) {
                    is DeviceNodeData.InverterNode -> {
                        deviceData.inverter?.let { inverter ->
                            Text(
                                text = stringResource(Res.string.inverter),
                                style = MaterialTheme.typography.titleMedium
                            )
                            InfoItem(stringResource(Res.string.inverter_name), inverter.inverterName)
//                            InfoItem(stringResource(Res.string.power), inverter.power.toString())
                        }
                    }
                    is DeviceNodeData.CollectorNode -> {
                        deviceData.collector?.let { collector ->
                            Text(
                                text = stringResource(Res.string.collector),
                                style = MaterialTheme.typography.titleMedium
                            )
                            InfoItem(stringResource(Res.string.collector_name), collector.cloudName)
                            InfoItem(stringResource(Res.string.imei), collector.imei)
                        }
                    }
                    is DeviceNodeData.GroupNode -> {
                        deviceData.group?.let { group ->
                            Text(
                                text = stringResource(Res.string.group),
                                style = MaterialTheme.typography.titleMedium
                            )
                            InfoItem(stringResource(Res.string.group_name), group.groupName)
                            InfoItem(stringResource(Res.string.power), group.power.toString())
                        }
                    }
                    else -> {}
                }
            }
        }
    }
} 