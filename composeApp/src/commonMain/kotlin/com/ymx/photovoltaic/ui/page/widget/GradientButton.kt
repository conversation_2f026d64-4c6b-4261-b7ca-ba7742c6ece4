package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun GradientButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    height: Dp = 50.dp,
    textStyle: TextStyle = TextStyle(
        fontSize = 16.sp,
        fontWeight = FontWeight.Medium,
        color = Color.White
    ),
    contentPadding: PaddingValues = PaddingValues(vertical = 4.dp),
    cornerRadius: Dp = 25.dp,
    languageState: Any? = null // 可选参数，用于触发语言变化时的重组
) {
    Box(
        modifier = modifier
            .height(height)
            .background(
                brush = Brush.verticalGradient(
                    0.4468f to Color(0xFFEF5864),
                    100f to Color(0xFFE60012),
                ),
                shape = RoundedCornerShape(cornerRadius)
            )
    ) {
        Button(
            modifier = Modifier.fillMaxSize(),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.Transparent,
                contentColor = Color.White,
                disabledContainerColor = Color.Transparent,
                disabledContentColor = Color.White
            ),
            enabled = enabled,
            shape = RoundedCornerShape(cornerRadius),
            contentPadding = contentPadding,
            onClick = onClick
        ) {
            // 监听语言变化，如果提供了语言状态
            if (languageState != null) {
                LaunchedEffect(languageState) {
                    // 空实现，但会在语言变化时触发重组
                }
            }
            
            Text(
                text = text,
                style = textStyle
            )
        }
    }
} 