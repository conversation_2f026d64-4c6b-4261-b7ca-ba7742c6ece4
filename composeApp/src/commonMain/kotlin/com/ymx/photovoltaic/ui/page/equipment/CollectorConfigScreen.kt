package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.bean.Collector
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonSearch
import com.ymx.photovoltaic.ui.page.widget.DeviceItem
import com.ymx.photovoltaic.ui.page.widget.DeviceTextButtonRow
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.util.DateTimeUtil.formatDaystamp
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.collector
import photovoltaic_kmp_app.composeapp.generated.resources.input_collector_id
import photovoltaic_kmp_app.composeapp.generated.resources.sending
import photovoltaic_kmp_app.composeapp.generated.resources.smart_gateway_config

/**
 * 智能网关配置页面
 */
@Composable
fun CollectorConfigScreen(
    navHostController: NavHostController,
    collectorList: List<Collector>,
    equipmentViewModel: EquipmentViewModel = getKoin().get()

) {
    var searchQuery by remember { mutableStateOf("") } // 用于存储搜索框中的输入

    val filteredItems = collectorList.filter {
        it.imei.contains(searchQuery, ignoreCase = true)
        // 过滤IMEI字段
    }


    val relayList by equipmentViewModel.relayListFlow.collectAsState()
    val relayChipIdMap by equipmentViewModel.relayChipIdMapFlow.collectAsState()

    LaunchedEffect(collectorList) {
        if (collectorList.isNotEmpty()) {
            equipmentViewModel.fetchAllRelayComponentData(
                cloudId = collectorList[0].imei,
                createUserId = AppGlobal.mId
            )
        }
    }

    Scaffold(
        topBar = {
            TopBar(
                textStr = stringResource(Res.string.smart_gateway_config),
                backClick = { navHostController.popBackStack() },
            )
        },
        containerColor = Grey_F5,
        floatingActionButton = {
//            FloatingButton(
//                onAddClicked = {
//                    // 导航到新增智能网关页面
//                    navHostController.navigate(Route.COLLECTOR_NEW)
//                }
//            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 0.dp)
        ) {
            CommonSearch(
                searchQuery, 
                onQueryChange = { searchQuery = it }, 
                placeholderTextResId = Res.string.input_collector_id,
                modifier = Modifier
                    .padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 10.dp)
                    .fillMaxWidth()
                    .height(48.dp)
                    .clip(RoundedCornerShape(25.dp))
            )

            var showSuccessDialog by remember { mutableStateOf(false) }
    if (showSuccessDialog) {
        com.ymx.photovoltaic.ui.page.widget.ResultDialog(true) { showSuccessDialog = false }
    }

    var showFailDialog by remember { mutableStateOf(false) }
    if (showFailDialog) {
        com.ymx.photovoltaic.ui.page.widget.ResultDialog(false) { showFailDialog = false }
    }

    var showLoadingDialog by remember { mutableStateOf(false) }
    if (showLoadingDialog) {
        com.ymx.photovoltaic.ui.page.widget.LoadingDialog(loadingText = stringResource(Res.string.sending)) { showLoadingDialog = false }
    }

            LazyColumn {
                items(filteredItems) { collector ->
                    DeviceItem(
                        imagePainter = painterResource(Res.drawable.collector),
                        secondRowText = "型号: ${collector.model}",
                        firstRow = {
                            DeviceTextButtonRow(
                                textStr = "IMEI:${collector.imei}",
                                buttonText = "配置采集器",
                                onButtonClick = {
                                    navHostController.navigate(Route.SET_COLLECTOR)
                                },
                                isTextBold = false
                            )
                        },
                        lastRow = {
                            DeviceTextButtonRow(
                                textStr = "创建时间:${formatDaystamp(collector.createTime)}",
                                buttonText = "配置中继器",
                                onButtonClick = {
                                    // 将数据保存到 AppGlobal 中
                                    AppGlobal.relayList = relayList
                                    AppGlobal.relayChipIdMap = relayChipIdMap
                                    navHostController.navigate(Route.SET_RELAY+ "/${collector.imei}")
                                },
                                isTextBold = false
                            )
                        },
                    )
                }
            }
        }
    }
}
