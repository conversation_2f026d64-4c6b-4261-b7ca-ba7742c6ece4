package com.ymx.photovoltaic.ui.page.home.edit

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.CustomInput
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.ui.page.widget.rememberSingleColumnPickerState
import com.ymx.photovoltaic.viewmodel.EditViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.save
import photovoltaic_kmp_app.composeapp.generated.resources.temp_shutdown
import photovoltaic_kmp_app.composeapp.generated.resources.temp_warning
import photovoltaic_kmp_app.composeapp.generated.resources.temp_warning_set
import photovoltaic_kmp_app.composeapp.generated.resources.unit_celsius


@Composable
fun TempWarnPage(
    navHostController: NavHostController,
    editViewModel: EditViewModel = getKoin().get()
) {
    val warningSetting by editViewModel.warningSettingFlow.collectAsState()

    LaunchedEffect(Unit) {
        editViewModel.fetchWarningSetting(AppGlobal.powerStationId)
    }

    var tempValue by remember { mutableStateOf(warningSetting?.componentTemperature.toString()) }
    var offValue by remember { mutableStateOf(warningSetting?.offValue.toString()) }

    LaunchedEffect(warningSetting) {
        tempValue = warningSetting?.componentTemperature.toString()
        offValue = warningSetting?.offValue.toString()
    }

    val picker = rememberSingleColumnPickerState()
    
    // 创建104到1的数值列表
    val range = remember {
        (1..104).map { it.toString() }
    }

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.temp_warning), backClick = {navHostController.popBackStack()})
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 60.dp)
        ) {
            var showSuccessDialog by remember { mutableStateOf(false) }
            var showFailDialog by remember { mutableStateOf(false) }

            if (showSuccessDialog) {
                ResultDialog(true) { showSuccessDialog = false }
            }
            if (showFailDialog) {
                ResultDialog(false) { showFailDialog = false }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, shape = RoundedCornerShape(8.dp))
                    .padding(start = 16.dp, end = 16.dp,top = 16.dp, bottom = 22.dp)
            ) {
                val tempWarning = stringResource(Res.string.temp_warning_set)
                CustomInput(
                    label = tempWarning,
                    value = "≥ ${tempValue}${stringResource(Res.string.unit_celsius)}",
                    textAlign = TextAlign.Right,
                    labelWidth = 100.dp,
                    disabled = true,
                    onClick = {
                        picker.show(
                            title = tempWarning,
                            range = range,
                            value = tempValue.toIntOrNull() ?: 0
                        ) { selected ->
                            tempValue = range[selected]
                        }
                    }
                )


                val tempShutdown = stringResource(Res.string.temp_shutdown)
                CustomInput(
                    label = tempShutdown,
                    value = "≥ ${offValue}${stringResource(Res.string.unit_celsius)}",
                    textAlign = TextAlign.Right,
                    labelWidth = 100.dp,
                    disabled = true,
                    onClick = {
                        picker.show(
                            title = tempShutdown,
                            range = range,
                            value = offValue.toIntOrNull() ?: 0
                        ) { selected ->
                            offValue = range[selected]
                        }
                    }
                )

            }

            Spacer(modifier = Modifier.weight(1f))

            val isEnabled = tempValue.trim().isNotEmpty() && offValue.trim().isNotEmpty()
            val coroutineScope = rememberCoroutineScope()
            ConfirmButton(stringResource(Res.string.save), isEnabled) {
                editViewModel.updateWarningSettingList(
                    AppGlobal.powerStationId, 
                    tempValue.toInt(),
                    offValue.toInt(),
                    null, 
                    null, 
                    errorBlock = {
                        showFailDialog = true
                        coroutineScope.launch {
                            delay(2000)
                            showFailDialog = false
                        }
                    }
                ) {
                    showSuccessDialog = true
                    coroutineScope.launch {
                        delay(2000)
                        showSuccessDialog = false
                        navHostController.popBackStack()
                    }
                }
            }
        }
    }
}


