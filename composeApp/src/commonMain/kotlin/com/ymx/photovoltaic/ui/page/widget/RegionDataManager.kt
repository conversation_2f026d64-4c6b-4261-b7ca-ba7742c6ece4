package com.ymx.photovoltaic.ui.page.widget

import com.ymx.photovoltaic.data.bean.Region

object RegionDataManager {
    // 添加初始化标志位
    private var isInitialized = false
    
    // 按level分组存储所有地区
    private val regionsByLevel = mutableMapOf<Int, List<Region>>()
    
    // 存储父子关系
    private val childrenMap = mutableMapOf<Int, List<Region>>()
    
    fun init(regions: List<Region>) {
        // 按level分组
        regionsByLevel.clear()
        regions.groupBy { it.level }.forEach { (level, list) ->
            regionsByLevel[level] = list
        }
        
        // 建立父子关系
        childrenMap.clear()
        regions.forEach { region ->
            val children = regions.filter { it.pid == region.id }
            if (children.isNotEmpty()) {
                childrenMap[region.id] = children
            }
        }
        
        // 设置初始化标志
        isInitialized = true
    }
    
    // 获取指定level的所有地区
    fun getRegionsByLevel(level: Int): List<Region> {
        return regionsByLevel[level] ?: emptyList()
    }
    
    // 获取指定地区的子地区
    fun getChildren(parentId: Int): List<Region> {
        return childrenMap[parentId] ?: emptyList()
    }
    
    // 添加重置方法，用于需要重新初始化的情况
    fun reset() {
        isInitialized = false
        regionsByLevel.clear()
        childrenMap.clear()
    }
    
    // 添加检查是否已初始化的方法
    fun isInitialized(): Boolean = isInitialized
} 