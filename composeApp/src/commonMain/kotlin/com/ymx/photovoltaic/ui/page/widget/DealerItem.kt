package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.right_arrow

/**
 * 经销商/安装商组件
 * 
 * 该组件显示经销商或安装商信息，包含左侧图标和右侧两行文字
 * 左侧图标和文字内容可以通过参数传递
 */
@Composable
fun DealerItem(
    imagePainter: Painter,
    title: String,
    description: String,
    modifier: Modifier = Modifier.fillMaxWidth().padding(horizontal = 20.dp, vertical = 15.dp),
    onClick: () -> Unit = {}
) {
    Card(
        colors = CardDefaults.cardColors(containerColor = Color.White),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp, horizontal = 10.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = modifier,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧图标
            Image(
                painter = imagePainter,
                contentDescription = "dealer icon",
                modifier = Modifier.height(70.dp).width(55.dp),
                contentScale = ContentScale.Fit
            )

            Spacer(modifier = Modifier.width(16.dp))

            // 右侧文字内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 标题
                Text(
                    text = title,
                    style = TextStyle(fontSize = 17.sp,color = Color.Black),
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(6.dp))

                // 描述
                Text(
                    text = description,
                    style = TextStyle(fontSize = 15.sp, color = Color.Gray)
                )
            }

            // 右侧箭头图标
            Image(
                painter = painterResource(Res.drawable.right_arrow),
                contentDescription = "arrow right",
                modifier = Modifier.padding(end = 8.dp)
            )
        }
    }
}