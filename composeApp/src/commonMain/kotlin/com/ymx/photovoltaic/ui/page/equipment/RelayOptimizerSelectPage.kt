package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.SecondaryIndicator
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.platform.ToastUtil
import com.ymx.photovoltaic.ui.page.theme.ButtonPrimaryColor
import com.ymx.photovoltaic.ui.page.theme.Grey_8A
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.theme.LjRed
import com.ymx.photovoltaic.ui.page.widget.CheckboxToggleButton
import com.ymx.photovoltaic.ui.page.widget.CommonSearch
import com.ymx.photovoltaic.ui.page.widget.CustomDialog
import com.ymx.photovoltaic.ui.page.widget.DeviceItem
import com.ymx.photovoltaic.ui.page.widget.DeviceTextButtonRow
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.add_optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.add_to_relay
import photovoltaic_kmp_app.composeapp.generated.resources.confirm
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_operation
import photovoltaic_kmp_app.composeapp.generated.resources.group
import photovoltaic_kmp_app.composeapp.generated.resources.group_way
import photovoltaic_kmp_app.composeapp.generated.resources.input_group_name
import photovoltaic_kmp_app.composeapp.generated.resources.input_optimizer_id
import photovoltaic_kmp_app.composeapp.generated.resources.operation_success
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer_way
import photovoltaic_kmp_app.composeapp.generated.resources.remove_from_relay
import photovoltaic_kmp_app.composeapp.generated.resources.remove_optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.select_all

data class SelectItem(
    val id: String,
    val name: String
)

@Composable
fun RelayOptimizerSelectPage(
    navHostController: NavHostController,
    relayId: String,
    createUserId: String,
    flag: Int,  // 1是新增，2是删除，
    viewModel: EquipmentViewModel = getKoin().get()
) {
    // 获取字符串资源
    val operationSuccessStr = stringResource(Res.string.operation_success)

    // 存储每个tab的选中状态
    var firstTabSelectedItems by remember { mutableStateOf(listOf<Boolean>()) }
    var secondTabSelectedItems by remember { mutableStateOf(listOf<Boolean>()) }
    
    // 全选状态
    var firstTabSelectAll by remember { mutableStateOf(false) }
    var secondTabSelectAll by remember { mutableStateOf(false) }
    
    // 当前选中的tab
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    
    var showConfirmDialog by remember { mutableStateOf(false) }
    var selectedList by remember { mutableStateOf<List<SelectItem>>(emptyList()) }

    // 搜索状态
    var firstTabSearchQuery by remember { mutableStateOf("") }
    var secondTabSearchQuery by remember { mutableStateOf("") }

    // 获取数据
    val componentList by viewModel.relayComponentListFlow.collectAsState()
    val groupList by viewModel.relayGroupListFlow.collectAsState()

    // 过滤数据
    val filteredGroups = groupList.filter {
        it.groupName.contains(firstTabSearchQuery, ignoreCase = true)
    }

    val filteredComponents = componentList.filter {
        it.chipId.contains(secondTabSearchQuery, ignoreCase = true)
    }

    // 在页面加载时获取数据
    LaunchedEffect(Unit) {
        val operationFlag = if (flag == 1) "add" else "view"
        viewModel.fetchRelayGroupList(
            relayId = relayId,
            groupName = "",
            operationFlag = operationFlag,
            createUserId = createUserId,
            pageSize = 100
        )
        viewModel.fetchRelayComponentList(
            relayId = relayId,
            createUserId = createUserId,
            operationFlag = operationFlag
        )
    }

    // 更新选中状态数组大小
    LaunchedEffect(filteredGroups.size) {
        firstTabSelectedItems = List(filteredGroups.size) { false }
    }

    LaunchedEffect(filteredComponents.size) {
        secondTabSelectedItems = List(filteredComponents.size) { false }
    }

    // 根据 flag 确定标题
    val pageTitle = if (flag == 1) {
        stringResource(Res.string.add_optimizer)
    } else {
        stringResource(Res.string.remove_optimizer)
    }

    // 固定的 tab 文本
    val tabTitles = listOf(
        stringResource(Res.string.group_way),
        stringResource(Res.string.optimizer_way)
    )

    SetStatusBar(Color.White, true)

    Scaffold(
        topBar = {
            TopBar(textStr = pageTitle, backClick = { navHostController.navigateUp() })
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .fillMaxSize()
        ) {
            // Tab栏
            TabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.background(Color.White),
                indicator = { tabPositions ->
                    SecondaryIndicator(
                        modifier = Modifier.tabIndicatorOffset(tabPositions[selectedTabIndex]),
                        color = ButtonPrimaryColor
                    )
                },
                divider = {
                    HorizontalDivider(color = Color.Transparent)
                }
            ) {
                tabTitles.forEachIndexed { index, text ->
                    Tab(
                        selected = selectedTabIndex == index,
                        onClick = { selectedTabIndex = index },
                        text = {
                            Text(
                                text,
                                color = if (selectedTabIndex == index) LjRed else Grey_8A
                            )
                        },
                        modifier = Modifier.background(Color.White)
                    )
                }
            }

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                // 搜索框
                Spacer(modifier = Modifier.height(10.dp))
//                Card(
//                    colors = CardDefaults.cardColors(containerColor = Color.White),
//                    modifier = Modifier.fillMaxWidth().padding(horizontal = 10.dp, vertical = 10.dp),
//                ) {
                    CommonSearch(
                        searchQuery = if (selectedTabIndex == 0) firstTabSearchQuery else secondTabSearchQuery,
                        onQueryChange = { 
                            if (selectedTabIndex == 0) {
                                firstTabSearchQuery = it
                            } else {
                                secondTabSearchQuery = it
                            }
                        },
                        placeholderTextResId = if (selectedTabIndex == 0) Res.string.input_group_name
                        else Res.string.input_optimizer_id,
                        Modifier.padding(start = 10.dp, end = 10.dp).fillMaxWidth().height(48.dp)
                            .clip(RoundedCornerShape(25.dp))
                    )
//                }

                // 列表内容
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                ) {
                    // 全选项
                    item {
                        Card(
                            colors = CardDefaults.cardColors(containerColor = Color.White),
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 15.dp, bottom = 5.dp, start = 10.dp, end = 10.dp).
                                clip(RoundedCornerShape(25.dp))
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 2.dp, horizontal = 10.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                CheckboxToggleButton(
                                    isChecked = if (selectedTabIndex == 0) firstTabSelectAll else secondTabSelectAll,
                                    onCheckedChange = { isChecked ->
                                        if (selectedTabIndex == 0) {
                                            firstTabSelectAll = isChecked
                                            firstTabSelectedItems = List(filteredGroups.size) { isChecked }
                                        } else {
                                            secondTabSelectAll = isChecked
                                            secondTabSelectedItems = List(filteredComponents.size) { isChecked }
                                        }
                                    }
                                )
                                Text(text = stringResource(Res.string.select_all))
                            }
                        }
                    }

                    // 根据当前选中的tab显示不同的列表项
                    if (selectedTabIndex == 0) {
                        itemsIndexed(filteredGroups) { index, item ->
                            // 使用DeviceItem替换Card
                            DeviceItem(
                                imagePainter = painterResource(Res.drawable.group),
                                secondRowText = "创建时间:${item.createTimeCh}",
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 10.dp, vertical = 5.dp),
                                showCheckbox = true,
                                isChecked = firstTabSelectedItems.getOrNull(index) ?: false,
                                onCheckedChange = { isChecked ->
                                    val newList = firstTabSelectedItems.toMutableList()
                                    newList[index] = isChecked
                                    firstTabSelectedItems = newList
                                    firstTabSelectAll = newList.all { it }
                                },
                                firstRow = {
                                    DeviceTextButtonRow(
                                        textStr = item.groupName,
                                        isHaveButton = false
                                    )
                                }
                            )
                        }
                    } else {
                        itemsIndexed(filteredComponents) { index, item ->
                            // 使用DeviceItem替换Card
                            DeviceItem(
                                imagePainter = painterResource(Res.drawable.optimizer),
                                secondRowText = "型号:${item.model}",
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 10.dp, vertical = 5.dp),
                                showCheckbox = true,
                                isChecked = secondTabSelectedItems.getOrNull(index) ?: false,
                                onCheckedChange = { isChecked ->
                                    val newList = secondTabSelectedItems.toMutableList()
                                    newList[index] = isChecked
                                    secondTabSelectedItems = newList
                                    secondTabSelectAll = newList.all { it }
                                },
                                firstRow = {
                                    DeviceTextButtonRow(
                                        textStr = "ID#:${item.chipId}",
                                        isHaveButton = false
                                    )
                                },
                                lastRow = {
                                    DeviceTextButtonRow(
                                        textStr = "创建时间:${item.createTimeCh}",
                                        isHaveButton = false,
                                        isTextBold = false
                                    )
                                }
                            )
                        }
                    }
                }
            }

            // 底部按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Button(
                    onClick = {
                        selectedList = when (selectedTabIndex) {
                            0 -> filteredGroups.filterIndexed { index, _ -> 
                                firstTabSelectedItems.getOrNull(index) ?: false 
                            }.map { group ->
                                SelectItem(group.id, group.groupName)
                            }
                            1 -> filteredComponents.filterIndexed { index, _ -> 
                                secondTabSelectedItems.getOrNull(index) ?: false 
                            }.map { optimizer ->
                                SelectItem(optimizer.id, optimizer.chipId)
                            }
                            else -> emptyList()
                        }
                        if (selectedList.isNotEmpty()) {
                            showConfirmDialog = true
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(50.dp)
                        .padding(horizontal = 16.dp),
                    colors = ButtonDefaults.buttonColors(containerColor = ButtonPrimaryColor)
                ) {
                    Text(
                        text = stringResource(Res.string.confirm),
                        fontSize = 16.sp
                    )
                }
            }
        }
    }

    // 确认对话框
    if (showConfirmDialog) {
        val operation = if (flag == 1) 
            stringResource(Res.string.add_to_relay) 
        else 
            stringResource(Res.string.remove_from_relay)
        val names = selectedList.joinToString(",") { it.name.trim() }
        val ids = selectedList.joinToString(",") { it.id.trim() }


        CustomDialog(
            confirmStr = stringResource(Res.string.confirm_operation).replace("%1\$s", names).replace("%2\$s", operation),
            onConfirm = {
                showConfirmDialog = false
                // 根据当前选中的tab调用不同的方法
                if (selectedTabIndex == 0) {
                    // 组串方式 - 调用 changeComponentGroup
                    viewModel.changeComponentGroup(
                        id = ids,
                        relayId = if (flag == 1) relayId else null,
                        createUserId = AppGlobal.mId,
                        successCall = {
                            navHostController.navigateUp()
                        }
                    )
                } else {
                    // 优化器方式 - 调用 changeComponent
                    viewModel.changeComponent(
                        id = ids,
                        relayId = if (flag == 1) relayId else null,
                        createUserId = AppGlobal.mId,
                        successCall = {
                            ToastUtil.showLong(operationSuccessStr)
                            navHostController.navigateUp()
                        }
                    )
                }
            },
            onCancel = {
                showConfirmDialog = false
            }
        )
    }
} 