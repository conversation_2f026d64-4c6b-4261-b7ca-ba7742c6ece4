package com.ymx.photovoltaic.ui.page.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.compose.currentBackStackEntryAsState
import com.ymx.photovoltaic.data.local.CacheManager
import com.ymx.photovoltaic.navigation.NavBarItem
import com.ymx.photovoltaic.ui.page.theme.LjRed
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource

/**
 * 通用底部导航栏组件
 * @param navController 导航控制器
 * @param modifier 修饰符
 */
@Composable
fun CommonBottomBar(
    navController: NavController,
    modifier: Modifier = Modifier,
    isFirstMenu:Boolean=true
) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val destination = navBackStackEntry?.destination
    
    // 定义底部导航栏项目
    val navBarList = if(isFirstMenu) listOf(
        NavBarItem.Home,
        NavBarItem.Equipment,
        NavBarItem.Message,
        NavBarItem.My
    )
    else listOf(
        NavBarItem.Report,
        NavBarItem.View,
        NavBarItem.Edit,
    )

    // 根据用户类型过滤导航栏项目
    val userType = CacheManager.getUser()?.type ?: 0
    val filteredNavBarList = when (userType) {
        1 -> navBarList.filter {
            it != NavBarItem.Equipment && it != NavBarItem.Edit
        }
        else -> navBarList
    }

    if (destination != null) {
        BottomNavigationBar(
            navController = navController,
            navDestination = destination,
            navBarList = filteredNavBarList,
            modifier = modifier
        )
    }
}

/**
 * 底部导航栏实现
 */
@Composable
private fun BottomNavigationBar(
    navController: NavController,
    navDestination: NavDestination,
    navBarList: List<NavBarItem>,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth().background(Color.White)
    ) {

    // 使用Material 3的NavigationBar
    NavigationBar(
        containerColor = Color.White,
        contentColor = Color.White,
        tonalElevation = 0.dp, // 移除阴影
        modifier = Modifier
            .fillMaxWidth().navigationBarsPadding().height(50.dp),
        windowInsets = WindowInsets(top = 0.dp, bottom = 0.dp)
    ) {
        navBarList.forEach { item ->
            val itemSelected = navDestination.hierarchy.any { it.route?.contains(item.route) == true }
            
            // 使用自定义内容来替代默认的NavigationBarItem布局
            NavigationBarItem(
                selected = itemSelected,
                icon = { 
                    // 使用自定义布局减少间距
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Bottom,
                        modifier = Modifier.fillMaxSize()
                    ) {
                        // 添加图标上方的间距

                        Icon(
                            painter = if(itemSelected) painterResource(item.selectedIcon)
                            else painterResource(item.unSelectedIcon),
                            contentDescription = item.route,
                            modifier = Modifier.size(20.dp),
                            tint = Color.Unspecified
                        )
                        
                        Box(modifier = Modifier.height(2.dp)) // 图标和文字之间的间距
                        
                        Text(
                            text = stringResource(item.label),
                            fontSize = 10.sp
                        )
                    }
                },
                colors = NavigationBarItemDefaults.colors(
                    selectedIconColor = LjRed,
                    selectedTextColor = LjRed,
                    indicatorColor = Color.White,
                    unselectedIconColor = Color.Gray,
                    unselectedTextColor = Color.Gray
                ),
                label = { /* 不使用默认label */ },
                onClick = {
                    navController.navigate(item.route) {
                        // 这里让多个Tab下返回时，不是回到首页，而是直接退出
                        navDestination.route?.let {
                            popUpTo(it) {
                                // 跳转时保存页面状态
                                saveState = true
                                // 回退到栈顶时，栈顶页面是否也关闭
                                inclusive = true
                            }
                        }
                        // 栈顶复用，避免重复点击同一个导航按钮，回退栈中多次创建实例
                        launchSingleTop = true
                        // 回退时恢复页面状态
                        restoreState = true
                    }
                }
            )
        }
    }
    }
} 