package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.widget.CommonSearch
import com.ymx.photovoltaic.ui.page.widget.ComponentItem
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.create_time
import photovoltaic_kmp_app.composeapp.generated.resources.input_optimizer_id
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer_management

@Composable
fun OptimizerForRelayPage(
    navHostController: NavHostController,
    relayId: String,
    equipmentViewModel: EquipmentViewModel = getKoin().get()
) {
    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.optimizer_management),
                backClick = { navHostController.popBackStack() })
        },
        backgroundColor = Color(242, 243, 245, 1)
    ) { paddingValues ->
        OptimizerForRelayContent(
            navHostController = navHostController,
            relayId = relayId,
            equipmentViewModel = equipmentViewModel,
            modifier = Modifier.padding(paddingValues)
        )
    }
}

@Composable
fun OptimizerForRelayContent(
    navHostController: NavHostController,
    relayId: String,
    equipmentViewModel: EquipmentViewModel,
    modifier: Modifier = Modifier
) {
    val optimizerList by equipmentViewModel.relayComponentListFlow.collectAsState()
    var searchQuery by remember { mutableStateOf("") }

    val filteredOptimizers = optimizerList.filter {
        it.chipId.contains(searchQuery, ignoreCase = true)
    }

    LaunchedEffect(Unit) {
        equipmentViewModel.fetchRelayComponentList(
            relayId = relayId,
            operationFlag = "view",
            createUserId = AppGlobal.mId
        )
    }

    Column(modifier = modifier) {
        Spacer(modifier = Modifier.height(5.dp))
        CommonSearch(
            searchQuery = searchQuery,
            onQueryChange = { searchQuery = it },
            placeholderTextResId = Res.string.input_optimizer_id,
            Modifier.
            padding(start = 10.dp, end = 10.dp).fillMaxWidth().height(55.dp)
                .clip(RoundedCornerShape(25.dp))
        )

        LazyColumn(modifier = Modifier.padding(start = 10.dp, end = 10.dp)) {
            items(filteredOptimizers) { optimizer ->
                ComponentItem(
                    "optimizer",
                    optimizer.chipId,
                    stringResource(Res.string.create_time) + optimizer.createTimeCh,
                    menuItems = emptyList(),
                    onClick = {
                        navHostController.navigate(
                            Route.OPTIMIZER_EDIT + "/${optimizer.id}/${optimizer.chipId}/${optimizer.model}/${optimizer.status}"
                        )
                    }
                )
            }
        }
    }
} 