package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp


@Composable
fun PickerInput(selectedDate:String = "", isShowIcon:Boolean=true,
                onClickDo: () -> Unit,
                onPreviousDate: (() -> Unit)? = null,
                onNextDate: (() -> Unit)? = null) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.wrapContentSize()
    ) {
        // 左箭头按钮
        IconButton(
            onClick = { onPreviousDate?.invoke() },
            modifier = Modifier.wrapContentSize()
        ) {
            Icon(
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowLeft,
                contentDescription = "Previous Date",
                tint = Color.Gray
            )
        }
        
        // 中间日期按钮
        Button(
            onClick = { onClickDo() },
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.Transparent
            ),
            contentPadding = PaddingValues(horizontal = 4.dp),
            elevation = ButtonDefaults.buttonElevation(0.dp)
        ) {
            Text(text = selectedDate, color = Color.Black)
        }
        
        // 右箭头按钮
        IconButton(
            onClick = { onNextDate?.invoke() },
            modifier = Modifier.wrapContentSize()
        ) {
            Icon(
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = "Next Date",
                tint = Color.Gray
            )
        }
    }
}


