package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.local.CacheManager
import com.ymx.photovoltaic.ui.page.theme.Grey_89
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_logout
import photovoltaic_kmp_app.composeapp.generated.resources.ic_right_arrow

@Composable
fun MenuItem(
    icon: Painter,
    text: String,
    versionText: String="",
    onItemClick: () -> Unit = {},
    isShowExitDialog: Boolean = false,
    modifier: Modifier = Modifier
) {
    var shouldExitDialog by remember { mutableStateOf(false) }

    Card(
        shape = RoundedCornerShape(25.dp),
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp)
            .clickable {
                if (isShowExitDialog) shouldExitDialog = true
                else onItemClick()
            }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 13.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标
            Icon(
                painter = icon,
                contentDescription = text,
                modifier = Modifier.size(24.dp),
                tint = Grey_89
            )

            Spacer(modifier = Modifier.width(16.dp))

            // 文字
            Text(text = text, modifier = Modifier.weight(1f), fontSize = 16.sp)

            if(versionText.isNotEmpty())
            {
                Text(text = versionText, modifier = Modifier.padding(end=10.dp),
                    color = Color.Gray,
                    fontSize = 15.sp)
            }

            Icon(
                painter = painterResource( Res.drawable.ic_right_arrow),
                contentDescription = "Go",
                modifier = Modifier.size(16.dp),
                tint = Color.Gray
            )
        }
    }

    if (shouldExitDialog)
        CustomDialog(
            confirmStr = stringResource(Res.string.confirm_logout),
            onConfirm = {
                // 执行退出登录逻辑
                shouldExitDialog = false
                CacheManager.logout()
                AppGlobal.mId = ""
                // 重置 HomePageState 状态
                // HomePageState.hasShownWarningDialog = false
                onItemClick()
            },
            onCancel = {
                // 关闭弹窗
                shouldExitDialog = false
            }
        )
}
