package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_offline


@Composable
fun SolarPanelItem(
    power: String,
    voltage: String,
    id: String,
    backgroundImage: Painter = painterResource(Res.drawable.station_view_offline),
    modifier: Modifier = Modifier.size(120.dp)
) {
    Box(modifier = modifier) {
        // 面板背景图片
        Image(
            painter = backgroundImage,
            contentDescription = "Solar Panel Background",
            contentScale = ContentScale.Fit,
            modifier = Modifier.fillMaxSize()
        )
        
        // 面板内容（功率和电压信息）
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 功率和电压显示在一个垂直居中的Column中
            Box(
                modifier = Modifier.weight(1f),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 功率显示
                    Text(
                        text = power,
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White
                        ),
                        textAlign = TextAlign.Center
                    )
                    
                    // 电压显示（紧接着功率显示）
                    Text(
                        text = voltage,
                        style = TextStyle(
                            fontSize = 12.sp,
                            color = Color.White
                        ),
                        textAlign = TextAlign.Center
                    )
                }
            }
            
            // ID文本放在图片底部的黑色区域上
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.19f),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = id,
                    style = TextStyle(
                        fontSize = 11.sp,
                        color = Color.White
                    ),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}
