package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.report_info

/**
 * 节能减排计算标准弹窗
 * 
 * @param onDismiss 关闭弹窗的回调
 * @param co2Value 减排二氧化碳数值，默认为0.997kg
 * @param coalValue 节约标准煤数值，默认为0.404kg
 * @param treeValue 等效植树数值，默认为0.054棵
 */
@Composable
fun EnergyConsumptionDialog(
    onDismiss: () -> Unit,
    co2Value: String = "0.997 kg",
    coalValue: String = "0.404 kg",
    treeValue: String = "0.054 棵"
) {
    Dialog(
        onDismissRequest = { onDismiss() },
        properties = DialogProperties(dismissOnClickOutside = true)
    ) {
        Card(
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(16.dp),
            modifier = Modifier
                .width(320.dp)
                .clickable { onDismiss() }
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.fillMaxWidth()
            ) {
                // 背景图片只作用于四个Text区域
                Box(
                    modifier = Modifier // 为标题和按钮留出空间
                        .align(Alignment.Center)
                ) {
                    Image(
                        painter = painterResource(Res.drawable.report_info),
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.size(150.dp)
                    )
                }
                
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(start = 24.dp, end = 24.dp, top = 24.dp, bottom = 6.dp)
                ) {
                    // 标题
                    Text(
                        text = "节能减排计算标准",
                        style = TextStyle(
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF4CAF50)
                        ),
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    // 文本区域 - 使用Column使所有文本左对齐
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // 说明文本
                        Text(
                            text = "光伏组件每发1度电，相当于：",
                            style = TextStyle(fontSize = 15.sp, color = Color.DarkGray)
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // 减排数据：二氧化碳
                        Text(
                            text = "减排二氧化碳            $co2Value",
                            style = TextStyle(fontSize = 15.sp, color = Color.DarkGray),
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // 节约标准煤
                        Text(
                            text = "节约标准煤               $coalValue",
                            style = TextStyle(fontSize = 15.sp, color = Color.DarkGray)
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // 等效植树
                        Text(
                            text = "等效植树                   $treeValue",
                            style = TextStyle(fontSize = 15.sp, color = Color.DarkGray)
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // 添加横线
                    HorizontalDivider(
                        modifier = Modifier
                            .fillMaxWidth(),
                        thickness = 1.dp,
                        color = Color.LightGray
                    )
                    
                    Spacer(modifier = Modifier.height(2.dp))

                    // 确认按钮
                    Button(
                        onClick = { onDismiss() },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Transparent
                        ),
                        shape = RoundedCornerShape(20.dp),
                        modifier = Modifier
                            .width(200.dp)
                            .height(40.dp)
                    ) {
                        Text(
                            text = "已知悉",
                            style = TextStyle(
                                fontSize = 15.sp,
                                color = Color.Black
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 *
 */
@Composable
fun EnergyConsumptionDialogExample() {
    var showDialog by remember { mutableStateOf(false) }
    
    Box(modifier = Modifier.fillMaxSize()) {
        Button(
            onClick = { showDialog = true },
            modifier = Modifier.align(Alignment.Center)
        ) {
            Text("显示节能减排标准")
        }
        
        if (showDialog) {
            EnergyConsumptionDialog(
                onDismiss = { showDialog = false }
            )
        }
    }
} 