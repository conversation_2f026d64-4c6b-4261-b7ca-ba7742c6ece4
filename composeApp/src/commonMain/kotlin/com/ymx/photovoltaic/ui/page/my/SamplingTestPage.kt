package com.ymx.photovoltaic.ui.page.my

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.platform.FileUtil
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.ui.page.theme.ButtonPrimaryColor
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.LoadingDialog
import com.ymx.photovoltaic.ui.page.widget.PlaceholderText
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.util.DateTimeUtil
import com.ymx.photovoltaic.util.KtorSocketUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.close_optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.open_optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.operate_optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.please_select
import photovoltaic_kmp_app.composeapp.generated.resources.query_electrical_info
import photovoltaic_kmp_app.composeapp.generated.resources.query_window
import photovoltaic_kmp_app.composeapp.generated.resources.query_window_placeholder
import photovoltaic_kmp_app.composeapp.generated.resources.sampling_test
import photovoltaic_kmp_app.composeapp.generated.resources.scan
import photovoltaic_kmp_app.composeapp.generated.resources.send_log
import photovoltaic_kmp_app.composeapp.generated.resources.sending


@Composable
fun SamplingTestPage(
    navHostController: NavHostController
) {
    var operateInfo by rememberSaveable { mutableStateOf("") }

    // 先获取操作类型字符串资源
    val openOptimizer = stringResource(Res.string.open_optimizer)
    val closeOptimizer = stringResource(Res.string.close_optimizer)
    val queryElectricalInfo = stringResource(Res.string.query_electrical_info)

    var chipIdValue by remember { mutableStateOf("") }

    val scanResult =  navHostController.currentBackStackEntry?.savedStateHandle?.
    getStateFlow("scanResult","")?.collectAsState()

    // 需要监听确切变化的值 监听scanResult?.value有效，监听监听scanResult无效
    LaunchedEffect (scanResult?.value) {
      val  chipIdValueStr=scanResult?.value.toString()
        if(chipIdValueStr.isNotEmpty())
        {
            chipIdValue=chipIdValueStr
        }
    }

    SetStatusBar(Color.White,true)

    var showLoadingDialog by remember { mutableStateOf(false) }
    if (showLoadingDialog) {
        LoadingDialog(loadingText = stringResource(Res.string.sending)) { showLoadingDialog = false }
    }

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.sampling_test),
                backClick = { navHostController.navigate(Route.MY) })
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(start = 10.dp, end = 10.dp, top = 10.dp, bottom = 20.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                // 操作结果显示UI
                val scrollState = rememberScrollState()
                
                // 监听 operateInfo 的变化，自动滚动到底部
                LaunchedEffect(operateInfo) {
                    scrollState.animateScrollTo(scrollState.maxValue)
                }
                
                CommonTitleField(
                    value = operateInfo,
                    onValueChange = { },
                    titleText = stringResource(Res.string.query_window),
                    placeholderCom = {
                        PlaceholderText(Res.string.query_window_placeholder)
                    },
                    modifier = Modifier
                        .padding(top = 5.dp),
                    isReadOnly = true,
                    textFieldHeight = 300,
                    cornerRadius = 20,
                    isSingleLine = false,
                    interactionSource = remember { MutableInteractionSource() },
                    scrollState= scrollState
                )

                Spacer(modifier = Modifier.height(10.dp))
                
                // 输入或扫描优化器
                CommonTitleField(
                    value = chipIdValue,
                    onValueChange = { newValue ->
                        // 只保留有效字符
                        val validChars = setOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                            'a', 'b', 'c', 'd', 'e', 'f')
                        chipIdValue = newValue.lowercase().filter { it in validChars }
                    },
                    titleText = stringResource(Res.string.operate_optimizer),
                    isSelected = chipIdValue.isNotEmpty(),
                    placeholderCom = {
                        PlaceholderText(Res.string.please_select)
                    },
                    modifier = Modifier.padding(top = 5.dp),
                    textFieldHeight = 48,
                    cornerRadius = 30,
                    titleBottom = 10,
                    textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                    isReadOnly = false,
                    keyboardOptions = KeyboardOptions(
                        capitalization = KeyboardCapitalization.None,
                        autoCorrectEnabled = false,
                        keyboardType = KeyboardType.Text
                    ),
                    trailingIconCom = {
                        Image(
                            painter = painterResource(Res.drawable.scan),
                            modifier = Modifier.size(18.dp).clickable {
                                // 跳转到扫描页面
                                navHostController.navigate(Route.TEST_SCAN)
                            },
                            contentDescription = "Scan SN"
                        )
                    }
                )

                Spacer(modifier = Modifier.height(20.dp))

                // 第一行按钮
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 5.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    // 打开优化器按钮
                    Button(
                        colors = ButtonDefaults.buttonColors(
                            containerColor = ButtonPrimaryColor,
                            contentColor = ButtonPrimaryColor
                        ),
                        onClick = {
                            if (chipIdValue.isEmpty()) {
                                val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                val newInfo = "$timestamp 错误: 请选择优化器"
                                operateInfo = if (operateInfo.isNotEmpty()) {
                                    "$operateInfo\n\n$newInfo"
                                } else {
                                    newInfo
                                }
                                return@Button
                            }
                            
                            showLoadingDialog = true
                            executeOpenOptimizer(chipIdValue) { status ->
                                showLoadingDialog = false
                                val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                val newInfo = "$timestamp \n$status"
                                operateInfo = if (operateInfo.isNotEmpty()) {
                                    "$operateInfo\n$newInfo"
                                } else {
                                    newInfo
                                }
                            }
                        },
                        modifier = Modifier.weight(1f).padding(end = 4.dp)
                    ) {
                        Text(text=openOptimizer, color = Color.White)
                    }

                    // 查询电气信息按钮
                    Button(
                        colors = ButtonDefaults.buttonColors(
                            containerColor = ButtonPrimaryColor,
                            contentColor = ButtonPrimaryColor
                        ),
                        onClick = {
                            if (chipIdValue.isEmpty()) {
                                val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                val newInfo = "$timestamp 错误: 请选择优化器"
                                operateInfo = if (operateInfo.isNotEmpty()) {
                                    "$operateInfo\n\n$newInfo"
                                } else {
                                    newInfo
                                }
                                return@Button
                            }
                            
                            showLoadingDialog = true
                            executeQueryElectricalInfo(chipIdValue) { status ->
                                showLoadingDialog = false
                                val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                val newInfo = "$timestamp \n$status"
                                operateInfo = if (operateInfo.isNotEmpty()) {
                                    "$operateInfo\n$newInfo"
                                } else {
                                    newInfo
                                }
                            }
                        },
                        modifier = Modifier.weight(1f).padding(start = 4.dp)
                    ) {
                        Text(text=queryElectricalInfo,color = Color.White)
                    }
                }

                // 第二行按钮
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    // 关闭优化器按钮
                    Button(
                        colors = ButtonDefaults.buttonColors(
                            containerColor = ButtonPrimaryColor,
                            contentColor = ButtonPrimaryColor
                        ),
                        onClick = {
                            if (chipIdValue.isEmpty()) {
                                val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                val newInfo = "$timestamp 错误: 请选择优化器"
                                operateInfo = if (operateInfo.isNotEmpty()) {
                                    "$operateInfo\n\n$newInfo"
                                } else {
                                    newInfo
                                }
                                return@Button
                            }
                            
                            showLoadingDialog = true
                            executeCloseOptimizer(chipIdValue) { status ->
                                showLoadingDialog = false
                                val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                val newInfo = "$timestamp \n$status"
                                operateInfo = if (operateInfo.isNotEmpty()) {
                                    "$operateInfo\n$newInfo"
                                } else {
                                    newInfo
                                }
                            }
                        },
                        modifier = Modifier.weight(1f).padding(end = 4.dp)
                    ) {
                        Text(text=closeOptimizer,color = Color.White)
                    }

                    // 发送日志按钮
                    Button(
                        colors = ButtonDefaults.buttonColors(
                            containerColor = ButtonPrimaryColor,
                            contentColor = ButtonPrimaryColor
                        ),
                        onClick = {
                            showLoadingDialog = true
                            executeSendLog(operateInfo) { status ->
                                showLoadingDialog = false
                                operateInfo = if (operateInfo.isNotEmpty()) {
                                    "$operateInfo\n$status"
                                } else {
                                    status
                                }
                            }
                        },
                        modifier = Modifier.weight(1f).padding(start = 4.dp)
                    ) {
                        Text(text=stringResource(Res.string.send_log),color = Color.White)
                    }


                }
            }
        }
    }
}

// 0x06 打开优化器
// 0x07 关闭优化器
// 0x08 查询优化器电气信息

// 打开优化器 0x06
fun executeOpenOptimizer(optimizerId: String,  callback: (String) -> Unit) {

    val byteArray = ByteArray(6)
    // cmd
    byteArray[0] = 0x41.toByte()

    // optimizer_id
    val optimizerIdBytes = KtorSocketUtils.convertChipId(optimizerId)
    if (optimizerIdBytes != null) {
        optimizerIdBytes.copyInto(destination = byteArray, destinationOffset = 1)
    } else {
        callback("优化器ID格式错误")
        return
    }
    
    KtorSocketUtils.sendSocketCommand( byteArray) { result ->
        when (result) {
            is KtorSocketUtils.SocketResult.Success -> callback("打开优化器：\n${result.response}")
            is KtorSocketUtils.SocketResult.Error -> {
                CoroutineScope(Dispatchers.Main).launch {
                    callback("打开优化器失败：${result.message}")
                }
            }
        }
    }
}

// 关闭优化器 0x07
fun executeCloseOptimizer(optimizerId: String,  callback: (String) -> Unit) {

    val byteArray = ByteArray(6)
    // cmd
    byteArray[0] = 0x42.toByte()

    // optimizer_id
    val optimizerIdBytes = KtorSocketUtils.convertChipId(optimizerId)
    if (optimizerIdBytes != null) {
        optimizerIdBytes.copyInto(destination = byteArray, destinationOffset = 1)
    } else {
        callback("优化器ID格式错误")
        return
    }
    
    KtorSocketUtils.sendSocketCommand( byteArray) { result ->
        when (result) {
            is KtorSocketUtils.SocketResult.Success -> callback("关闭优化器：\n${result.response}")
            is KtorSocketUtils.SocketResult.Error -> {
                CoroutineScope(Dispatchers.Main).launch {
                    callback("关闭优化器失败：${result.message}")
                }
            }
        }
    }
}

// 查询优化器电气信息 0x08
fun executeQueryElectricalInfo(optimizerId: String,  callback: (String) -> Unit) {

    val byteArray = ByteArray(6)
    // cmd
    byteArray[0] = 0x40.toByte()

    // optimizer_id
    val optimizerIdBytes = KtorSocketUtils.convertChipId(optimizerId)
    if (optimizerIdBytes != null) {
        optimizerIdBytes.copyInto(destination = byteArray, destinationOffset = 1)
    } else {
        callback("优化器ID格式错误")
        return
    }
    
    KtorSocketUtils.sendSocketCommand( byteArray) { result ->
        when (result) {
            is KtorSocketUtils.SocketResult.Success -> callback("查询优化器电气信息：\n${result.response}")
            is KtorSocketUtils.SocketResult.Error -> {
                CoroutineScope(Dispatchers.Main).launch {
                    callback("查询优化器电气信息失败：${result.message}")
                }
            }
        }
    }
}

// 发送日志
fun executeSendLog(operateInfo: String, callback: (String) -> Unit) {
    if (operateInfo.isEmpty()) {
        val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
        val newInfo = "$timestamp 错误: 没有可发送的日志内容"
        callback(newInfo)
        return
    }

    // 添加发送中的提示
    val currentTime = DateTimeUtil.getFormattedTimestampWithBrackets()
    val startInfo = "$currentTime 正在准备文件..."
    callback(startInfo)

    // 创建临时文件
    val timestamp = DateTimeUtil.formatForFileName()
    val fileName = "optimizer_test_log_$timestamp.txt"
    
    // 使用协程处理文件保存和分享
    CoroutineScope(Dispatchers.Main).launch {
        try {
            // 保存文件
            FileUtil.saveTextToTempFile(operateInfo, fileName).collect { result ->
                result.onSuccess { filePath ->
                    // 文件保存成功，尝试分享文件
                    FileUtil.shareFile(filePath, "text/plain").collect { shareResult ->
                        shareResult.onSuccess { 
                            val successTime = DateTimeUtil.getFormattedTimestampWithBrackets()
                            val successInfo = "$successTime 日志已发送"
                            callback(successInfo)
                        }
                        shareResult.onFailure { error ->
                            val errorTime = DateTimeUtil.getFormattedTimestampWithBrackets()
                            val errorInfo = "$errorTime 发送日志失败：${error.message}"
                            callback(errorInfo)
                        }
                    }
                }
                result.onFailure { error ->
                    val errorTime = DateTimeUtil.getFormattedTimestampWithBrackets()
                    val errorInfo = "$errorTime 保存日志失败：${error.message}"
                    callback(errorInfo)
                }
            }
        } catch (e: Exception) {
            val errorTime = DateTimeUtil.getFormattedTimestampWithBrackets()
            val errorInfo = "$errorTime 处理日志失败：${e.message}"
            callback(errorInfo)
        }
    }
}