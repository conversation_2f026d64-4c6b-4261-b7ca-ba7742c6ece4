package com.ymx.photovoltaic.ui.page.my

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.PlaceholderText
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.ui.page.widget.rememberSingleColumnPickerState
import com.ymx.photovoltaic.viewmodel.MyViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.contact_info
import photovoltaic_kmp_app.composeapp.generated.resources.error_input_contact
import photovoltaic_kmp_app.composeapp.generated.resources.error_input_issue
import photovoltaic_kmp_app.composeapp.generated.resources.error_select_device
import photovoltaic_kmp_app.composeapp.generated.resources.error_select_station
import photovoltaic_kmp_app.composeapp.generated.resources.feedback
import photovoltaic_kmp_app.composeapp.generated.resources.feedback_hint
import photovoltaic_kmp_app.composeapp.generated.resources.issue_description
import photovoltaic_kmp_app.composeapp.generated.resources.please_input_contact
import photovoltaic_kmp_app.composeapp.generated.resources.submit

@Composable
fun FeedbackPage(
    navHostController: NavHostController,
    myViewModel: MyViewModel = getKoin().get()
) {

    SetStatusBar(Color.White,true)

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.feedback), backClick = { navHostController.popBackStack() })
        },
        containerColor = Grey_F5
    ) { padding ->
        FeedbackScreen(padding, myViewModel, navHostController)
    }
}

@Composable
fun FeedbackScreen(
    padding: PaddingValues,
    myViewModel: MyViewModel,
    navHostController: NavHostController
) {
    // State variables
    var stationValue by rememberSaveable { mutableStateOf("") }
    var deviceValue by rememberSaveable { mutableStateOf("") }
    var feedbackText by rememberSaveable { mutableStateOf("") }
    var contactText by rememberSaveable { mutableStateOf("") }
    
    // Error states
    var stationError by remember { mutableStateOf<String?>(null) }
    var deviceError by remember { mutableStateOf<String?>(null) }
    var feedbackError by remember { mutableStateOf<String?>(null) }
    var contactError by remember { mutableStateOf<String?>(null) }

    // 获取错误提示文本
    val errorSelectStation = stringResource(Res.string.error_select_station)
    val errorSelectDevice = stringResource(Res.string.error_select_device)
    val errorInputIssue = stringResource(Res.string.error_input_issue)
    val errorInputContact = stringResource(Res.string.error_input_contact)

    // Pickers
    val stationPicker = rememberSingleColumnPickerState()
    val devicePicker = rememberSingleColumnPickerState()

    var showDialog by remember { mutableStateOf(false) }
    if (showDialog) {
        ResultDialog(true) { showDialog = false }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF7F7F7))
            .padding(padding)
            .padding(start = 10.dp, end = 10.dp, top = 20.dp, bottom = 30.dp),
    ) {
        // 选择电站
//        CommonTitleField(
//            value = stationValue,
//            onValueChange = { },
//            titleText = stringResource(Res.string.select_station),
//            isSelected = stationValue.isNotEmpty(),
//            placeholderCom = {
//                PlaceholderText(Res.string.please_select)
//            },
//            modifier = Modifier.padding(top = 5.dp),
//            textFieldHeight = 48,
//            cornerRadius = 30,
//            titleBottom = 10,
//            textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
//            isReadOnly = true,
//            isError = stationError != null,
//            errorText = stationError ?: "",
//            onBoxClick = {
//                // TODO: 实现电站选择逻辑
//            },
//            trailingIconCom = {
//                androidx.compose.foundation.Image(
//                    painter = painterResource(Res.drawable.right_arrow),
//                    modifier = Modifier.size(18.dp),
//                    contentDescription = "Select Station"
//                )
//            }
//        )

//        Spacer(modifier = Modifier.height(12.dp))

        // 选择设备
//        CommonTitleField(
//            value = deviceValue,
//            onValueChange = { },
//            titleText = stringResource(Res.string.select_device),
//            isSelected = deviceValue.isNotEmpty(),
//            placeholderCom = {
//                PlaceholderText(Res.string.please_select)
//            },
//            modifier = Modifier.padding(top = 5.dp),
//            textFieldHeight = 48,
//            cornerRadius = 30,
//            titleBottom = 10,
//            textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
//            isReadOnly = true,
//            isError = deviceError != null,
//            errorText = deviceError ?: "",
//            onBoxClick = {
//                // TODO: 实现设备选择逻辑
//            },
//            trailingIconCom = {
//                androidx.compose.foundation.Image(
//                    painter = painterResource(Res.drawable.right_arrow),
//                    modifier = Modifier.size(18.dp),
//                    contentDescription = "Select Device"
//                )
//            }
//        )

        Spacer(modifier = Modifier.height(12.dp))

        // 问题描述
        CommonTitleField(
            value = feedbackText,
            onValueChange = { 
                if (it.length <= 200) {
                    feedbackText = it
                    feedbackError = null
                }
            },
            titleText = stringResource(Res.string.issue_description),
            placeholderCom = {
                PlaceholderText(Res.string.feedback_hint)
            },
            modifier = Modifier.padding(top = 5.dp),
            textFieldHeight = 200,
            cornerRadius = 30,
            titleBottom = 10,
            textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
            isError = feedbackError != null,
            errorText = feedbackError ?: "",
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Next,
                keyboardType = KeyboardType.Text
            ),
            isSingleLine = false
        )

        Spacer(modifier = Modifier.height(12.dp))

        // 联系方式
        CommonTitleField(
            value = contactText,
            onValueChange = { 
                contactText = it
                contactError = null
            },
            titleText = stringResource(Res.string.contact_info),
            placeholderCom = {
                PlaceholderText(Res.string.please_input_contact)
            },
            modifier = Modifier.padding(top = 5.dp),
            textFieldHeight = 48,
            cornerRadius = 30,
            titleBottom = 10,
            textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
            isError = contactError != null,
            errorText = contactError ?: "",
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Done,
                keyboardType = KeyboardType.Text
            )
        )

        Spacer(modifier = Modifier.weight(1f))

        val coroutineScope = rememberCoroutineScope()
        ConfirmButton(stringResource(Res.string.submit), true) {
            var hasError = false

            // Validate station
//            if (stationValue.isEmpty()) {
//                stationError = errorSelectStation
//                hasError = true
//            }
//
//            // Validate device
//            if (deviceValue.isEmpty()) {
//                deviceError = errorSelectDevice
//                hasError = true
//            }

            // Validate feedback text
            if (feedbackText.trim().isEmpty()) {
                feedbackError = errorInputIssue
                hasError = true
            }

            // Validate contact
            if (contactText.trim().isEmpty()) {
                contactError = errorInputContact
                hasError = true
            }

            if (hasError) {
                return@ConfirmButton
            }

            myViewModel.saveFeedback(feedbackText, contactText) {
                showDialog = true
                coroutineScope.launch {
                    delay(2000)
                    showDialog = false
                    navHostController.popBackStack()
                }
            }
        }
    }
}
