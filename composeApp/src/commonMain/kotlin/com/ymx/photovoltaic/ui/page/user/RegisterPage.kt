package com.ymx.photovoltaic.ui.page.user

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.config.AppConfig
import com.ymx.photovoltaic.data.local.CacheManager
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.platform.ToastUtil
import com.ymx.photovoltaic.ui.page.theme.BlueColor
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.theme.LjRed
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.LoadingDialog
import com.ymx.photovoltaic.ui.page.widget.PlaceholderText
import com.ymx.photovoltaic.ui.page.widget.RegionDataManager
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.ui.page.widget.rememberRegionPickerState
import com.ymx.photovoltaic.ui.page.widget.rememberSingleColumnPickerState
import com.ymx.photovoltaic.util.encodeUrl
import com.ymx.photovoltaic.viewmodel.UserViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.account_register
import photovoltaic_kmp_app.composeapp.generated.resources.and
import photovoltaic_kmp_app.composeapp.generated.resources.company_name
import photovoltaic_kmp_app.composeapp.generated.resources.company_name_hint
import photovoltaic_kmp_app.composeapp.generated.resources.country_and_region
import photovoltaic_kmp_app.composeapp.generated.resources.have_account
import photovoltaic_kmp_app.composeapp.generated.resources.i_have_read_and_agree
import photovoltaic_kmp_app.composeapp.generated.resources.input_verification_code
import photovoltaic_kmp_app.composeapp.generated.resources.password_settings
import photovoltaic_kmp_app.composeapp.generated.resources.please_enter
import photovoltaic_kmp_app.composeapp.generated.resources.please_input_email_phone
import photovoltaic_kmp_app.composeapp.generated.resources.please_input_password_again
import photovoltaic_kmp_app.composeapp.generated.resources.please_input_password_hint
import photovoltaic_kmp_app.composeapp.generated.resources.please_select
import photovoltaic_kmp_app.composeapp.generated.resources.privacy_agreement
import photovoltaic_kmp_app.composeapp.generated.resources.privacy_policy
import photovoltaic_kmp_app.composeapp.generated.resources.register_account
import photovoltaic_kmp_app.composeapp.generated.resources.register_now
import photovoltaic_kmp_app.composeapp.generated.resources.right_arrow
import photovoltaic_kmp_app.composeapp.generated.resources.selected_region
import photovoltaic_kmp_app.composeapp.generated.resources.send_verification_code_btn
import photovoltaic_kmp_app.composeapp.generated.resources.service_agreement
import photovoltaic_kmp_app.composeapp.generated.resources.to_login
import photovoltaic_kmp_app.composeapp.generated.resources.type_2_4g
import photovoltaic_kmp_app.composeapp.generated.resources.type_plc
import photovoltaic_kmp_app.composeapp.generated.resources.user_agreement
import photovoltaic_kmp_app.composeapp.generated.resources.user_name

@Composable
fun RegisterPage(
    navHostController: NavHostController,
    userViewModel: UserViewModel =  getKoin().get(),
    accountType: String? = null
) {

    SetStatusBar(Color.White,true)


    var verificationCode by rememberSaveable { mutableStateOf("") }
    var countdown by remember { mutableIntStateOf(0) }
    var showLoadingDialog by remember { mutableStateOf(false) }

    val regionPicker = rememberRegionPickerState()

    val pleaseSelect = stringResource(Res.string.please_select)
    var regionValue by remember { mutableStateOf(pleaseSelect) }
    var userNameValue by remember { mutableStateOf("") }
    var companyNameValue by remember { mutableStateOf("") }
    var phoneOrEmail by remember { mutableStateOf("") }
    
    var password by remember { mutableStateOf("") }
    var passwordVisibility by remember { mutableStateOf(false) }

    var passwordAgain by remember { mutableStateOf("") }
    var passwordVisibilityAgain by remember { mutableStateOf(false) }

    var showError by remember { mutableStateOf(false) }
    
    var agreementChecked by remember { mutableStateOf(false) }
    
    // 错误提示状态
    var regionError by remember { mutableStateOf(false) }
    var userNameError by remember { mutableStateOf(false) }
    var companyNameError by remember { mutableStateOf(false) }
    var phoneOrEmailError by remember { mutableStateOf(false) }
    var verificationCodeError by remember { mutableStateOf(false) }
    var passwordError by remember { mutableStateOf(false) }
    var passwordAgainError by remember { mutableStateOf(false) }
    var agreementError by remember { mutableStateOf(false) }

    if (showLoadingDialog) {
        LoadingDialog(loadingText = "注册中...") { showLoadingDialog = false }
    }

    var showSuccessDialog by remember { mutableStateOf(false) }

    if (showSuccessDialog) {
        ResultDialog(true) { showSuccessDialog = false }
    }

    var showFailDialog by remember { mutableStateOf(false) }

    if (showFailDialog) {
        ResultDialog(false) { showFailDialog = false }
    }
    
    val coroutineScope = rememberCoroutineScope()

    var regionValues by remember { mutableStateOf(arrayOf(0, 0, 0)) }

    // 获取各级地区列表
    val level2Regions = RegionDataManager.getRegionsByLevel(2)
    val level3Regions = remember(regionValues[0]) {
        if (regionValues[0] >= 0 && regionValues[0] < level2Regions.size) {
            RegionDataManager.getChildren(level2Regions[regionValues[0]].id)
        } else emptyList()
    }
    val level4Regions = remember(regionValues[1]) {
        if (regionValues[1] >= 0 && regionValues[1] < level3Regions.size) {
            RegionDataManager.getChildren(level3Regions[regionValues[1]].id)
        } else emptyList()
    }

    val typePicker = rememberSingleColumnPickerState()

    val twoG=stringResource(Res.string.type_2_4g)
    val plcT=stringResource(Res.string.type_plc)
    // 电站类型列表
    val typeRange = remember {
        listOf(
            twoG,
            plcT
        )
    }
    // 选中的类型索引
    var typeValue by remember { mutableStateOf(0) }

    LaunchedEffect(countdown) {
        if (countdown > 0) {
            delay(1000)
            countdown--
        }
    }

    val selectedRegion= stringResource(Res.string.selected_region)
    val countryAndRegion= stringResource(Res.string.country_and_region)


    fun doRegister() {
        // 重置错误状态
        regionError = false
        userNameError = false
        companyNameError = false
        phoneOrEmailError = false
        verificationCodeError = false
        passwordError = false
        passwordAgainError = false
        agreementError = false
        showError = false
        
        // 检查字段是否为空
        var hasError = false
        
        if (regionValue == pleaseSelect) {
            regionError = true
            hasError = true
        }
        
        if (userNameValue.trim().isEmpty()) {
            userNameError = true
            hasError = true
        }
        
        // 仅当accountType不是owner时才验证公司名称
        if (companyNameValue.trim().isEmpty() && accountType != "owner") {
            companyNameError = true
            hasError = true
        }
        
        if (phoneOrEmail.trim().isEmpty()) {
            phoneOrEmailError = true
            hasError = true
        }
        
        if (verificationCode.trim().isEmpty()) {
            verificationCodeError = true
            hasError = true
        }
        
        if (password.trim().isEmpty()) {
            passwordError = true
            hasError = true
        }
        
        if (passwordAgain.trim().isEmpty()) {
            passwordAgainError = true
            hasError = true
        }
        
        // 检查密码是否一致
        if (password != passwordAgain && !passwordError && !passwordAgainError) {
            showError = true
            hasError = true
        }
        
        if (!agreementChecked) {
            agreementError = true
            hasError = true
        }
        
        if (hasError) {
            return
        }

        // 执行注册操作
        showLoadingDialog = true
        userViewModel.register(
            phone = phoneOrEmail,
            pwd = password,
            loginCode = null,
            smsCode = verificationCode,
            errorBlock = { 
                showLoadingDialog = false
                showFailDialog = true
                // 使用协程延迟2秒关闭对话框
                coroutineScope.launch {
                    delay(2000)
                    showFailDialog = false
                }
            }
        ) {
            // 登录成功后的操作
            showLoadingDialog = false
            showSuccessDialog = true
            // 使用协程延迟2秒关闭对话框
            coroutineScope.launch {
                delay(2000)
                showSuccessDialog = false
                navHostController.navigate(Route.LOGIN) {
                    popUpTo(Route.REGISTER) { inclusive = true }
                }
            }
        }
    }

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxSize()
            .background(color = Grey_F5)
            .verticalScroll(rememberScrollState())
    ) {

        TopBar(
            textStr = stringResource(Res.string.register_account),
            backClick = { navHostController.popBackStack() }
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {

            // 国家及地区
            CommonTitleField(
                value = regionValue,
                onValueChange = {
                },
                titleText = stringResource(Res.string.country_and_region),
                placeholderCom = { PlaceholderText(Res.string.please_enter) },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                isReadOnly = true,
                isSelected = regionValue != pleaseSelect,
                isError = regionError,
                errorText = if (regionError) stringResource(Res.string.please_select) else "",
                onBoxClick = {
                    regionPicker.show(
                        title = countryAndRegion,
                        values = regionValues
                    ) { selected ->
                        regionValues = selected
                        
                        // 获取选中的地区对象
                        val region1 = level2Regions.getOrNull(selected[0])
                        val region2 = level3Regions.getOrNull(selected[1])
                        val region3 = level4Regions.getOrNull(selected[2])
                        
                        regionValue = "${region1?.name ?: ""} ${region2?.name ?: ""} ${region3?.name ?: ""}"
                        regionError = false
                    }
                },
                trailingIconCom = {
                    Image(
                        painter = painterResource(Res.drawable.right_arrow),
                        modifier = Modifier.size(18.dp),
                        contentDescription = "Select Region"
                    )
                }
            )
            // 用户名
            CommonTitleField(
                value = userNameValue,
                onValueChange = { 
                    userNameValue = it 
                    userNameError = false
                },
                titleText = stringResource(Res.string.user_name),
                placeholderCom = { PlaceholderText(Res.string.please_enter) },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                isError = userNameError,
                errorText = if (userNameError) stringResource(Res.string.please_enter) else ""
            )

            // 公司名称
            CommonTitleField(
                value = companyNameValue,
                onValueChange = { 
                    companyNameValue = it 
                    companyNameError = false
                },
                titleText = stringResource(Res.string.company_name),
                placeholderCom = { PlaceholderText(Res.string.company_name_hint) },
                textFieldHeight = 48,
                modifier = Modifier.padding(top = 5.dp),
                titleBottom = 10, // Match the original Box padding
                cornerRadius = 30,
                isError = companyNameError,
                errorText = if (companyNameError) stringResource(Res.string.please_enter) else ""
            )

            // 发送验证码
            CommonTitleField(
                titleText = stringResource(Res.string.account_register),
                value = phoneOrEmail,
                onValueChange = { 
                    phoneOrEmail = it 
                    phoneOrEmailError = false
                },
                placeholderCom = { PlaceholderText(Res.string.please_input_email_phone) },
                isPassword = false,
                modifier = Modifier.fillMaxWidth(),
                textFieldHeight = 48,
                isError = phoneOrEmailError,
                errorText = if (phoneOrEmailError) stringResource(Res.string.please_input_email_phone) else "",
                trailingIconCom = {
                    OutlinedButton(
                        onClick = {
                            if (phoneOrEmail.isNotEmpty()) {
                                countdown = 60
                                userViewModel.sendSms(
                                    account = phoneOrEmail,
                                    type = "register",
                                    errorBlock = { errorMsg ->
                                        val message = errorMsg.ifEmpty { "获取验证码失败" }
                                        ToastUtil.showShort(message)
                                        countdown = 0
                                    }
                                ) {
                                    ToastUtil.showShort("验证码发送成功")
                                }
                            } else {
                                // 如果账号为空，设置错误状态
                                phoneOrEmailError = true
                            }
                        },
                        modifier = Modifier.padding(end = 2.dp),
                        shape = RoundedCornerShape(30.dp),
                        enabled = countdown == 0,
                        border = BorderStroke(1.dp, LjRed),
                    ) {
                        Text(
                            text = if (countdown == 0) stringResource(Res.string.send_verification_code_btn) else "${countdown}s",
                            color = LjRed
                        )
                    }
                }
            )

            // 输入验证码
            CommonTitleField(
                value = verificationCode,
                onValueChange = { 
                    verificationCode = it 
                    verificationCodeError = false
                },
                placeholderCom = { PlaceholderText(Res.string.input_verification_code) },
                modifier = Modifier.padding(top = 10.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 0, // No title
                isError = verificationCodeError,
                errorText = if (verificationCodeError) stringResource(Res.string.input_verification_code) else ""
            )
            // 密码设置
            CommonTitleField(
                value = password,
                onValueChange = { 
                    password = it
                    passwordError = false
                    showError = password != passwordAgain && passwordAgain.isNotEmpty()
                },
                titleText = stringResource(Res.string.password_settings),
                placeholderCom = { PlaceholderText(Res.string.please_input_password_hint) },
                textFieldHeight = 48,
                isPassword = true,
                passwordVisibility = passwordVisibility,
                onPasswordVisibilityChange = { passwordVisibility = !passwordVisibility },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 5.dp),
                 cornerRadius = 30,
                 titleBottom = 10,
                 isError = passwordError,
                 errorText = if (passwordError) stringResource(Res.string.please_input_password_hint) else ""
            )
            // 再次输入密码
            CommonTitleField(
                value = passwordAgain,
                onValueChange = { 
                    passwordAgain = it
                    passwordAgainError = false
                    showError = password != passwordAgain && password.isNotEmpty()
                },
                placeholderCom = { PlaceholderText(Res.string.please_input_password_again) },
                isPassword = true,
                textFieldHeight = 48,
                passwordVisibility = passwordVisibilityAgain,
                onPasswordVisibilityChange = { passwordVisibilityAgain = !passwordVisibilityAgain },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 10.dp),
                cornerRadius = 30,
                titleBottom = 0, // No title for the second password field
                isError = passwordAgainError || showError, // Add error state to the second field
                errorText = if (passwordAgainError) stringResource(Res.string.please_input_password_again) 
                           else if (showError) "密码不一致，请重新输入" 
                           else ""
            )

            val language = CacheManager.getLanguage()
            val userAgreement = if (language == "zh") {
                stringResource(Res.string.user_agreement).replace("%s", "")  // 中文不添加空格
            } else {
                stringResource(Res.string.user_agreement).replace("%s", " ")  // 其他语言添加空格
            }

            // 根据当前语言获取对应的协议URL
            val userAgreementUrl = AppConfig.getUserAgreementUrl()

            val privacyPolicy = if (language == "zh") {
                stringResource(Res.string.privacy_policy).replace("%s", "")  // 中文不添加空格
            } else {
                stringResource(Res.string.privacy_policy).replace("%s", " ")  // 其他语言添加空格
            }

            // 根据当前语言获取对应的隐私政策URL
            val privacyPolicyUrl = AppConfig.getPrivacyPolicyUrl()

            AgreementRow(
                agreementChecked = agreementChecked,
                onAgreementCheckedChange = { agreementChecked = it },
                agreementError = agreementError,
                onServiceAgreementClick = {
                    val encodedUrl = encodeUrl(userAgreementUrl)
                    val encodedTitle = encodeUrl(userAgreement)
                    navHostController.navigate("web_view?title=$encodedTitle&url=$encodedUrl") },
                onPrivacyAgreementClick = {
                    val encodedUrl = encodeUrl(privacyPolicyUrl)
                    val encodedTitle = encodeUrl(privacyPolicy)
                    navHostController.navigate("web_view?title=$encodedTitle&url=$encodedUrl")
                }
            )

            Button(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 20.dp, bottom = 5.dp)
                    .height(50.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = LjRed
                ),
                shape = RoundedCornerShape(30.dp),
                onClick = { doRegister() }
            ) {
                Text(
                    text = stringResource(Res.string.register_now),
                    color = Color.White,
                    modifier = Modifier.padding(vertical = 4.dp),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 30.dp),
                horizontalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(Res.string.have_account),
                    color = Color.Gray,
                    fontSize = 14.sp
                )
                Text(
                    text = stringResource(Res.string.to_login),
                    modifier = Modifier.clickable {
                        navHostController.navigate(Route.LOGIN) {
                            popUpTo(Route.REGISTER) { inclusive = true }
                        }
                    }.padding(start = 4.dp),
                    color = LjRed,
                    fontSize = 14.sp
                )
            }
        }
    }
}

// 协议同意
/**
 * 协议同意行组件
 *
 * @param agreementChecked 是否同意协议
 * @param onAgreementCheckedChange 协议选中状态变化回调
 * @param agreementError 是否显示错误状态
 * @param onServiceAgreementClick 服务协议点击回调
 * @param onPrivacyAgreementClick 隐私协议点击回调
 */

@Composable
fun AgreementRow(
    agreementChecked: Boolean,
    onAgreementCheckedChange: (Boolean) -> Unit,
    agreementError: Boolean = false,
    onServiceAgreementClick: () -> Unit,
    onPrivacyAgreementClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier.padding(top = 16.dp)
        ) {
            MiniRadioButton(
                selected = agreementChecked,
                onClick = {
                    onAgreementCheckedChange(!agreementChecked)
                },
                selectedColor = Color.Gray,
                unselectedColor = Color.Gray
            )
            Text(
                text = stringResource(Res.string.i_have_read_and_agree),
                fontSize = 13.sp,
                color = if (agreementChecked) Color.Black else Color.Gray,
                modifier = Modifier.padding(start = 4.dp)
            )
            Text(
                text = stringResource(Res.string.service_agreement),
                color = BlueColor,
                textDecoration = TextDecoration.Underline,
                fontSize = 13.sp,
                modifier = Modifier.clickable { onServiceAgreementClick() }.padding(horizontal = 2.dp)
            )
            Text(
                text = stringResource(Res.string.and).replace("%s", ""),
                fontSize = 13.sp,
                color = if (agreementChecked) Color.Black else Color.Gray,
            )
            Text(
                text = stringResource(Res.string.privacy_agreement),
                color = BlueColor,
                textDecoration = TextDecoration.Underline,
                fontSize = 13.sp,
                modifier = Modifier.clickable { onPrivacyAgreementClick() }.padding(start = 2.dp)
            )
        }

        if (agreementError) {
            Text(
                text = "请阅读并同意协议",
                color = Color.Red,
                fontSize = 12.sp,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
}




