package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.PlaceholderText
import com.ymx.photovoltaic.ui.page.widget.RegionDataManager
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TimeType
import com.ymx.photovoltaic.ui.page.widget.TitleItem
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.ui.page.widget.rememberCustomDatePickerState
import com.ymx.photovoltaic.ui.page.widget.rememberCustomTimePickerState
import com.ymx.photovoltaic.ui.page.widget.rememberRegionPickerState
import com.ymx.photovoltaic.ui.page.widget.rememberSingleColumnPickerState
import com.ymx.photovoltaic.util.DateTimeUtil
import com.ymx.photovoltaic.viewmodel.EquipmentViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.add_station
import photovoltaic_kmp_app.composeapp.generated.resources.collect_time
import photovoltaic_kmp_app.composeapp.generated.resources.contact_way
import photovoltaic_kmp_app.composeapp.generated.resources.error_empty_field
import photovoltaic_kmp_app.composeapp.generated.resources.error_invalid_power
import photovoltaic_kmp_app.composeapp.generated.resources.error_station_time
import photovoltaic_kmp_app.composeapp.generated.resources.input_address_details
import photovoltaic_kmp_app.composeapp.generated.resources.input_power_sample
import photovoltaic_kmp_app.composeapp.generated.resources.location_address
import photovoltaic_kmp_app.composeapp.generated.resources.please_enter
import photovoltaic_kmp_app.composeapp.generated.resources.please_select
import photovoltaic_kmp_app.composeapp.generated.resources.power
import photovoltaic_kmp_app.composeapp.generated.resources.power_price_config
import photovoltaic_kmp_app.composeapp.generated.resources.province_city_district_placeholder
import photovoltaic_kmp_app.composeapp.generated.resources.region
import photovoltaic_kmp_app.composeapp.generated.resources.right_arrow
import photovoltaic_kmp_app.composeapp.generated.resources.save
import photovoltaic_kmp_app.composeapp.generated.resources.station_name
import photovoltaic_kmp_app.composeapp.generated.resources.sunrise_placeholder_format
import photovoltaic_kmp_app.composeapp.generated.resources.sunset_placeholder_format
import photovoltaic_kmp_app.composeapp.generated.resources.type_2_4g
import photovoltaic_kmp_app.composeapp.generated.resources.type_plc
import photovoltaic_kmp_app.composeapp.generated.resources.unit_kw


@Composable
fun StationOnePage(
    navHostController: NavHostController,
    equipmentViewModel: EquipmentViewModel = getKoin().get()
) {
    val currentLocalTime = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).time
    // 添加开始日期，使用系统创建时间
    val startDate = DateTimeUtil.fromEpochMillis(AppGlobal.powCreateTime)
    val nowDay = DateTimeUtil.now()

    val startTimePicker = rememberCustomTimePickerState()
    val endTimePicker = rememberCustomTimePickerState()

    // 存储各种字段的值
    var systemNameValue by remember { mutableStateOf("") }
    var regionValue by remember { mutableStateOf("") }
    var streetNameValue by remember { mutableStateOf("") }
    var powerValue by remember { mutableStateOf("") }
    var sunUpTimeValue by remember { mutableStateOf("") }
    var sunDownTimeValue by remember { mutableStateOf("") }
    var communicationMethodValue by remember { mutableStateOf("") }
    var powerPriceValue by remember { mutableStateOf("") }
    var onlineTimeValue by remember { mutableStateOf("") }
    var onlineTypeValue by remember { mutableStateOf("") }
    
    // 错误状态
    var systemNameError by remember { mutableStateOf<String?>(null) }
    var regionError by remember { mutableStateOf<String?>(null) }
    var streetNameError by remember { mutableStateOf<String?>(null) }
    var powerError by remember { mutableStateOf<String?>(null) }
    var typeError by remember { mutableStateOf<String?>(null) }
    var sunUpTimeError by remember { mutableStateOf<String?>(null) }
    var sunDownTimeError by remember { mutableStateOf<String?>(null) }
    var communicationMethodError by remember { mutableStateOf<String?>(null) }
    var powerPriceError by remember { mutableStateOf<String?>(null) }
    var onlineTimeError by remember { mutableStateOf<String?>(null) }
    var onlineTypeError by remember { mutableStateOf<String?>(null) }

    val regionPicker = rememberRegionPickerState()
    var regionValues by remember { mutableStateOf(arrayOf(0, 0, 0)) }

    // 获取各级地区列表
    val level2Regions = RegionDataManager.getRegionsByLevel(2)
    val level3Regions = remember(regionValues[0]) {
        if (regionValues[0] >= 0 && regionValues[0] < level2Regions.size) {
            RegionDataManager.getChildren(level2Regions[regionValues[0]].id)
        } else emptyList()
    }
    val level4Regions = remember(regionValues[1]) {
        if (regionValues[1] >= 0 && regionValues[1] < level3Regions.size) {
            RegionDataManager.getChildren(level3Regions[regionValues[1]].id)
        } else emptyList()
    }

    // 电站类型选择器状态
    val typePicker = rememberSingleColumnPickerState()
    // 通讯方式选择器状态
    val communicationMethodPicker = rememberSingleColumnPickerState()
    // 电价配置选择器状态
    val powerPricePicker = rememberSingleColumnPickerState()
    // 并网类型选择器状态
    val onlineTypePicker = rememberSingleColumnPickerState()
    // 并网时间选择器状态
    val onlineTimePicker = rememberCustomTimePickerState()

    // 添加日期选择器状态
    val onlineDatePicker = rememberCustomDatePickerState()

    val twoG = stringResource(Res.string.type_2_4g)
    val plcT = stringResource(Res.string.type_plc)
    // 电站类型列表
    val typeRange = remember { listOf(twoG, plcT) }
    // 选中的类型索引
    var typeIndex by remember { mutableStateOf(-1) }
    
    // 通讯方式列表和索引
    val communicationMethodRange = remember { listOf("2.4G", "PLC") }
    var communicationMethodIndex by remember { mutableStateOf(-1) }
    
    // 电价配置列表和索引
    val powerPriceRange = remember { listOf("配置1", "配置2", "配置3") }
    var powerPriceIndex by remember { mutableStateOf(-1) }
    
    // 并网类型列表和索引
    val onlineTypeRange = remember { listOf("类型1", "类型2", "类型3") }
    var onlineTypeIndex by remember { mutableStateOf(-1) }

     SetStatusBar(Color.White,true)

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.add_station), backClick = { navHostController.popBackStack() })
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(start = 10.dp, end = 10.dp, top = 10.dp).verticalScroll(
                    rememberScrollState()
                )
        ) {
            var showSuccessDialog by remember { mutableStateOf(false) }
            if (showSuccessDialog) {
                ResultDialog(true) { showSuccessDialog = false }
            }

            var showFailDialog by remember { mutableStateOf(false) }
            if (showFailDialog) {
                ResultDialog(false) { showFailDialog = false }
            }

            val regionStr = stringResource(Res.string.region)

            // 所在地址选择
            CommonTitleField(
                value = regionValue,
                onValueChange = { },
                titleText = stringResource(Res.string.location_address),
                isSelected = regionValue.isNotEmpty(),
                placeholderCom = {
                    PlaceholderText(Res.string.province_city_district_placeholder)
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                isReadOnly = true,
                isError = regionError != null,
                errorText = regionError ?: "",
                onBoxClick = {
                    regionPicker.show(
                        title = regionStr,
                        values = regionValues
                    ) { selected ->
                        regionValues = selected
                        
                        // 获取选中的地区对象
                        val region1 = RegionDataManager.getRegionsByLevel(2).getOrNull(selected[0])
                        val region2Children = RegionDataManager.getChildren(region1?.id ?: 0)
                        val region2 = if (region2Children.isEmpty()) null else region2Children.getOrNull(selected[1])

                        val region3Children = RegionDataManager.getChildren(region2?.id ?: 0)
                        val region3 = if (region3Children.isEmpty()) null else region3Children.getOrNull(selected[2])

                        regionValue = "${region1?.name ?: ""} ${region2?.name ?: ""} ${region3?.name ?: ""}"
                        regionError = null
                    }
                },
                trailingIconCom = {
                    Image(
                        painter = painterResource(Res.drawable.right_arrow),
                        modifier = Modifier.size(18.dp),
                        contentDescription = "Select Region"
                    )
                }
            )

            // 街道/小区/门牌号详细地址
            CommonTitleField(
                value = streetNameValue,
                onValueChange = { 
                    streetNameValue = it
                    streetNameError = null
                },
                placeholderCom = {
                    PlaceholderText(Res.string.input_address_details)
                },
                modifier = Modifier.padding(top = 10.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                isError = streetNameError != null,
                errorText = streetNameError ?: ""
            )

            // 电站名称输入
            CommonTitleField(
                value = systemNameValue,
                onValueChange = {
                    systemNameValue = it
                    systemNameError = null
                },
                titleText = stringResource(Res.string.station_name),
                placeholderCom = {
                    PlaceholderText(Res.string.please_enter)
                },
                modifier = Modifier.padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                isError = systemNameError != null,
                errorText = systemNameError ?: ""
            )

            // 装机功率和电价配置在同一行
            Row(
                modifier = Modifier.padding(top = 5.dp)
            ) {
                // 装机功率
                CommonTitleField(
                    value = powerValue,
                    onValueChange = { newValue ->
                        // 只保留数字
                        powerValue = newValue.filter { it.isDigit() }
                        powerError = null
                    },
                    titleText = stringResource(Res.string.power) + "(" + stringResource(Res.string.unit_kw) + ")",
                    placeholderCom = {
                        PlaceholderText(Res.string.input_power_sample)
                    },
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = 5.dp),
                    textFieldHeight = 48,
                    cornerRadius = 30,
                    titleBottom = 10,
                    textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
//                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    isError = powerError != null,
                    errorText = powerError ?: ""
                )

                val powerPriceConfigStr= stringResource(Res.string.power_price_config)
                
                // 电价配置
                CommonTitleField(
                    value = powerPriceValue,
                    onValueChange = { },
                    titleText = stringResource(Res.string.power_price_config),
                    isSelected = powerPriceValue.isNotEmpty(),
                    placeholderCom = {
                        PlaceholderText(Res.string.please_select)
                    },
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 5.dp),
                    textFieldHeight = 48,
                    cornerRadius = 30,
                    titleBottom = 10,
                    textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                    isReadOnly = true,
                    isError = powerPriceError != null,
                    errorText = powerPriceError ?: "",
                    onBoxClick = {
                        powerPricePicker.show(
                            title = powerPriceConfigStr,
                            range = powerPriceRange,
                            value = if (powerPriceIndex < 0) 0 else powerPriceIndex
                        ) { index ->
                            powerPriceIndex = index
                            powerPriceValue = powerPriceRange[index]
                            powerPriceError = null
                        }
                    },
                    trailingIconCom = {
                        Image(
                            painter = painterResource(Res.drawable.right_arrow),
                            modifier = Modifier.size(18.dp),
                            contentDescription = "Select Power Price"
                        )
                    }
                )
            }

            // 并网时间和并网类型在同一行
//            Row(
//                modifier = Modifier.padding(top = 5.dp)
//            ) {
//                // 并网时间
//                CommonTitleField(
//                    value = onlineTimeValue,
//                    onValueChange = { },
//                    titleText = stringResource(Res.string.online_time),
//                    isSelected = onlineTimeValue.isNotEmpty(),
//                    placeholderCom = {
//                        PlaceholderText(Res.string.date_format_sample)
//                    },
//                    modifier = Modifier
//                        .weight(1f)
//                        .padding(end = 5.dp),
//                    textFieldHeight = 48,
//                    cornerRadius = 30,
//                    titleBottom = 10,
//                    textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
//                    isReadOnly = true,
//                    isError = onlineTimeError != null,
//                    errorText = onlineTimeError ?: "",
//                    onBoxClick = {
//                        // 使用日期选择器选择并网时间
//                        onlineDatePicker.show(
//                            // 默认选择当前日期，如果已有选择则使用已选择的日期
//                            value = if (onlineTimeValue.isNotEmpty()) {
//                                try {
//                                    DateTimeUtil.parseDate(onlineTimeValue)
//                                } catch (e: Exception) {
//                                    DateTimeUtil.now()
//                                }
//                            } else {
//                                DateTimeUtil.now()
//                            },
//                            // 可选择的日期范围从电站安装时间开始到当前日期
//                            start = startDate,
//                            end = DateTimeUtil.now()
//                        ) { selectedDate ->
//                            // 更新选择的日期
//                            onlineTimeValue = DateTimeUtil.formatDate(selectedDate)
//                            onlineTimeError = null
//                        }
//                    },
//                    trailingIconCom = {
//                        Image(
//                            painter = painterResource(Res.drawable.right_arrow),
//                            modifier = Modifier.size(18.dp),
//                            contentDescription = "Select Online Time"
//                        )
//                    }
//                )
//
//                 val onlineType=stringResource(Res.string.online_type)
//                // 并网类型
//                CommonTitleField(
//                    value = onlineTypeValue,
//                    onValueChange = { },
//                    titleText = stringResource(Res.string.online_type),
//                    isSelected = onlineTypeValue.isNotEmpty(),
//                    placeholderCom = {
//                        PlaceholderText(Res.string.please_select)
//                    },
//                    modifier = Modifier
//                        .weight(1f)
//                        .padding(start = 5.dp),
//                    textFieldHeight = 48,
//                    cornerRadius = 30,
//                    titleBottom = 10,
//                    textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
//                    isReadOnly = true,
//                    isError = onlineTypeError != null,
//                    errorText = onlineTypeError ?: "",
//                    onBoxClick = {
//                        onlineTypePicker.show(
//                            title = onlineType,
//                            range = onlineTypeRange,
//                            value = if (onlineTypeIndex < 0) 0 else onlineTypeIndex
//                        ) { index ->
//                            onlineTypeIndex = index
//                            onlineTypeValue = onlineTypeRange[index]
//                            onlineTypeError = null
//                        }
//                    },
//                    trailingIconCom = {
//                        Image(
//                            painter = painterResource(Res.drawable.right_arrow),
//                            modifier = Modifier.size(18.dp),
//                            contentDescription = "Select Online Type"
//                        )
//                    }
//                )
//            }

            val contactWayStr=stringResource(Res.string.contact_way)

            // 通讯方式
            CommonTitleField(
                value = communicationMethodValue,
                onValueChange = { },
                titleText = stringResource(Res.string.contact_way),
                isSelected = communicationMethodValue.isNotEmpty(),
                placeholderCom = {
                    PlaceholderText(Res.string.please_select)
                },
                modifier = Modifier
                    .padding(top = 5.dp),
                textFieldHeight = 48,
                cornerRadius = 30,
                titleBottom = 10,
                textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                isReadOnly = true,
                isError = communicationMethodError != null,
                errorText = communicationMethodError ?: "",
                onBoxClick = {
                    communicationMethodPicker.show(
                        title = contactWayStr,
                        range = communicationMethodRange,
                        value = if (communicationMethodIndex < 0) 0 else communicationMethodIndex
                    ) { index ->
                        communicationMethodIndex = index
                        communicationMethodValue = communicationMethodRange[index]
                        communicationMethodError = null
                    }
                },
                trailingIconCom = {
                    Image(
                        painter = painterResource(Res.drawable.right_arrow),
                        modifier = Modifier.size(18.dp),
                        contentDescription = "Select Communication Method"
                    )
                }
            )

            val errorStationTimeStr = stringResource(Res.string.error_station_time)

            Box(modifier = Modifier.padding(top = 10.dp, bottom = 5.dp)) {
                TitleItem(text = stringResource(Res.string.collect_time))
            }

            // 日出时间和日落时间在同一行
            Row(
                modifier = Modifier.padding(top = 5.dp, bottom = 20.dp)
            ) {

                // 日出时间
                CommonTitleField(
                    value = sunUpTimeValue,
                    onValueChange = { },
                    isSelected = sunUpTimeValue.isNotEmpty(),
                    placeholderCom = {
                        PlaceholderText(Res.string.sunrise_placeholder_format)
                    },
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = 5.dp),
                    textFieldHeight = 48,
                    cornerRadius = 30,
                    titleBottom = 10,
                    textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                    isReadOnly = true,
                    isError = sunUpTimeError != null,
                    errorText = sunUpTimeError ?: "",
                    onBoxClick = {
                        startTimePicker.show(currentLocalTime, type = TimeType.MINUTE) {
                            sunUpTimeValue = it.toString()
                            sunUpTimeError = null
                        }
                    },
                    trailingIconCom = {
                        Image(
                            painter = painterResource(Res.drawable.right_arrow),
                            modifier = Modifier.size(18.dp),
                            contentDescription = "Select Time"
                        )
                    }
                )

                // 日落时间
                CommonTitleField(
                    value = sunDownTimeValue,
                    onValueChange = { },
                    isSelected = sunDownTimeValue.isNotEmpty(),
                    placeholderCom = {
                        PlaceholderText(Res.string.sunset_placeholder_format)
                    },
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 5.dp),
                    textFieldHeight = 48,
                    cornerRadius = 30,
                    titleBottom = 10,
                    textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                    isReadOnly = true,
                    isError = sunDownTimeError != null,
                    errorText = sunDownTimeError ?: "",
                    onBoxClick = {
                        endTimePicker.show(currentLocalTime, type = TimeType.MINUTE) {
                            if (sunUpTimeValue.isNotEmpty()) {
                                val startTime = LocalTime.parse(sunUpTimeValue)
                                if (it < startTime) {
                                    sunDownTimeError = errorStationTimeStr
                                    return@show
                                }
                            }
                            sunDownTimeValue = it.toString()
                            sunDownTimeError = null
                        }
                    },
                    trailingIconCom = {
                        Image(
                            painter = painterResource(Res.drawable.right_arrow),
                            modifier = Modifier.size(18.dp),
                            contentDescription = "Select Time"
                        )
                    }
                )
            }

            Spacer(modifier = Modifier.weight(1f))

            val coroutineScope = rememberCoroutineScope()
            val errorMsgEmptyField = stringResource(Res.string.error_empty_field)
            val errorMsgInvalidPower = stringResource(Res.string.error_invalid_power)
            val errorMsgTimeError = stringResource(Res.string.error_station_time)

            ConfirmButton(stringResource(Res.string.save), true) {
                // 重置错误
                systemNameError = null
                regionError = null
                streetNameError = null
                powerError = null
                typeError = null
                sunUpTimeError = null
                sunDownTimeError = null
                communicationMethodError = null
                powerPriceError = null
                onlineTimeError = null
                onlineTypeError = null
                var hasError = false

                // 验证电站名称
                if (systemNameValue.trim().isEmpty()) {
                    systemNameError = errorMsgEmptyField
                    hasError = true
                }

                // 验证地址
                if (regionValue.trim().isEmpty()) {
                    regionError = errorMsgEmptyField
                    hasError = true
                }

                // 验证街道
                if (streetNameValue.trim().isEmpty()) {
                    streetNameError = errorMsgEmptyField
                    hasError = true
                }

                // 验证功率
                if (powerValue.trim().isEmpty()) {
                    powerError = errorMsgEmptyField
                    hasError = true
                } else {
                    try {
                        val powerInt = powerValue.toInt()
                        if (powerInt <= 0) {
                            powerError = errorMsgInvalidPower
                            hasError = true
                        }
                    } catch (e: NumberFormatException) {
                        powerError = errorMsgInvalidPower
                        hasError = true
                    }
                }

                
                // 验证通讯方式
                if (communicationMethodValue.trim().isEmpty()) {
                    communicationMethodError = errorMsgEmptyField
                    hasError = true
                }
                
                // 验证电价配置
                if (powerPriceValue.trim().isEmpty()) {
                    powerPriceError = errorMsgEmptyField
                    hasError = true
                }

                // 验证日出时间
                if (sunUpTimeValue.trim().isEmpty()) {
                    sunUpTimeError = errorMsgEmptyField
                    hasError = true
                }

                // 验证日落时间
                if (sunDownTimeValue.trim().isEmpty()) {
                    sunDownTimeError = errorMsgEmptyField
                    hasError = true
                } else if (sunUpTimeValue.isNotEmpty()) {
                    try {
                        val startTime = LocalTime.parse(sunUpTimeValue)
                        val endTime = LocalTime.parse(sunDownTimeValue)
                        if (endTime < startTime) {
                            sunDownTimeError = errorMsgTimeError
                            hasError = true
                        }
                    } catch (e: Exception) {
                        sunDownTimeError = errorMsgTimeError
                        hasError = true
                    }
                }

                // 验证并网时间
//                if (onlineTimeValue.trim().isEmpty()) {
//                    onlineTimeError = errorMsgEmptyField
//                    hasError = true
//                }
//
//                // 验证并网类型
//                if (onlineTypeValue.trim().isEmpty()) {
//                    onlineTypeError = errorMsgEmptyField
//                    hasError = true
//                }

                if (hasError) {
                    return@ConfirmButton
                }

                // 准备提交的数据
                val stationMap = mutableMapOf(
                    "systemName" to systemNameValue.trim(),
                    "streetName" to streetNameValue.trim(),
                    "power" to powerValue.trim(),
                    "type" to (communicationMethodIndex + 1).toString(),
                    "sunuptime" to sunUpTimeValue.trim(),
                    "sundowntime" to sunDownTimeValue.trim(),
                    "memberId" to AppGlobal.mId,
//                    "powerPrice" to (powerPriceIndex + 1).toString(),
//                    "onlineTime" to onlineTimeValue.trim(),
//                    "onlineType" to (onlineTypeIndex + 1).toString()
                )

                // 处理地区信息
                val region1 = RegionDataManager.getRegionsByLevel(2).getOrNull(regionValues[0])
                val region2Children = RegionDataManager.getChildren(region1?.id ?: 0)
                val region2 = if (region2Children.isEmpty()) null else region2Children.getOrNull(regionValues[1])
                
                val region3Children = RegionDataManager.getChildren(region2?.id ?: 0)
                val region3 = if (region3Children.isEmpty()) null else region3Children.getOrNull(regionValues[2])
                
                stationMap["countriesId"] = region1?.id?.toString() ?: ""
                stationMap["province"] = region2?.id?.toString() ?: ""
                stationMap["cityId"] = region3?.id?.toString() ?: ""
                
                // 处理直辖市的特殊情况
                val directCities = listOf("北京", "上海", "天津", "重庆")
                stationMap["districtId"] = if (region2?.name in directCities) {
                    region2?.name ?: ""
                } else {
                    region3?.name ?: ""
                }

                equipmentViewModel.addStation(stationMap, errorBlock = {
                    showFailDialog = true
                    coroutineScope.launch {
                        delay(2000)
                        showFailDialog = false
                    }
                }) {
                    showSuccessDialog = true
                    coroutineScope.launch {
                        delay(2000)
                        showSuccessDialog = false
                        navHostController.navigate(Route.HOME)
                    }
                }
            }

            Spacer(modifier = Modifier.height(60.dp))
        }
    }
}