package com.ymx.photovoltaic.ui.page.user

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.local.CacheManager
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.platform.getPlatform
import com.ymx.photovoltaic.viewmodel.AppScreenViewModel
import kotlinx.coroutines.delay
import org.jetbrains.compose.resources.painterResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.lj_splash1
import photovoltaic_kmp_app.composeapp.generated.resources.lj_splash2
import photovoltaic_kmp_app.composeapp.generated.resources.lj_splash3

/**
 * 启动页面 - 在Android展示三张启动图片，iOS直接跳转
 */
@Composable
fun SplashScreen(
    navController: NavHostController,
    appScreenViewModel: AppScreenViewModel = getKoin().get()
) {

    // 判断当前平台
    val isAndroid = getPlatform().name.startsWith("Android")
    
    // 检查登录状态
    val isLoginState by appScreenViewModel.isLogin.collectAsState()
    
    if (isAndroid) {

        SetStatusBar(Color.Black, false)

        // Android平台显示启动图片
        
        // 当前显示的启动图片索引
        var currentSplashIndex by remember { mutableStateOf(0) }
        
        // 启动图片资源数组
        val splashImages = listOf(
            Res.drawable.lj_splash1,
            Res.drawable.lj_splash2,
            Res.drawable.lj_splash3
        )
        
        // 每张图片显示的时间（毫秒）
        val displayDurations = listOf(2000L, 2000L, 2000L)
        
        // 控制图片显示的状态
        var isVisible by remember { mutableStateOf(true) }
        
        // 显示闪屏页的效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black),
            contentAlignment = Alignment.Center
        ) {
            // 使用AnimatedVisibility实现淡入淡出效果
            AnimatedVisibility(
                visible = isVisible,
                enter = fadeIn(animationSpec = tween(500)),
                exit = fadeOut(animationSpec = tween(500))
            ) {
                Image(
                    painter = painterResource(splashImages[currentSplashIndex]),
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Fit
                )
            }
        }
        
        // 启动时执行的协程效果
        LaunchedEffect(Unit) {
            // 依次显示三张启动图片
            for (i in splashImages.indices) {
                // 显示当前图片
                currentSplashIndex = i
                isVisible = true
                delay(100) // 短暂延迟确保动画有时间启动
                
                // 等待显示时间
                delay(displayDurations[i] - 600) // 减去动画时间
                
                // 如果不是最后一张图片，则淡出当前图片
                if (i < splashImages.size - 1) {
                    isVisible = false
                    delay(500) // 等待淡出动画完成
                }
            }
            
            // 所有图片显示完成后，导航到下一个页面
            navigateToNextScreen(navController, isLoginState)
        }
    } else {
        // iOS平台不显示启动图片，直接导航到下一个页面
        LaunchedEffect(Unit) {
            navigateToNextScreen(navController, isLoginState)
        }
    }
    
    // 清理资源
    DisposableEffect(Unit) {
        onDispose { }
    }
}

/**
 * 导航到下一个页面
 */
private fun navigateToNextScreen(navController: NavHostController, isLoginState: Boolean) {
    if (isLoginState) {
        val user = CacheManager.getUser()
        if (user?.mId != null && user.mId != "") {
            AppGlobal.mId = user.mId
            
            navController.navigate(Route.HOME) {
                popUpTo(Route.SPLASH) { inclusive = true } // 清除返回栈
            }
        } else {
            navController.navigate(Route.LOGIN) {
                popUpTo(Route.SPLASH) { inclusive = true } // 清除返回栈
            }
        }
    } else {
        navController.navigate(Route.LOGIN) {
            popUpTo(Route.SPLASH) { inclusive = true } // 清除返回栈
        }
    }
}