package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp


@Composable
fun EditBasicTextField(value: String,
                       onValueChange: (String) -> Unit,
                       keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
                       keyboardActions: KeyboardActions = KeyboardActions.Default,//键盘的选项,例如键盘类型、输入法
)
{

    BasicTextField(
        value = value,
        onValueChange = onValueChange,
        textStyle = TextStyle(
            color = Color.Black,           // 字体颜色
            fontSize = 16.sp, // 字体大小
            textAlign = TextAlign.Right
        ),
        modifier = Modifier.width(150.dp),
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        singleLine = true,
    )

}