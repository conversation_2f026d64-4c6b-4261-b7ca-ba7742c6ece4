package com.ymx.photovoltaic.util

import com.ymx.photovoltaic.platform.GbkDecoder
import com.ymx.photovoltaic.platform.NetworkConnectivity
import io.ktor.network.selector.SelectorManager
import io.ktor.network.sockets.aSocket
import io.ktor.network.sockets.openReadChannel
import io.ktor.network.sockets.openWriteChannel
import io.ktor.utils.io.readAvailable
import io.ktor.utils.io.writeFully
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull


/**
 * 使用Ktor网络库实现的跨平台Socket通信工具类
 *
 * 
 * ## 网络权限
 * 使用此类仍然需要在各平台配置适当的网络权限:
 * - Android: android.permission.INTERNET, android.permission.ACCESS_NETWORK_STATE
 * - iOS: 在Info.plist中添加适当的网络权限描述
 * 
 * 注意: 此实现仅支持通过WiFi网络进行Socket通信
 */
object KtorSocketUtils {
    
    sealed class SocketResult {
        data class Success(val response: String) : SocketResult()
        data class Error(val message: String) : SocketResult()
    }
    
    private val socketScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    /**
     * 发送Socket命令到指定地址和端口
     * 
     * 注意：该方法只能通过WiFi网络发送，如果设备未连接WiFi，将返回错误
     *
     * @param command 要发送的字节数组命令
     * @param ipAddress 目标IP地址，默认为***********
     * @param port 目标端口，默认为5566
     * @param callback 结果回调函数
     */
    fun sendSocketCommand(
        command: ByteArray,
        ipAddress: String = "***********",
        port: Int = 5566,
        callback: (SocketResult) -> Unit
    ) {
        // 检查WiFi连接状态
        if (!NetworkConnectivity.isWifiConnected()) {
            println("错误：未连接WiFi网络，无法发送Socket命令")
            callback(SocketResult.Error("请连接WiFi网络后再试"))
            return
        }
        
        // 尝试强制使用WiFi网络（Android平台有效）
        NetworkConnectivity.forceUseWifiForRequests()
        
        // 打印发送的命令
        val hexString = command.joinToString("") { 
            it.toUByte().toString(16).padStart(2, '0').uppercase() 
        }
        println("发送命令: $hexString (${command.size} 字节)")
        
        socketScope.launch {
            try {
                val result = sendDataViaKtor(ipAddress, port, command)
                callback(result)
            } catch (e: Exception) {
                println("Socket错误: ${e.message}")
                callback(SocketResult.Error("连接失败: ${e.message}"))
            } finally {
                // 解除WiFi网络绑定
                NetworkConnectivity.releaseWifiForRequests()
            }
        }
    }
    
    /**
     * 使用Ktor网络库发送数据
     *
     * @param ipAddress 目标IP地址
     * @param port 目标端口
     * @param data 要发送的数据
     * @return SocketResult 操作结果
     */
    private suspend fun sendDataViaKtor(
        ipAddress: String,
        port: Int,
        data: ByteArray
    ): SocketResult = withContext(Dispatchers.IO) {
        var selectorManager: SelectorManager? = null
        try {
            // 创建Ktor Socket连接
            selectorManager = SelectorManager(Dispatchers.IO)
            val socket = aSocket(selectorManager).tcp().connect(ipAddress, port) {
                socketTimeout = 8000 // 增加超时时间到8秒
            }
            
            try {
                // 发送数据
                val outputChannel = socket.openWriteChannel(autoFlush = true)
                outputChannel.writeFully(data, 0, data.size)
                
                // 读取响应
                val inputChannel = socket.openReadChannel()
                val responseBuilder = StringBuilder()
                val responseBytes = mutableListOf<Byte>()
                
                // 使用超时机制限制整个读取过程
                val result = withTimeoutOrNull(10000) { // 10秒超时
                    // 使用ByteReadChannel直接读取响应
                    val buffer = ByteArray(1024)
                    var bytesRead: Int
                    var hasEndMarker = false
                    var readCount = 0
                    val maxReadAttempts = 10
                    
                    while (!hasEndMarker && readCount < maxReadAttempts) {
                        try {
                            bytesRead = inputChannel.readAvailable(buffer, 0, buffer.size)
                            
                            if (bytesRead < 0) break
                            
                            if (bytesRead > 0) {
                                // 由于GBK编码问题，先保存原始字节数据
                                for (i in 0 until bytesRead) {
                                    responseBytes.add(buffer[i])
                                }
                                
                                // 使用UTF-8进行临时解码，仅用于查找结束标记
                                // 在实际应用中，可能需要特定的处理方式来处理GBK编码的"OK"或"ERROR"标记
                                val tempChunk = buffer.decodeToString(0, bytesRead)
                                println("接收原始数据: ${bytesToHex(buffer, 0, bytesRead)}")
                                
                                // 检查是否包含结束标记
                                if (tempChunk.contains("OK") || tempChunk.contains("ERROR")) {
                                    hasEndMarker = true
                                }
                            } else {
                                // 没有读取到数据，增加尝试次数
                                readCount++
                            }
                        }  catch (e: Exception) {
                            println("读取数据时出错: ${e.message}")
                            break
                        }
                    }
                    
                    // 将收集到的字节转换为GBK编码的字符串
                    if (responseBytes.isNotEmpty()) {
                        val byteArray = responseBytes.toByteArray()
                        // 使用平台特定的GBK解码器解码
                        val response = GbkDecoder.decodeGbk(byteArray)
                        responseBuilder.append(response)
                        println("响应数据(GBK): $response")
                    }
                    
                    responseBuilder.toString()
                }
                
                return@withContext if (result != null) {
                    if (result.isNotEmpty()) {
                        SocketResult.Success(result)
                    } else {
                        SocketResult.Error("未收到响应数据")
                    }
                } else {
                    SocketResult.Error("读取响应超时")
                }
            }  catch (e: Exception) {
                println("Socket通信错误: ${e.message}")
                return@withContext SocketResult.Error("通信错误: ${e.message}")
            } finally {
                // 关闭连接
                try {
                    socket.close()
                } catch (e: Exception) {
                    println("关闭Socket时出错: ${e.message}")
                }
            }
        } catch (e: Exception) {
            println("Ktor Socket错误: ${e.message}")
            return@withContext SocketResult.Error("连接失败: ${e.message}")
        } finally {
            // 关闭SelectorManager
            try {
                selectorManager?.close()
            } catch (e: Exception) {
                println("关闭SelectorManager时出错: ${e.message}")
            }
        }
    }
    
    /**
     * 将字节数组转换为16进制字符串，用于调试
     */
    private fun bytesToHex(bytes: ByteArray, offset: Int, length: Int): String {
        val hexChars = CharArray(length * 2)
        for (i in 0 until length) {
            val v = bytes[offset + i].toInt() and 0xFF
            hexChars[i * 2] = "0123456789ABCDEF"[v ushr 4]
            hexChars[i * 2 + 1] = "0123456789ABCDEF"[v and 0x0F]
        }
        return hexChars.concatToString()
    }
    
    // 将十六进制字符串转换为字节数组
    fun hexStringToBytes(hex: String): ByteArray? {
        var processedHex = hex.trim()
        
        // 补0到10位
        while (processedHex.length < 10) {
            processedHex = "0$processedHex"
        }
        
        // 转换为大写
        processedHex = processedHex.uppercase()

        if (processedHex.isEmpty() || processedHex.length % 2 != 0) {
            return null
        }

        val len = processedHex.length / 2
        val bytes = ByteArray(len)
        
        try {
            for (i in 0 until len) {
                val h = charToByte(processedHex[i * 2])
                val l = charToByte(processedHex[i * 2 + 1])
                bytes[i] = ((h shl 4) or l).toByte()
            }
            return bytes
        } catch (e: Exception) {
            return null
        }
    }

    private fun charToByte(c: Char): Int {
        return when (c) {
            in '0'..'9' -> c - '0'
            in 'A'..'F' -> c - 'A' + 10
            else -> throw IllegalArgumentException("无效的十六进制字符: $c")
        }
    }

    // 转换芯片ID
    fun convertChipId(chipId: String): ByteArray? {
        // 检查是否是7位且以"0"开头的格式
        if (chipId.length == 7 && chipId.startsWith("0")) {
            val bytes = ByteArray(5)
            try {
                var processedChipId = chipId
                // 补齐到10位
                processedChipId = processedChipId.padStart(10, '0')
                
                // 按照原逻辑进行转换
                bytes[0] = processedChipId.substring(processedChipId.length - 10, processedChipId.length - 8).toInt().toByte()
                bytes[1] = processedChipId.substring(processedChipId.length - 8, processedChipId.length - 6).toInt().toByte()
                bytes[2] = processedChipId.substring(processedChipId.length - 6, processedChipId.length - 4).toInt().toByte()
                bytes[3] = processedChipId.substring(processedChipId.length - 4, processedChipId.length - 2).toInt().toByte()
                bytes[4] = processedChipId.substring(processedChipId.length - 2).toInt().toByte()
                
                return bytes
            } catch (e: Exception) {
                return null
            }
        } else {
            // 如果不是7位且以"0"开头的格式，使用hexStringToBytes方法
            return hexStringToBytes(chipId)
        }
    }
} 