package com.ymx.photovoltaic.util

import kotlinx.datetime.Clock
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.minus
import kotlinx.datetime.plus
import kotlinx.datetime.toLocalDateTime

/**
 * 日期时间工具类，提供多平台环境下的日期时间格式化功能
 */
object DateTimeUtil {
    
    /**
     * 格式化时间戳为标准日期时间字符串（格式：yyyy-MM-dd HH:mm:ss）
     * @param timestamp 毫秒时间戳
     * @return 格式化后的字符串
     */
    fun formatTimestamp(timestamp: Long): String {
        val instant = Instant.fromEpochMilliseconds(timestamp)
        return formatTimeInstant(instant)
    }

    /**
     * 格式化LocalDate为标准日期字符串（格式：yyyy-MM-dd）
     * @param date LocalDate实例
     * @return 格式化后的字符串
     */
    fun formatDate(date: LocalDate): String {
        return "${date.year}-${date.monthNumber.toString().padStart(2, '0')}-${date.dayOfMonth.toString().padStart(2, '0')}"
    }

    /**
     * 从字符串解析日期（格式：yyyy-MM-dd）
     * @param dateString 日期字符串
     * @return LocalDate实例
     */
    fun parseDate(dateString: String): LocalDate {
        val parts = dateString.split("-")
        if (parts.size != 3) {
            throw IllegalArgumentException("Invalid date format. Expected yyyy-MM-dd")
        }
        
        return try {
            LocalDate(parts[0].toInt(), parts[1].toInt(), parts[2].toInt())
        } catch (e: Exception) {
            throw IllegalArgumentException("Invalid date format. Expected yyyy-MM-dd", e)
        }
    }

    fun formatDaystamp(timestamp: Long): String {
        val instant = Instant.fromEpochMilliseconds(timestamp)
        return formatDateInstant(instant)
    }
    /**
     * 格式化Instant为标准日期时间字符串（格式：yyyy-MM-dd HH:mm:ss）
     * @param instant Instant实例
     * @return 格式化后的字符串
     */
    private fun formatTimeInstant(instant: Instant): String {
        val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
        return "${localDateTime.year}-${localDateTime.monthNumber.toString().padStart(2, '0')}-${localDateTime.dayOfMonth.toString().padStart(2, '0')} " +
               "${localDateTime.hour.toString().padStart(2, '0')}:${localDateTime.minute.toString().padStart(2, '0')}:${localDateTime.second.toString().padStart(2, '0')}"
    }

    /**
     * 格式化Instant为标准日期时间字符串（格式：yyyy-MM-dd）
     * @param instant Instant实例
     * @return 格式化后的字符串
     */
    private fun formatDateInstant(instant: Instant): String {
        val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
        return "${localDateTime.year}-${localDateTime.monthNumber.toString().padStart(2, '0')}-${localDateTime.dayOfMonth.toString().padStart(2, '0')} "
    }
    
    /**
     * 获取当前时间的格式化字符串（格式：yyyy-MM-dd HH:mm:ss）
     * @return 当前时间的格式化字符串
     */
    fun getCurrentTimeFormatted(): String {
        return formatTimeInstant(Clock.System.now())
    }
    
    /**
     * 获取带中括号的当前时间格式化字符串（格式：[yyyy-MM-dd HH:mm:ss]）
     * 用于替代 java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date())
     * @return 带中括号的当前时间格式化字符串
     */
    fun getFormattedTimestampWithBrackets(): String {
        return "[${getCurrentTimeFormatted()}]"
    }

    /**
     * 获取适合用于文件名的当前时间格式化字符串（格式：yyyyMMdd_HHmmss）
     * 用于替代 java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(Date())
     * @return 适合用于文件名的当前时间格式化字符串
     */
    fun formatForFileName(): String {
        val localDateTime = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        return "${localDateTime.year}${localDateTime.monthNumber.toString().padStart(2, '0')}${localDateTime.dayOfMonth.toString().padStart(2, '0')}_" +
               "${localDateTime.hour.toString().padStart(2, '0')}${localDateTime.minute.toString().padStart(2, '0')}${localDateTime.second.toString().padStart(2, '0')}"
    }

    /**
     * 获取当前日期
     * @return LocalDate实例
     */
    fun now(): LocalDate {
        return Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
    }

    /**
     * 格式化日期为yyyy-MM格式
     * @param date LocalDate实例
     * @return 格式化后的字符串
     */
    fun formatYearMonth(date: LocalDate): String {
        return "${date.year}-${date.monthNumber.toString().padStart(2, '0')}"
    }

    /**
     * 从时间戳创建LocalDate实例
     * @param timestamp 毫秒时间戳
     * @return LocalDate实例
     */
    fun fromEpochMillis(timestamp: Long): LocalDate {
        return Instant.fromEpochMilliseconds(timestamp)
            .toLocalDateTime(TimeZone.currentSystemDefault())
            .date
    }
    
    /**
     * 日期减去指定天数
     * @param date 原始日期
     * @param days 要减去的天数
     * @return 新的日期
     */
    fun minusDays(date: LocalDate, days: Int): LocalDate {
        return date.minus(days, DateTimeUnit.DAY)
    }
    
    /**
     * 日期加上指定天数
     * @param date 原始日期
     * @param days 要加上的天数
     * @return 新的日期
     */
    fun plusDays(date: LocalDate, days: Int): LocalDate {
        return date.plus(days, DateTimeUnit.DAY)
    }
    
    /**
     * 日期减去指定月数
     * @param date 原始日期
     * @param months 要减去的月数
     * @return 新的日期
     */
    fun minusMonths(date: LocalDate, months: Int): LocalDate {
        return date.minus(months, DateTimeUnit.MONTH)
    }
    
    /**
     * 日期加上指定月数
     * @param date 原始日期
     * @param months 要加上的月数
     * @return 新的日期
     */
    fun plusMonths(date: LocalDate, months: Int): LocalDate {
        return date.plus(months, DateTimeUnit.MONTH)
    }
    
    /**
     * 日期减去指定年数
     * @param date 原始日期
     * @param years 要减去的年数
     * @return 新的日期
     */
    fun minusYears(date: LocalDate, years: Int): LocalDate {
        return date.minus(years, DateTimeUnit.YEAR)
    }
    
    /**
     * 日期加上指定年数
     * @param date 原始日期
     * @param years 要加上的年数
     * @return 新的日期
     */
    fun plusYears(date: LocalDate, years: Int): LocalDate {
        return date.plus(years, DateTimeUnit.YEAR)
    }
}