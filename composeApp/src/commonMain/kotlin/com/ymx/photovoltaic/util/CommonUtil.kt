package com.ymx.photovoltaic.util

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlin.jvm.JvmStatic
import kotlin.math.abs
import kotlin.math.roundToInt

/**
 */
object CommonUtil {

    /** 判断String是否为空或空串，主要提供给xml中dataBinding用 */
    @JvmStatic
    fun isEmpty(str: String?): <PERSON><PERSON><PERSON> {
        return str?.isEmpty() ?: true
    }

    // 四舍五入保留2位小数
    fun Float.roundTo2Decimals(): Float {
        return (this * 100).roundToInt() / 100f
    }

    // 四舍五入保留3位小数
    fun Float.roundTo3Decimals(): Float {
        return (this * 1000).roundToInt() / 1000f
    }


    // 四舍五入保留2位小数
    fun Double.roundTo2Decimals(): Float {
        return ((this * 100).roundToInt() / 100f).toFloat()
    }

    // 四舍五入保留3位小数
    fun Double.roundTo3Decimals(): Float {
        return ((this * 1000).roundToInt() / 1000f).toFloat()
    }

    // 四舍五入保留2位小数
    fun Int.roundTo2Decimals(): Float {
        return this.toFloat()
    }

    // 四舍五入保留3位小数
    fun Int.roundTo3Decimals(): Float {
        return this.toFloat()
    }

    fun getDaysSinceTime(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val givenInstant = Instant.fromEpochMilliseconds(timestamp)
        val currentInstant = Clock.System.now()
        
        // 计算两个时间点之间的毫秒差
        val diffInMillis = currentInstant.toEpochMilliseconds() - givenInstant.toEpochMilliseconds()
        // 转换为天数（1天 = 24小时 * 60分钟 * 60秒 * 1000毫秒）
        return abs(diffInMillis / (24 * 60 * 60 * 1000))
    }
}

fun encodeUrl(input: String): String {
    // 简单实现URL编码，替换常见特殊字符
    return input.replace(" ", "%20")
        .replace("!", "%21")
        .replace("#", "%23")
        .replace("$", "%24")
        .replace("&", "%26")
        .replace("'", "%27")
        .replace("(", "%28")
        .replace(")", "%29")
        .replace("*", "%2A")
        .replace("+", "%2B")
        .replace(",", "%2C")
        .replace("/", "%2F")
        .replace(":", "%3A")
        .replace(";", "%3B")
        .replace("=", "%3D")
        .replace("?", "%3F")
        .replace("@", "%40")
        .replace("[", "%5B")
        .replace("]", "%5D")
}