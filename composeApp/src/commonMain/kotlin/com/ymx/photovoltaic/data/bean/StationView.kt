package com.ymx.photovoltaic.data.bean

import kotlinx.serialization.Serializable

@Serializable
data class StationView(
    val dayKwh: Float = 0f,
    val data: List<InnerOptimizer>,
    val groupList: List<InnerGroup>,
    val collectorList: List<InnerCollector> = emptyList(),
    val timeAxis: List<String>,
    val timeAxisBeginTime: String = "",
    val timeAxisEndTime: String = "",
    val timeAxisStep: String = ""
) {


    @Serializable
    // 定义内部类
    data class InnerOptimizer(
        val chipId: String = "",
        val groupId: String = "",
        val hv: String = "",
        val gapTop: String = "",
        val gapLeft: String = "",
        val componentTemperature: Int = -130,
        val mosTemperature: Int = -130,
        val outputCurrent: Int = -1,
        val outputVoltage: Int = -1,
        val status: Int = 0,
        val equipmentId: String = "",
        var groupName: String = "",
        var power: Float = -1f,
        var kwh: Double = -1.0,
        var powerRatio: String = "",
        var ratedPowerRatio: Float = 0f,
        var groupStatus: Int = 0,
    )

    @Serializable
    data class InnerGroup(val groupId: String = "", val groupName: String = "", val power: Int = 0)

    @Serializable
    data class InnerCollector(val cloudName: String = "", val imei: String = "")
}