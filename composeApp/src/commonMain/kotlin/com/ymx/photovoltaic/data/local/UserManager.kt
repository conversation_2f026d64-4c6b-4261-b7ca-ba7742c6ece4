package com.ymx.photovoltaic.data.local

import com.russhwolf.settings.set
import com.ymx.photovoltaic.platform.SettingsManager


/**
 * 数据管理类
 *
 * <AUTHOR>  17/8/25.
 */
object UserManager {

    /** MMKV独有的mapId */
    private const val USER_MAP_ID = "user"

    /** 保存用户ID的KEY */
    private const val KEY_USER_ID = "user_id"
    
    /** 保存用户Token的KEY */
    private const val KEY_TOKEN = "token"

    /** 保存最后一次登录成功的用户名的KEY */
    private const val KEY_LAST_USER_NAME = "lastUserName"
    private const val KEY_LAST_USER_PASSWORD = "lastUserPassword"

    private val settings by lazy { SettingsManager.defaultSettings }

    /**
     * 存储用户ID到本地
     *
     * @param userId    用户ID
     */
    fun saveUserId(userId: String) {
        settings[KEY_USER_ID] = userId
    }

    /**
     * 获取存储的用户ID
     *
     * @return 用户ID
     */
    private fun getUserId(): String {
        return settings.getString(KEY_USER_ID, "")
    }


    /**
     * 是否已登录(自动登录的判断)
     *
     * @return 是否已登录
     */
    fun isLogin(): Boolean {
        return getUserId().isNotEmpty()
    }

    /**
     * 存储用户token到本地
     *
     * @param token    Token
     */
    fun saveToken(token: String) {
        settings[KEY_TOKEN] = token
    }

    /**
     * 获取登录用户Token信息
     *
     * @return 登录Token
     */
    fun getToken(): String {
        return settings.getString(KEY_TOKEN, "")
    }


    /**
     * 存储上一次登录成功的用户名(退出时显示在登录名里)
     *
     * @param userName 用户名
     */
    fun saveLastUserName(userName: String) {
        settings[KEY_LAST_USER_NAME] = userName
    }

    /**
     * 获取上一次登录成功的用户名(退出时显示在登录名里)
     *
     * @return 上一次登录成功的用户名
     */
    fun getLastUserName(): String {
        return settings.getString(KEY_LAST_USER_NAME, "")
    }

    /**
     * 存储上一次登录成功的用户密码(自动登录)
     *
     * @param password 密码
     */
    fun storeLastUserPwd(password: String) {
        settings[KEY_LAST_USER_PASSWORD] = password
    }


    /**
     * 获取上一次登录成功的用户密码(自动登录)
     *
     * @return 上一次登录成功的用户密码
     */
    fun getLastUserPassword(): String {
        return settings.getString(KEY_LAST_USER_PASSWORD, "")
    }

}
