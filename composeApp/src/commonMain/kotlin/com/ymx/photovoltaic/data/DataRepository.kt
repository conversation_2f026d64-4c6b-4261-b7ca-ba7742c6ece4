package com.ymx.photovoltaic.data

import com.ymx.photovoltaic.data.bean.Collector
import com.ymx.photovoltaic.data.bean.Group
import com.ymx.photovoltaic.data.bean.HistoryKwh
import com.ymx.photovoltaic.data.bean.Optimizer
import com.ymx.photovoltaic.data.bean.PowerDayInfo
import com.ymx.photovoltaic.data.bean.Region
import com.ymx.photovoltaic.data.bean.Relay
import com.ymx.photovoltaic.data.bean.ScanDevice
import com.ymx.photovoltaic.data.bean.SearchGroup
import com.ymx.photovoltaic.data.bean.Station
import com.ymx.photovoltaic.data.bean.StationView
import com.ymx.photovoltaic.data.bean.TendDayInfo
import com.ymx.photovoltaic.data.bean.TendMinuteGroupInfo
import com.ymx.photovoltaic.data.bean.TendMinuteInfo
import com.ymx.photovoltaic.data.bean.User
import com.ymx.photovoltaic.data.bean.Version
import com.ymx.photovoltaic.data.bean.Warning
import com.ymx.photovoltaic.data.bean.WarningSettingModel
import com.ymx.photovoltaic.data.bean.WarningStats
import com.ymx.photovoltaic.data.bean.Weather
import com.ymx.photovoltaic.data.response.ApiResponse
import com.ymx.photovoltaic.data.response.ApiResponseWithNull
import com.ymx.photovoltaic.data.response.PageReModel
import com.ymx.photovoltaic.data.response.ReModel
import com.ymx.photovoltaic.http.Api
import com.ymx.photovoltaic.http.HttpClientManager

/**
 * 数据仓库
 */
object DataRepository {

    init {
        // 添加服务器变更监听器
        HttpClientManager.addServerChangeListener {
        }
    }

    // 所有方法直接调用Api对象的方法，不需要额外处理
    suspend fun login(
        username: String,
        pwd: String,
        registId: String
    ): ApiResponseWithNull<ReModel<User>> {
        return Api.login(username, pwd, registId)
    }

    suspend fun register(
        phone: String,
        pwd: String,
        loginCode: String? = null,
        smsCode: String? = null
    ): ApiResponseWithNull<User> {
        return Api.register(phone, pwd, loginCode, smsCode)
    }

    suspend fun changePwd(
        userName: String,
        oldPwd: String,
        newPwd: String
    ): ApiResponseWithNull<String?> {
        return Api.changePwd(userName, oldPwd, newPwd)
    }

    suspend fun saveFeedback(
        information: String,
        content: String
    ): ApiResponse<Int> {
        return Api.saveFeedback(information, content)
    }

    suspend fun queryPowerStationList(
        mId: String,
        userType: String
    ): ApiResponse<PageReModel<List<Station>>> {
        return Api.queryPowerStationList(mId, userType)
    }

    suspend fun savePowerStationModel(
        stationMap: Map<String, String>
    ): ApiResponseWithNull<Int> {
        return Api.savePowerStationModel(stationMap)
    }

    suspend fun updatePowerStationModel(
        stationMap: Map<String, String>
    ): ApiResponseWithNull<Int> {
        return Api.updatePowerStationModel(stationMap)
    }

    suspend fun queryCollectorList(
        powerStationId: String?
    ): ApiResponse<ReModel<List<Collector>>> {
        return Api.queryCollectorList(powerStationId)
    }

    suspend fun addOrModCollector(collectorMap: Map<String, String>):
            ApiResponseWithNull<String> {
        return Api.addOrModCollector(collectorMap)
    }

    suspend fun viewGroup(id: String?): ApiResponse<Group> {
        return Api.viewGroup(id)
    }

    suspend fun addOrModGroup(groupMap: Map<String, Any>): ApiResponseWithNull<String> {
        return Api.addOrModGroup(groupMap)
    }

    suspend fun addOrModOptimizer(optimizerMap: Map<String, String>): ApiResponseWithNull<String> {
        return Api.addOrModOptimizer(optimizerMap)
    }

    suspend fun viewOptimizer(id: String?): ApiResponse<Optimizer> {
        return Api.viewOptimizer(id)
    }

    suspend fun queryGroupList(cloudId: String?): ApiResponse<ReModel<List<Group>>> {
        return Api.queryGroupList(cloudId)
    }

    suspend fun queryOptimizerList(
        powerStationId: String?,
        belongsGroupId: String?,
        belongsGroupFlag: String?,
        pageSize: Int
    ): ApiResponse<ReModel<List<Optimizer>>> {
        return Api.queryOptimizerList(
            powerStationId,
            belongsGroupId,
            belongsGroupFlag,
            pageSize
        )
    }

    suspend fun queryPowerStationById(powerStationId: String): ApiResponse<Station> {
        return Api.queryPowerStationById(powerStationId)
    }

    suspend fun queryComponentList(
        powerStationId: String,
        date: String,
        type: String,
        sunUpTime: String,
        sunDownTime: String,
        from: String,
        groupId: String?
    ): ApiResponse<StationView> {
        return Api.queryComponentList(
            powerStationId,
            date,
            type,
            sunUpTime,
            sunDownTime,
            from,
            groupId
        )
    }

    suspend fun queryComponentGroupList(
        mId: String,
        userType: String
    ): ApiResponse<Station> {
        return Api.queryComponentGroupList(mId, userType)
    }

    suspend fun queryComponentListByMinute(
        mId: String,
        userType: String
    ): ApiResponse<Station> {
        return Api.queryComponentListByMinute(mId, userType)
    }


    suspend fun remoteControllerAck(
        powerStationId: String?,
        status: Int,
        belongsGroupId: String?,
        id: String?,
        flag: Int
    ): ApiResponseWithNull<Int> {
        return Api.remoteControllerAck(powerStationId, status, id,belongsGroupId, flag)
    }


     suspend fun queryReportByMinute(
        powerStationId: String,
        day: String
    ): ApiResponse<TendMinuteInfo> {
        return Api.queryReportByMinute(powerStationId, day)
    }


     suspend fun queryGroup(
        powerStationId: String,
        groupName: String?
    ): ApiResponse<ReModel<List<SearchGroup>>> {
        return Api.queryGroup(powerStationId, groupName)
    }

    suspend fun queryReportByMinuteGroup(
        powerStationId: String,
        day: String,
        groupName: String,
        groupId: String
    ): ApiResponse<TendMinuteGroupInfo> {
        return Api.queryReportByMinuteGroup(powerStationId, day, groupName, groupId)
    }

    // 查询版本信息
     suspend fun appVersionList(
        versionNum: String,
        language: String
    ): ApiResponse<Version> {
        return Api.appVersionList(versionNum, language)
    }

     suspend fun queryReportByDay(
        powerStationId: String,
        month: String
    ): ApiResponse<TendDayInfo> {
        return Api.queryReportByDay(powerStationId, month)
    }

     suspend fun queryReportByMonth(
        powerStationId: String,
        year: String
    ): ApiResponse<TendDayInfo> {
        return Api.queryReportByMonth(powerStationId, year)
    }

     suspend fun queryReportByYear(
        powerStationId: String
    ): ApiResponse<TendDayInfo> {
        return Api.queryReportByYear(powerStationId)
    }

     suspend fun queryTotalReport(
        powerStationId: String
    ):
            ApiResponse<PowerDayInfo> {
        return Api.queryTotalReport(powerStationId)
    }




    suspend fun queryWarningList(
        powerStationId: String,
        pageNo: Int
    ): ApiResponse<PageReModel<List<Warning>>> {
        return Api.queryWarningList(
            powerStationId,
            pageNo
        )
    }

    suspend fun queryWarningSettingList(
        powerStationId: String
    ): ApiResponse<WarningSettingModel> {
        return Api.queryWarningSettingList(powerStationId)
    }

    suspend fun updateWarningSettingList(
        powerStationId: String,
        componentTemperature: Int?,
        offValue: Int?,
        keepOutRate: Float?,
        keepOutTime: Int?
    ): ApiResponseWithNull<Int> {
        return Api.updateWarningSettingList(
            powerStationId,
            componentTemperature,
            offValue,
            keepOutRate,
            keepOutTime
        )
    }

    suspend fun sendSms(account: String, type: String): ApiResponseWithNull<Int> {
        return Api.sendSms(account, type)
    }

    suspend fun resetPassword(
        account: String,
        code: String, 
        password: String,
        type: String,
    ): ApiResponseWithNull<Int> {
        return Api.resetPassword(account, code, password, type)
    }

    suspend fun queryWarningTypeCount(powerIdList: List<String>): ApiResponse<WarningStats> {
        return Api.queryWarningTypeCount(powerIdList)
    }

    suspend fun queryTodayWarningDetails(powerIdList: List<String>): ApiResponse<List<Warning>> {
        return Api.queryTodayWarningDetails(powerIdList)
    }

    suspend fun queryRegion(
        pid: Int,
        level: Int,
        language: String
    ): ApiResponse<List<Region>> {
        return Api.queryRegion(pid, level, language)
    }

    suspend fun getWeather(
        districtId: String,
        dataType: String
    ): ApiResponse<Weather> {
        return Api.getWeather(districtId, dataType)
    }

    suspend fun getScanDeviceInfo(
        code: String,
        language: String?
    ): ApiResponse<ScanDevice> {
        return Api.getScanDeviceInfo(code, language)
    }

    suspend fun queryRelayList(
        createUserId: String,
        operationFlag: String,
        powerId: String
    ): ApiResponse<ReModel<List<Relay>>> {
        return Api.queryRelayList(createUserId, operationFlag, powerId)
    }

    suspend fun saveOrUpdateRelay(
        relayId: String,
        relayName: String,
        imei: String,
        powerStationId: String,
        createUserId: String,
        id: Int?
    ): ApiResponseWithNull<Int> {
        return Api.saveOrUpdateRelay(relayId, relayName, imei, powerStationId, createUserId, id)
    }

    suspend fun queryRelayComponentList(
        relayId: String,
        pageNo: Int,
        pageSize: Int,
        operationFlag: String?,
        chipId: String?,
        groupName: String?,
        createUserId: String
    ): ApiResponse<ReModel<List<Optimizer>>> {
        return Api.queryRelayComponentList(
            relayId,
            pageNo,
            pageSize,
            operationFlag,
            chipId,
            groupName,
            createUserId
        )
    }

    suspend fun queryRelayListForCloudId(
        createUserId: String,
        operationFlag: String, 
        cloudId: String
    ): ApiResponse<ReModel<List<Relay>>> {
        return Api.queryRelayListForCloudId(createUserId, operationFlag, cloudId)
    }

    suspend fun changeComponentGroup(
        id: String,
        relayId: String?,
        createUserId: String
    ): ApiResponseWithNull<Int> {
        return Api.changeComponentGroup(id, relayId, createUserId)
    }

    suspend fun changeComponent(
        id: String,
        relayId: String?,
        createUserId: String
    ): ApiResponseWithNull<Int> {
        return Api.changeComponent(id, relayId, createUserId)
    }

    suspend fun queryRelayGroupList(
        relayId: String,
        groupName: String,
        operationFlag: String,
        createUserId: String,
        pageSize: Int
    ): ApiResponse<ReModel<List<Group>>> {
        return Api.queryRelayGroupList(relayId, groupName, operationFlag, createUserId, pageSize)
    }
    
    /** 查询未读消息数量 */
    suspend fun queryUnreadMessageCount(
        powerIdList: List<String>
    ): ApiResponse<Int> {
        return Api.queryUnreadMessageCount(powerIdList)
    }
    
    /** 设置消息为已读 */
    suspend fun setMessageRead(ids: String): ApiResponseWithNull<Int> {
        return Api.setMessageRead(ids)
    }

    /** 查询历史电量 */
    suspend fun queryHistoryKwh(
        powerStationId: String,
        day: String
    ): ApiResponse<HistoryKwh> {
        return Api.queryHistoryKwh(powerStationId, day)
    }
}