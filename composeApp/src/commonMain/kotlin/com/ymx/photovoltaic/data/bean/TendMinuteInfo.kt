package com.ymx.photovoltaic.data.bean

import kotlinx.serialization.Serializable

@Serializable
data class TendMinuteInfo(
    var minDay: String = "",
    var maxDay: String = "",
    var maxKwh: Float = 0f,
    var outputCurrentMax: Float = 0f,
    var outputVoltageMax: Float = 0f,
    var powerMax: Float = 0f,
    var data: ArrayList<TendMinuteData>
) {
    @Serializable
    data class TendMinuteData(
        var createTimeCh: String = "",
        var createTime: Long = 0,
        var batchNo: String = "",
        var powerStationId: String = "",
        var componentCount: Int = 0,
        val kwh: Float = 0f,
        var power: Float = 0f,
        var outputCurrent: Float = 0f,
        var outputVoltage: Float = 0f
    )

}

