package com.ymx.photovoltaic.data.bean

import kotlinx.serialization.Serializable

@Serializable
data class TendMinuteGroupInfo(
    var minDay: String = "",
    var maxDay: String = "",
    var data: ArrayList<TendMinuteGroupData>
)

@Serializable
data class TendMinuteGroupData(
    var outputCurrent: Float = 0f,
    var outputVoltage: Float = 0f,
    var power: Float = 0f,
    var groupName: String = "",
    var groupId: String = "",
    var list: ArrayList<TendMinuteGroupList>
)

@Serializable
data class TendMinuteGroupList(
    var createTimeCh: String = "",
    var createTime: Long = 0,
    var batchNo: Long = 0,
    var powerStationId: String = "",
    var componentCount: Int = 0,
    var kwh: Float = 0f,
    var power: Float = 0f,
    var outputCurrent: Float = 0f,
    var outputVoltage: Float = 0f
)
