package com.ymx.photovoltaic.data.bean

import kotlinx.serialization.Serializable

@Serializable
data class Station(

    val countries: String = "",
    val countriesId: String = "",
    val districtId: String = "",

    val cityId: String = "",
    val cityName: String = "",
    val cityNameEn: String = "",

    val province: String = "",
    val provinceName: String = "",
    val provinceNameEn: String = "",

    val streetName: String = "",

    val cloudTerminalNum: Int = 0,
    val componentNum: Int = 0,
    val inverterNum: Int = 0,
    var status: Int = 0,
    val createTime: Long = 0,
    val createTimeCh: String = "",


    val createT: String = "",
    val createUserId: String = "",
    val createUserName: String = "",
    val id: String = "",

    val streetId: String = "",
    var systemName: String = "",
    val systemNo: String = "",
    val collectGap: Int = 0,
    var sunuptime: String = "",
    val sundowntime: String = "",
    val power: Int = 100,
    val grade: Int = 1,
    val type: Int = 1,
)
