package com.ymx.photovoltaic.data.bean

import kotlinx.serialization.Serializable

@Serializable
data class Optimizer(
    val belongsGroupId: String = "",
    val belongsGroupName: String = "",
    val chipId: String = "",
    val cloudId: String = "",
    val componentNo: String = "",
    val createTimeCh: String = "",
    val createUserId: String = "",
    val createUserName: String = "",
    val id: String = "",
    val imei: String = "",
    val model: String = "",
    val producersName: String = "",
    val modelName: String = "",
    val powerStationId: String = "",
    val powerStationName: String = "",
    val producers: String = "",
    val serialNo: String = "",
    val createTime: Long = 0,
    val createType: Int = 0,
    val groupType: Int = 0,
    val isAuth: Int = 0,
    val isDelete: Int = 0,
    val isException: Int = 0,
    val isPlace: Int = 0,
    val isRegister: Int = 0,
    val status: Int = 0
)
