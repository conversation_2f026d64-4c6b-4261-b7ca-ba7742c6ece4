package com.ymx.photovoltaic.data

import com.ymx.photovoltaic.data.bean.Relay

object AppGlobal {
    var mId: String=""
    var userName: String=""
    var powerStationId: String=""
    var imei: String=""
    var powerStationName: String=""
    var powerDistrictId: String=""
    var powerDistrictName: String=""
    var collectorId: String=""
    var sunUpTime: String=""
    var sunDownTime: String=""
    var powCreateTime: Long=0
    var powerIdList: List<String>?=null
    var tempTabTexts: List<String> = emptyList()
    var powerStatus: String = ""
    var stationPower: Int = 0
    var powerStationType: Int = 0

    // 存储中继列表
    var relayList: List<Relay> = emptyList()

    // 存储中继器ID到组串id和优化器ID列表的映射
    var relayChipIdMap: Map<String, Map<String, List<String>>> = emptyMap()
}