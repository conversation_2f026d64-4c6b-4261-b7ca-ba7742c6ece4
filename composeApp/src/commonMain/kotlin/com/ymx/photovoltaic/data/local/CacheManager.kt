package com.ymx.photovoltaic.data.local

import com.russhwolf.settings.set
import com.ymx.photovoltaic.data.bean.User
import com.ymx.photovoltaic.platform.SettingsManager

/**
 * 项目中用到的一些缓存
 *
 * <AUTHOR> 2022/4/8
 */
object CacheManager {

    private const val KEY_SEARCH_HISTORY = "search_history"
    private const val KEY_FIRST_USE = "first_use"
    private const val KEY_LOGIN_TIME = "login_time"

    private const val KEY_IS_LOGIN = "is_login"
    private const val KEY_MID = "mid"
    private const val KEY_USER_NAME = "user_name"
    private const val KEY_USER_NICKNAME = "user_nickname"
    private const val KEY_USER_EMAIL = "user_email"
    private const val KEY_USER_PHONE = "user_phone"
    private const val KEY_USER_TYPE = "user_type"
    private const val KEY_USER_TOKEN = "user_token"
    private const val KEY_LAN = "language"
    
    // 添加记住用户名和密码的键
    private const val KEY_REMEMBER_CREDENTIALS = "remember_credentials"
    private const val KEY_SAVED_USERNAME = "saved_username"
    private const val KEY_SAVED_PASSWORD = "saved_password"

    private val settings by lazy { SettingsManager.defaultSettings }

    /** 存储是否首次使用APP */
    fun saveFirstUse(isFirstUse: Boolean): Boolean {
        settings[KEY_FIRST_USE] = isFirstUse
        return true
    }

    /** 是否首次使用APP */
    fun isFirstUse(): Boolean {
        return settings.getBoolean(KEY_FIRST_USE, true)
    }

    // 登录成功，保存用户数据
    fun loginOk(user: User) {
        // 只保存必要的用户字段，不再保存整个用户对象
        settings[KEY_IS_LOGIN] = true
        settings[KEY_MID] = user.mId
        settings[KEY_USER_NAME] = user.name
        settings[KEY_USER_NICKNAME] = user.nickName
        settings[KEY_USER_EMAIL] = user.email
        settings[KEY_USER_PHONE] = user.phone
        settings[KEY_USER_TYPE] = user.type
        settings[KEY_USER_TOKEN] = user.token
        
        // 同时更新UserManager中的数据
        UserManager.saveUserId(user.mId)
        UserManager.saveToken(user.token)
    }

    fun saveLanguage(language: String) {
        settings[KEY_LAN] = language
    }

    fun getLanguage(): String {
        return settings.getString(KEY_LAN, "zh")
    }

    // 退出登录，清空用户数据
    fun logout() {
        settings.remove(KEY_MID)
        settings.remove(KEY_USER_NAME)
        settings.remove(KEY_USER_NICKNAME)
        settings.remove(KEY_USER_EMAIL)
        settings.remove(KEY_USER_PHONE)
        settings.remove(KEY_USER_TYPE)
        settings.remove(KEY_USER_TOKEN)
        settings[KEY_IS_LOGIN] = false
        
        // 如果不记住凭据，则清除保存的用户名和密码
        if (!isRememberCredentials()) {
            clearSavedCredentials()
        }
    }

    fun getUser(): User? {
        val mid = settings.getString(KEY_MID, "")
        if (mid.isEmpty()) return null
        
        return User(
            mId = mid,
            name = settings.getString(KEY_USER_NAME, ""),
            nickName = settings.getString(KEY_USER_NICKNAME, ""),
            email = settings.getString(KEY_USER_EMAIL, ""),
            phone = settings.getString(KEY_USER_PHONE, ""),
            type = settings.getInt(KEY_USER_TYPE, 0),
            token = settings.getString(KEY_USER_TOKEN, "")
        )
    }

    /** 存储是否登录 */
    fun saveIsLogin(isLogin: Boolean): Boolean {
        settings[KEY_IS_LOGIN] = isLogin
        return true
    }

    /** 判断是否登录 */
    fun isLogin(): Boolean {
        return settings.getBoolean(KEY_IS_LOGIN, false)
    }

    /** 存储是否登录 */
    fun saveMid(mid: String): Boolean {
        settings[KEY_MID] = mid
        return true
    }

    /** 判断是否登录 */
    fun getMid(): String? {
        return settings.getString(KEY_MID, "")
    }

    fun removeMid() {
         settings.remove(KEY_MID)
    }


    /** 存储登录时间 */
    fun getLoginTime(): Long {
        return  settings.getLong(KEY_LOGIN_TIME,0)
    }

    /** 保存是否记住用户名和密码 */
    fun saveRememberCredentials(remember: Boolean): Boolean {
        settings[KEY_REMEMBER_CREDENTIALS] = remember
        return true
    }

    /** 获取是否记住用户名和密码 */
    fun isRememberCredentials(): Boolean {
        return settings.getBoolean(KEY_REMEMBER_CREDENTIALS, true)
    }

    /** 保存用户名 */
    fun saveUsername(username: String): Boolean {
        settings[KEY_SAVED_USERNAME] = username
        return true
    }

    /** 获取保存的用户名 */
    fun getSavedUsername(): String {
        return settings.getString(KEY_SAVED_USERNAME, "") ?: ""
    }

    /** 保存密码 */
    fun savePassword(password: String): Boolean {
        settings[KEY_SAVED_PASSWORD] = password
        return true
    }

    /** 获取保存的密码 */
    fun getSavedPassword(): String {
        return settings.getString(KEY_SAVED_PASSWORD, "") ?: ""
    }

    /** 清除保存的用户名和密码 */
    fun clearSavedCredentials() {
        settings.remove(KEY_SAVED_USERNAME)
        settings.remove(KEY_SAVED_PASSWORD)
    }

}