package com.ymx.photovoltaic.data.bean

import kotlinx.serialization.Serializable

@Serializable
data class Collector(

    val id: String = "",
    val powerStationId: String = "",
    val inverterId: String = "",
    val powerStationName: String = "",
    val cloudNo: String = "",
    val cloudName: String = "",
    val bridgeNum: String = "",
    val imei: String = "",
    val isRegister: Int = 0,
    val serialNo: String = "",
    val sc: String = "",
    val createType: Int = 0,
    val description: String = "",
    val createUserId: String = "",
    val createUserName: String = "",
    val xz: String = "",
    val yz: String = "",
    val position: String = "",
    val groupNum: String = "",
    val qb: String = "",
    val divtop: String = "",
    val divleft: String = "",
    val laystatus: String = "",
    val language: String = "",
    val producers: String = "",
    val producersName: String = "",
    val model: String = "",
    val modelName: String = "",
    val chipId: String = "",
    val softVersion: String = "",
    val bootPartition: String = "",
    val updateTime: String = "",
    val mcu: String = "",
    val bomId: String = "",
    val hardVersion: String = "",
    val createTime: Long = 0,
    val createTimeCh: String = "",
    val createTimeStart: String = "",
    val createTimeEnd: String = "",
    val isDelete: Int = 0,
    val selectCloudTerminal: String = "",
    val selectInverter: String = ""

)
