<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- 基础主题 -->
    <style name="Theme.App" parent="android:Theme.Material.Light.NoActionBar">
        <!-- 使状态栏变为半透明  允许应用内容延伸到状态栏区域下方 -->
        <item name="android:windowTranslucentStatus">true</item>
        <!-- 隐藏状态栏，方便截图 -->
<!--        <item name="android:windowFullscreen">true</item>-->

    </style>


    <!-- YMX 品牌启动主题 -->
    <style name="Theme.App.Starting.Ymx" parent="Theme.App">
        <item name="android:windowBackground">@drawable/ymx_splash</item>
    </style>

    <!-- LJ 品牌启动主题 -->
    <style name="Theme.App.Starting.Lj" parent="Theme.App">
    </style>
</resources>