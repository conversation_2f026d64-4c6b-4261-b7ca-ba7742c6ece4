package com.ymx.photovoltaic.platform

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.util.Log
import androidx.core.content.FileProvider
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileWriter

/**
 * 文件工具类 - Android实现
 */
actual object FileUtil {
    private const val TAG = "FileUtil"
    private const val WECHAT_PACKAGE = "com.tencent.mm"

    // Context引用，需要在应用启动时设置
    private lateinit var appContext: Context

    /**
     * 设置应用上下文
     * 在应用启动时调用一次
     */
    fun init(context: Context) {
        appContext = context.applicationContext
    }

    /**
     * 保存文本内容到临时文件 - Android实现
     */
    actual suspend fun saveTextToTempFile(content: String, fileName: String): Flow<Result<String>> = flow {
        try {
            val file = withContext(Dispatchers.IO) {
                val cacheDir = appContext.cacheDir
                val file = File(cacheDir, fileName)
                FileWriter(file).use { writer ->
                    writer.write(content)
                }
                file.absolutePath
            }
            emit(Result.success(file))
        } catch (e: Exception) {
            Log.e(TAG, "保存文件失败", e)
            emit(Result.failure(e))
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 分享文件到微信 - Android实现
     */
    actual suspend fun shareFileToWeChat(filePath: String, mimeType: String?): Flow<Result<Boolean>> = flow {
        try {
            val result = withContext(Dispatchers.IO) {
                val file = File(filePath)
                if (!file.exists()) {
                    throw IllegalArgumentException("文件不存在: $filePath")
                }

                val uri = getFileUri(file)
                val intent = Intent(Intent.ACTION_SEND).apply {
                    setPackage(WECHAT_PACKAGE)
                    type = mimeType ?: getMimeType(file)
                    putExtra(Intent.EXTRA_STREAM, uri)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }

                // 授予微信读取权限
                appContext.grantUriPermission(
                    WECHAT_PACKAGE,
                    uri,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
                )

                // 启动分享活动
                appContext.startActivity(Intent.createChooser(intent, "分享文件到微信"))
                true
            }
            emit(Result.success(result))
        } catch (e: Exception) {
            Log.e(TAG, "分享文件到微信失败", e)
            emit(Result.failure(e))
        }
    }.flowOn(Dispatchers.Main)

    /**
     * 检查微信是否已安装 - Android实现
     */
    actual fun isWeChatInstalled(): Boolean {
        return try {
            appContext.packageManager.getPackageInfo(WECHAT_PACKAGE, 0)
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取文件的MIME类型
     */
    private fun getMimeType(file: File): String {
        return when {
            file.name.endsWith(".txt", ignoreCase = true) -> "text/plain"
            file.name.endsWith(".pdf", ignoreCase = true) -> "application/pdf"
            file.name.endsWith(".doc", ignoreCase = true) || file.name.endsWith(".docx", ignoreCase = true) ->
                "application/msword"
            file.name.endsWith(".xls", ignoreCase = true) || file.name.endsWith(".xlsx", ignoreCase = true) ->
                "application/vnd.ms-excel"
            file.name.endsWith(".jpg", ignoreCase = true) || file.name.endsWith(".jpeg", ignoreCase = true) ->
                "image/jpeg"
            file.name.endsWith(".png", ignoreCase = true) -> "image/png"
            else -> "*/*"
        }
    }

    /**
     * 获取文件的URI
     */
    private fun getFileUri(file: File): Uri {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            FileProvider.getUriForFile(
                appContext,
                "${appContext.packageName}.FileProvider",
                file
            )
        } else {
            Uri.fromFile(file)
        }
    }

    /**
     * 分享文件到任意应用 - Android实现
     */
    actual suspend fun shareFile(filePath: String, mimeType: String?): Flow<Result<Boolean>> = flow {
        try {
            val result = withContext(Dispatchers.IO) {
                val file = File(filePath)
                if (!file.exists()) {
                    throw IllegalArgumentException("文件不存在: $filePath")
                }

                val uri = getFileUri(file)
                val intent = Intent(Intent.ACTION_SEND).apply {
                    type = mimeType ?: getMimeType(file)
                    putExtra(Intent.EXTRA_STREAM, uri)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }

                // 启动系统分享活动
                appContext.startActivity(Intent.createChooser(intent, "分享文件").apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                })
                true
            }
            emit(Result.success(result))
        } catch (e: Exception) {
            Log.e(TAG, "分享文件失败", e)
            emit(Result.failure(e))
        }
    }.flowOn(Dispatchers.Main)
}