package com.ymx.photovoltaic.platform

import com.ymx.photovoltaic.data.local.CacheManager
import java.util.Locale

/**
 * 多平台语言ViewModel
 * 使用expect/actual模式实现平台特定的语言切换逻辑
 */

actual fun updateLanguage() {
    // 获取当前设置的语言
    val languageCode = CacheManager.getLanguage()
    val locale = when (languageCode) {
        "en" -> Locale.ENGLISH
        else -> Locale.CHINESE
    }
    
    // 设置全局Locale
    Locale.setDefault(locale)
    
    // 获取当前Context并重启MainActivity
//    val context = getKoin().get<Context>()
//    val intent = Intent(context, MainActivity::class.java)
//    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
//    context.startActivity(intent)
}