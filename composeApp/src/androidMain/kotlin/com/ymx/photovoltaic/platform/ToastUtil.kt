package com.ymx.photovoltaic.platform

import android.content.Context
import android.view.Gravity
import android.widget.Toast
import com.ymx.photovoltaic.getAppContext

/**
 * Toast封装工具类 - Android实现
 */
actual object ToastUtil {
    /**
     * 显示短时间的提示信息
     *
     * @param message 显示的消息
     */
    actual fun showShort(message: String) {
        val context = getAppContext()
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    /**
     * 显示长时间的提示信息
     *
     * @param message 显示的消息
     */
    actual fun showLong(message: String) {
        val context = getAppContext()
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
    }

    /**
     * 在屏幕中央显示短时间的提示信息
     *
     * @param message 显示的消息
     */
    actual fun showShortInCenter(message: String) {
        val context = getAppContext()
        Toast.makeText(context, message, Toast.LENGTH_SHORT).apply {
            setGravity(Gravity.CENTER, 0, 0)
            show()
        }
    }

    /**
     * 在屏幕中央显示长时间的提示信息
     *
     * @param message 显示的消息
     */
    actual fun showLongInCenter(message: String) {
        val context = getAppContext()
        Toast.makeText(context, message, Toast.LENGTH_LONG).apply {
            setGravity(Gravity.CENTER, 0, 0)
            show()
        }
    }

    // 保留原有的接口以便兼容原有代码
    fun showShort(context: Context, msg: String) {
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show()
    }

    fun showLong(context: Context, msg: String) {
        Toast.makeText(context, msg, Toast.LENGTH_LONG).show()
    }

    fun showShortInCenter(context: Context, msg: String) {
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).apply {
            setGravity(Gravity.CENTER, 0, 0)
            show()
        }
    }

    fun showLongInCenter(context: Context, msg: String) {
        Toast.makeText(context, msg, Toast.LENGTH_LONG).apply {
            setGravity(Gravity.CENTER, 0, 0)
            show()
        }
    }
}