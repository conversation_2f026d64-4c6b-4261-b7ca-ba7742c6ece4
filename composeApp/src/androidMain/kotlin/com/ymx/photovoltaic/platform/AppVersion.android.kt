package com.ymx.photovoltaic.platform

import android.content.Context
import org.koin.mp.KoinPlatform

/**
 * Android平台获取APP版本号的具体实现
 */
actual fun getAppVersion(): String {
    val context = KoinPlatform.getKoin().get<Context>()
    return try {
        val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
        packageInfo.versionName?:"1.0.0"
    } catch (e: Exception) {
        "1.0.0" // 默认版本号
    }
} 