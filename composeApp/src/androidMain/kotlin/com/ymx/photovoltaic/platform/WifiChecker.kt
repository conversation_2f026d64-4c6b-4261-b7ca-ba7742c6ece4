package com.ymx.photovoltaic.platform

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import com.ymx.photovoltaic.getAppContext

/**
 * Android平台的WiFi网络检测工具类
 * 
 * 该类可以配合KtorSocketUtils使用，用于在发送Socket请求前检查WiFi网络状态
 * 注意：此功能需要以下权限:
 * - android.permission.ACCESS_NETWORK_STATE
 */
object WifiChecker {
    
    /**
     * 检查是否连接到WiFi网络
     * 
     * @return 如果连接到WiFi网络，则返回true；否则返回false
     */
    fun isWifiConnected(): Boolean {
        val context = getAppContext()
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return getWifiNetwork(connectivityManager) != null
    }
    
    /**
     * 获取WiFi网络对象
     * 
     * @param connectivityManager 连接管理器
     * @return WiFi网络对象，如果没有WiFi连接，则返回null
     */
    fun getWifiNetwork(connectivityManager: ConnectivityManager): Network? {
        // 检查现有网络
        val networks = connectivityManager.allNetworks
        for (network in networks) {
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            if (capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true) {
                return network
            }
        }
        return null
    }
    
    /**
     * 绑定Socket到WiFi网络
     * 
     * 在Android上，这可以确保Socket通过WiFi网络发送数据，即使设备同时连接了移动数据
     * 
     * @param socket 要绑定的Socket
     * @return 是否成功绑定
     */
    fun bindSocketToWifi(socket: java.net.Socket): Boolean {
        try {
            val context = getAppContext()
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val wifiNetwork = getWifiNetwork(connectivityManager)
            
            if (wifiNetwork != null) {
                wifiNetwork.bindSocket(socket)
                return true
            }
            return false
        } catch (e: Exception) {
            println("绑定Socket到WiFi网络失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 获取WiFi网络信息
     * 
     * @return WiFi网络信息字符串
     */
    fun getWifiInfo(): String {
        val context = getAppContext()
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = getWifiNetwork(connectivityManager)
        
        if (network != null) {
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            val builder = StringBuilder()
            
            builder.append("WiFi连接状态: 已连接\n")
            
            if (capabilities != null) {
                builder.append("网络能力:\n")
                if (capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)) {
                    builder.append("- 互联网访问\n")
                }
                if (capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_NOT_METERED)) {
                    builder.append("- 非计费网络\n")
                }
                if (capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_NOT_RESTRICTED)) {
                    builder.append("- 非受限网络\n")
                }
                if (capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_NOT_VPN)) {
                    builder.append("- 非VPN网络\n")
                }
            }
            
            return builder.toString()
        } else {
            return "WiFi连接状态: 未连接"
        }
    }
} 