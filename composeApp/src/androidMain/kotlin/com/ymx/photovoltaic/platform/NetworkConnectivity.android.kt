package com.ymx.photovoltaic.platform

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import com.ymx.photovoltaic.getAppContext
import io.ktor.network.sockets.Socket

/**
 * Android平台的网络连接状态检测实现
 * 
 * 该实现使用WifiChecker和Android系统API检测WiFi网络状态
 * 注意：此功能需要以下权限:
 * - android.permission.ACCESS_NETWORK_STATE
 */
actual object NetworkConnectivity {


    /**
     * 检查设备是否连接到WiFi网络
     * 
     * @return 如果连接到WiFi网络，则返回true；否则返回false
     */
    actual fun isWifiConnected(): Boolean {
        return WifiChecker.isWifiConnected()
    }
    
    /**
     * 尝试将Ktor Socket绑定到WiFi网络
     * 
     * 注意：Ktor网络库使用的Socket与Java标准库的Socket不同，
     * 此方法使用反射技术获取Ktor Socket的底层java.net.Socket，
     * 然后通过ConnectivityManager将其绑定到WiFi网络。
     * 
     * @param socket Ktor Socket对象
     * @return 如果成功绑定，则返回true；否则返回false
     */
    actual fun bindSocketToWifi(socket: Socket): Boolean {
        try {
            // 尝试获取Ktor Socket的底层java.net.Socket
            val socketClass = socket::class.java
            // 反射获取底层的java.net.Socket字段
            val channelField = socketClass.getDeclaredField("rawSocket")
            channelField.isAccessible = true
            val rawSocket = channelField.get(socket) as? java.net.Socket
            
            if (rawSocket != null) {
                // 使用WifiChecker绑定原生Socket到WiFi网络
                return WifiChecker.bindSocketToWifi(rawSocket)
            } else {
                println("无法获取底层Socket实例")
            }
        } catch (e: Exception) {
            println("绑定Ktor Socket到WiFi网络失败: ${e.message}")
        }
        
        // 如果上述方法失败，仅返回WiFi连接状态
        return isWifiConnected()
    }
    
    /**
     * 获取当前WiFi网络信息
     * 
     * @return WiFi网络信息字符串
     */
    actual fun getWifiInfo(): String {
        val baseInfo = WifiChecker.getWifiInfo()
        return baseInfo
    }
    
    /**
     * 检查设备是否连接到任何网络
     * 
     * @return 如果连接到任何网络，则返回true；否则返回false
     */
    fun isNetworkConnected(): Boolean {
        val context = getAppContext()
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        val network = connectivityManager.activeNetwork
        val capabilities = connectivityManager.getNetworkCapabilities(network)
        
        return capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
    }
    
    /**
     * 获取网络类型
     * 
     * @return 网络类型描述字符串
     */
    fun getNetworkType(): String {
        val context = getAppContext()
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        val network = connectivityManager.activeNetwork
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return "无网络连接"
        
        return when {
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WiFi"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "移动数据"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "以太网"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> "蓝牙网络"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN) -> "VPN"
            else -> "未知网络类型"
        }
    }
    
    /**
     * 强制后续网络请求使用WiFi网络
     * 
     * 在Android平台上，通过ConnectivityManager请求绑定进程到WiFi网络
     * 注意：这种方法在Android 10及以上版本可能受到限制
     * 
     * @return 是否成功设置网络偏好
     */
    actual fun forceUseWifiForRequests(): Boolean {
        try {
            val context = getAppContext()
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val wifiNetwork = WifiChecker.getWifiNetwork(connectivityManager)
            
            if (wifiNetwork != null) {
                // Android 5.0及以上版本可以使用此API
                // 请求进程默认使用WiFi网络
                connectivityManager.bindProcessToNetwork(wifiNetwork)
                println("已将进程绑定到WiFi网络")
                return true
            } else {
                println("无可用WiFi网络，无法强制使用WiFi")
                return false
            }
        } catch (e: Exception) {
            println("设置网络偏好失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 解除强制使用WiFi网络的设置
     * 
     * 在Android平台上，通过ConnectivityManager解除进程绑定到指定网络
     * 
     * @return 是否成功解除网络偏好
     */
    actual fun releaseWifiForRequests(): Boolean {
        try {
            val context = getAppContext()
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            
            // Android 5.0及以上版本可以使用此API
            // 传入null表示解除任何网络绑定
            connectivityManager.bindProcessToNetwork(null)
            println("已解除进程与WiFi网络的绑定")
            return true
        } catch (e: Exception) {
            println("解除网络偏好设置失败: ${e.message}")
            return false
        }
    }
} 