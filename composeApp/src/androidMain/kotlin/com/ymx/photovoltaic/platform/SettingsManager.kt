package com.ymx.photovoltaic.platform

import android.content.Context
import com.russhwolf.settings.Settings
import com.russhwolf.settings.SharedPreferencesSettings

/**
 * Settings工具类，用于替代MMKV
 * 提供统一的接口进行键值对存储
 */
actual object SettingsManager {
    private var _defaultSettings: Settings? = null

    // 创建不同的Settings实例，对应不同的存储域
    actual val defaultSettings: Settings
        get() = _defaultSettings ?: throw IllegalStateException("SettingsManager not initialized. Call initialize() first.")

    fun initialize(context: Context) {
        if (_defaultSettings == null) {
            _defaultSettings = SharedPreferencesSettings(
                context.getSharedPreferences(
                    "default_settings",
                    Context.MODE_PRIVATE
                )
            )
        }
    }
}