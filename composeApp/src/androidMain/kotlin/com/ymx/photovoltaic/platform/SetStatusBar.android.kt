package com.ymx.photovoltaic.platform

import android.app.Activity
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.core.view.WindowCompat

/**
 * 多平台语言ViewModel
 * 使用expect/actual模式实现平台特定的语言切换逻辑
 */
@Composable
actual fun SetStatusBar(color: Color, darkIcons: <PERSON>olean) {

    val activity = LocalContext.current as Activity
    val window = activity.window
    val insetsController = WindowCompat.getInsetsController(window, window.decorView)

    // 设置状态栏颜色
    window.statusBarColor = color.toArgb()

    // 设置状态栏图标颜色 true为深色  false为浅色
    insetsController.isAppearanceLightStatusBars = darkIcons
    if(darkIcons)
    {
        window.navigationBarColor = Color.White.toArgb() //设置导航栏颜色
    }
    else
    {
        window.navigationBarColor = Color.Black.toArgb() //设置导航栏颜色

    }
}