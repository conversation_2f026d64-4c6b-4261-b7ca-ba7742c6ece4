package com.ymx.photovoltaic.platform

import android.app.Activity
import android.content.Context
import android.hardware.camera2.CameraManager
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView

/**
 * Android平台特定的扫码视图组件
 * 将Native视图集成到Compose中
 */
@Composable
actual fun ScanViewIntegration(
    modifier: Modifier,
    onScanResult: (String) -> Unit,
    onError: (Exception) -> Unit
) {

}

// 保存上下文对象为静态变量，确保可以在非Composable函数中访问
private var applicationContext: Context? = null

@Composable
actual fun rememberApplicationContext() {
    val context = LocalContext.current
    // 立即保存应用上下文
    applicationContext = context.applicationContext
    
    // 使用DisposableEffect监听生命周期
    DisposableEffect(Unit) {
        onDispose { }
    }
}

/**
 * 切换闪光灯的Android实现 - 使用安卓原生Camera2 API
 */
actual fun togglePlatformFlashlight(turnOn: Boolean) {
    try {
        // 获取应用上下文
        val context = applicationContext
        if (context == null) {
            // 如果上下文为空，记录错误
            println("错误: 应用上下文为空，无法控制闪光灯")
            return
        }
        
        // 获取相机管理器服务
        val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
        
        // 获取后置摄像头ID (通常是第一个相机)
        val cameraIds = cameraManager.cameraIdList
        if (cameraIds.isEmpty()) {
            println("错误: 未找到相机设备")
            return
        }
        
        // 找到支持闪光灯的相机ID
        var flashCameraId: String? = null
        for (id in cameraIds) {
            val characteristics = cameraManager.getCameraCharacteristics(id)
            val hasFlash = characteristics.get(android.hardware.camera2.CameraCharacteristics.FLASH_INFO_AVAILABLE) ?: false
            if (hasFlash) {
                flashCameraId = id
                break
            }
        }
        
        if (flashCameraId == null) {
            println("错误: 未找到带闪光灯的相机")
            return
        }
        
        // 不直接使用Camera2 API控制闪光灯
        // 而是通过org.publicvalue.multiplatform.qrcode库提供的接口
        try {
            // 使用反射获取multiplatform-qrcode库的闪光灯控制器
            val scannerClass = Class.forName("org.publicvalue.multiplatform.qrcode.ScannerController")
            val instanceMethod = scannerClass.getDeclaredMethod("getInstance")
            val instance = instanceMethod.invoke(null)
            
            // 调用setTorch方法
            val setTorchMethod = scannerClass.getDeclaredMethod("setTorch", Boolean::class.java)
            setTorchMethod.invoke(instance, turnOn)
        } catch (e: Exception) {
            println("通过扫码库控制闪光灯失败，尝试直接控制: ${e.message}")
            // 如果反射失败，尝试原始方法
            cameraManager.setTorchMode(flashCameraId, turnOn)
        }
        
        // 打印成功信息
        println("闪光灯状态已切换为: ${if (turnOn) "开启" else "关闭"}")
    } catch (e: Exception) {
        // 处理异常
        println("控制闪光灯时出错: ${e.message}")
        e.printStackTrace()
    }
} 
