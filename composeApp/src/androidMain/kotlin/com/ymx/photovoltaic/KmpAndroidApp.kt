package com.ymx.photovoltaic

import android.app.Application
import android.content.Context
import com.ymx.photovoltaic.config.AppConfigInitializer
import com.ymx.photovoltaic.di.appModule
import com.ymx.photovoltaic.platform.FileUtil
import com.ymx.photovoltaic.platform.SettingsManager
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.startKoin
import org.koin.core.logger.Level

// 全局上下文变量
private lateinit var appContext: Context

/**
 * 初始化全局应用上下文
 */
fun initAppContext(context: Context) {
    appContext = context
}

/**
 * 获取全局应用上下文
 */
fun getAppContext(): Context {
    return appContext
}

/**
 * Application基类
 */
class KmpAndroidApp : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // 初始化全局应用上下文
        initAppContext(this)
        FileUtil.init(this)
        
        // 初始化AppConfig
        AppConfigInitializer.initialize(this)
        
        // 初始化SettingsManager
        SettingsManager.initialize(this)
        
        startKoin {
            androidLogger(Level.ERROR) // 设置日志级别
            androidContext(this@KmpAndroidApp)
            modules(appModule)
        }
    }
} 