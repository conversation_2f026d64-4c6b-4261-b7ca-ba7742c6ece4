package com.ymx.photovoltaic

//import com.ctrip.flight.mmkv.initialize
import androidx.compose.ui.window.ComposeUIViewController
import com.ymx.photovoltaic.di.appModule
import org.koin.core.context.startKoin

private var isKoinInitialized = false
private var isAppInitialized = false

fun MainViewController() = ComposeUIViewController {

//    val rootDir = NSSearchPathForDirectoriesInDomains(
//        NSDocumentDirectory,
//        NSUserDomainMask,
//        true
//    ).firstOrNull() as? String ?: NSTemporaryDirectory()
//    initializeMMKV(rootDir)
    // 初始化Koin
    // 确保只初始化一次
    if (!isKoinInitialized) {
        initKoin()
        isKoinInitialized = true
    }
    
    // 初始化App配置
    if (!isAppInitialized) {
        App.initialize()
        isAppInitialized = true
    }
    
    App()
}


//fun initializeMMKV(rootDir: String) {
//    initialize(rootDir)
//    println("MMKV Path: $rootDir")
//}

private fun initKoin() {
    startKoin {
        // 可以使用logger，但iOS没有androidLogger
        // 使用普通logger
//        logger(Logger.DEFAULT)
        // 添加模块
        modules(appModule)
    }
}