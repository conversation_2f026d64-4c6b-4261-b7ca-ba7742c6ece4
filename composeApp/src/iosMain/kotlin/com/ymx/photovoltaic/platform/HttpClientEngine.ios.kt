package com.ymx.photovoltaic.platform

import io.ktor.client.HttpClient
import io.ktor.client.engine.darwin.Darwin
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.cookies.AcceptAllCookiesStorage
import io.ktor.client.plugins.cookies.HttpCookies
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logger
import io.ktor.client.plugins.logging.Logging
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json

/**
 * 为iOS平台创建HttpClient引擎
 */
actual fun createPlatformEngine(): HttpClient {
    return HttpClient(Darwin) {
        // 配置超时时间
        engine {
            configureRequest {
                setAllowsCellularAccess(true)
            }
        }
        
        // 配置Cookie
        install(HttpCookies) {
            storage = AcceptAllCookiesStorage()
        }
        
        // 配置日志
        install(Logging) {
            logger = object : Logger {
                override fun log(message: String) {
                    // 可以使用LogUtil记录日志
                    // println("Ktor iOS: $message")
                }
            }
            level = LogLevel.BODY
        }
        
        // 配置JSON序列化
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                isLenient = true
                encodeDefaults = true
                coerceInputValues = true
            })
        }
        
        // 默认请求配置
        defaultRequest {
            // 这里可以添加默认的请求头等
        }
    }
}