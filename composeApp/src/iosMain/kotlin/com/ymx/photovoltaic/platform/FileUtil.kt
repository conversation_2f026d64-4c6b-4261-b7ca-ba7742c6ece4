package com.ymx.photovoltaic.platform

import kotlinx.cinterop.ExperimentalForeignApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import platform.CoreGraphics.CGRectMake
import platform.Foundation.NSFileManager
import platform.Foundation.NSString
import platform.Foundation.NSTemporaryDirectory
import platform.Foundation.NSURL
import platform.Foundation.NSUTF8StringEncoding
import platform.Foundation.dataUsingEncoding
import platform.Foundation.writeToFile
import platform.UIKit.UIApplication
import platform.UIKit.UIDocumentInteractionController

/**
 * 文件工具类 - iOS实现
 */
actual object FileUtil {
    private const val WECHAT_URL_SCHEME = "weixin://"

    /**
     * 保存文本内容到临时文件 - iOS实现
     */
    actual suspend fun saveTextToTempFile(content: String, fileName: String): Flow<Result<String>> = flow {
        try {
            val filePath = withContext(Dispatchers.Default) {
                // 获取临时目录
                val tempDir = NSTemporaryDirectory()
                val filePath = tempDir + fileName

                // 将字符串转换为NSData并写入文件
                val data = (content as NSString).dataUsingEncoding(NSUTF8StringEncoding)
                if (data != null) {
                    data.writeToFile(filePath, true)
                    filePath
                } else {
                    throw IllegalArgumentException("无法将内容转换为NSData")
                }
            }
            emit(Result.success(filePath))
        } catch (e: Exception) {
            println("保存文件失败: ${e.message}")
            emit(Result.failure(e))
        }
    }.flowOn(Dispatchers.Default)

    /**
     * 分享文件到微信 - iOS实现
     */
    actual suspend fun shareFileToWeChat(filePath: String, mimeType: String?): Flow<Result<Boolean>> = flow {
        try {
            val result = withContext(Dispatchers.Main) {
                if (!isWeChatInstalled()) {
                    throw IllegalStateException("微信未安装")
                }

                // 创建文件URL
                val fileManager = NSFileManager.defaultManager
                if (!fileManager.fileExistsAtPath(filePath)) {
                    throw IllegalArgumentException("文件不存在: $filePath")
                }

                // 使用UIDocumentInteractionController分享文件
                // 注意：这只是iOS中分享文件的通用方法，要特定发送到微信需要使用微信SDK
                val url = NSURL.fileURLWithPath(filePath)
                val controller = UIDocumentInteractionController.interactionControllerWithURL(url)

                // 由于这里无法获取到ViewController，这种方式可能不完整
                // 实际应用中需要通过UIDocumentInteractionControllerDelegate来处理
//                controller.presentOpenInMenuFromRect(
//                    CGRectZero,
//                    UIApplication.sharedApplication.keyWindow!!.rootViewController!!.view,
//                    true
//                )
                true
            }
            emit(Result.success(result))
        } catch (e: Exception) {
            println("分享文件到微信失败: ${e.message}")
            emit(Result.failure(e))
        }
    }.flowOn(Dispatchers.Main)

    /**
     * 分享文件到任意应用 - iOS实现
     */
    @OptIn(ExperimentalForeignApi::class)
    actual suspend fun shareFile(filePath: String, mimeType: String?): Flow<Result<Boolean>> = flow {
        try {
            val result = withContext(Dispatchers.Main) {
                // 创建文件URL
                val fileManager = NSFileManager.defaultManager
                if (!fileManager.fileExistsAtPath(filePath)) {
                    throw IllegalArgumentException("文件不存在: $filePath")
                }

                // 使用UIDocumentInteractionController分享文件
                val url = NSURL.fileURLWithPath(filePath)
                val controller = UIDocumentInteractionController.interactionControllerWithURL(url)
                
                // 获取当前最顶层的视图控制器来显示分享菜单
                val keyWindow = UIApplication.sharedApplication.keyWindow
                val rootViewController = keyWindow?.rootViewController
                if (rootViewController != null) {
                    // 这会显示系统的分享菜单，用户可以选择任意支持此类型文件的应用进行分享
                    // 不再限制为微信
                    val rect = CGRectMake(0.0, 0.0, 0.0, 0.0)
                        controller.presentOptionsMenuFromRect(
                            rect,
                            rootViewController.view,
                            true
                        )
                    true
                } else {
                    throw IllegalStateException("无法获取根视图控制器")
                }
            }
            emit(Result.success(result))
        } catch (e: Exception) {
            println("分享文件失败: ${e.message}")
            emit(Result.failure(e))
        }
    }.flowOn(Dispatchers.Main)

    /**
     * 检查微信是否已安装 - iOS实现
     * 
     * 注意：即使微信已安装，由于iOS的隐私保护机制，
     * 如果用户没有授权应用使用URL Schemes，此方法也可能返回false
     */
    actual fun isWeChatInstalled(): Boolean {
        return UIApplication.sharedApplication.canOpenURL(
            platform.Foundation.NSURL.URLWithString(WECHAT_URL_SCHEME)!!
        )
    }
}