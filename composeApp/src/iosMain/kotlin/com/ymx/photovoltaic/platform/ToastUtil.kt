package com.ymx.photovoltaic.platform

import kotlinx.cinterop.ExperimentalForeignApi
import platform.UIKit.UIAlertController
import platform.UIKit.UIAlertControllerStyleAlert
import platform.UIKit.UIApplication
import platform.darwin.DISPATCH_TIME_NOW
import platform.darwin.dispatch_after
import platform.darwin.dispatch_get_main_queue
import platform.darwin.dispatch_time

/**
 * Toast封装工具类 - iOS实现
 * 修复UIAlertController自动消失的问题
 */
actual object ToastUtil {
    // 定义不同持续时间
    private const val DURATION_SHORT = 2.0 // 2秒
    private const val DURATION_LONG = 3.5  // 3.5秒

    // 当前显示的Alert控制器
    private var currentAlertController: UIAlertController? = null

    /**
     * 显示短时间的提示信息
     *
     * @param message 显示的消息
     */
    actual fun showShort(message: String) {
        showAlert(message, DURATION_SHORT, false)
    }

    /**
     * 显示长时间的提示信息
     *
     * @param message 显示的消息
     */
    actual fun showLong(message: String) {
        showAlert(message, DURATION_LONG, false)
    }

    /**
     * 在屏幕中央显示短时间的提示信息
     *
     * @param message 显示的消息
     */
    actual fun showShortInCenter(message: String) {
        showAlert(message, DURATION_SHORT, true)
    }

    /**
     * 在屏幕中央显示长时间的提示信息
     *
     * @param message 显示的消息
     */
    actual fun showLongInCenter(message: String) {
        showAlert(message, DURATION_LONG, true)
    }

    /**
     * 显示Alert提示信息
     *
     * @param message 消息内容
     * @param duration 显示时长(秒)
     * @param center 是否居中显示
     */
    @OptIn(ExperimentalForeignApi::class)
    private fun showAlert(message: String, duration: Double, center: Boolean) {
        // 在主线程中执行UI操作
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, 0), dispatch_get_main_queue()) {
            // 先关闭当前显示的Alert（如果有的话）
            currentAlertController?.let { alert ->
                if (alert.presentingViewController != null) {
                    alert.dismissViewControllerAnimated(false, null)
                }
            }
            currentAlertController = null

            // 获取当前正在显示的视图控制器
            val rootController = UIApplication.sharedApplication.keyWindow?.rootViewController ?: return@dispatch_after
            var currentController = rootController

            // 找到最前面的视图控制器，注意避免无限循环
            var depth = 0
            while (currentController.presentedViewController != null && depth < 10) {
                currentController = currentController.presentedViewController!!
                depth++
            }

            // 创建提示控制器
            val alertController = UIAlertController.alertControllerWithTitle(
                null, // 不设置标题
                message,
                UIAlertControllerStyleAlert
            )

            // 保存当前Alert的引用
            currentAlertController = alertController

            // 显示提示框
            currentController.presentViewController(alertController, true) {
                // 显示完成后，使用GCD延迟执行关闭操作
                val delayInNanoseconds = (duration * 1_000_000_000).toLong()
                val popTime = dispatch_time(DISPATCH_TIME_NOW, delayInNanoseconds)

                dispatch_after(popTime, dispatch_get_main_queue()) {
                    // 确保这个Alert还是当前显示的Alert才关闭
                    if (currentAlertController == alertController && alertController.presentingViewController != null) {
                        alertController.dismissViewControllerAnimated(true) {
                            // 关闭完成后清空引用
                            if (currentAlertController == alertController) {
                                currentAlertController = null
                            }
                        }
                    }
                }
            }
        }
    }
}