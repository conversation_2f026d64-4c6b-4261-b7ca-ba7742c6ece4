package com.ymx.photovoltaic.platform

import kotlinx.cinterop.ExperimentalForeignApi
import platform.AVFoundation.AVCaptureDevice
import platform.AVFoundation.AVCaptureDeviceInput
import platform.AVFoundation.AVCaptureMetadataOutput
import platform.AVFoundation.AVCaptureSession
import platform.AVFoundation.AVCaptureTorchModeOff
import platform.AVFoundation.AVCaptureTorchModeOn
import platform.AVFoundation.AVCaptureVideoPreviewLayer
import platform.AVFoundation.AVLayerVideoGravityResizeAspectFill
import platform.AVFoundation.AVMediaTypeVideo
import platform.AVFoundation.AVMetadataObjectTypeQRCode
import platform.AVFoundation.hasTorch
import platform.AVFoundation.torchMode
import platform.UIKit.UIView
import kotlin.experimental.ExperimentalNativeApi
import kotlin.native.ref.WeakReference

/**
 * iOS平台下的ScanController实现
 * 使用AVFoundation框架实现扫码功能
 */
actual class ScanController {
    private var captureSession: AVCaptureSession? = null
    private var previewLayer: AVCaptureVideoPreviewLayer? = null
    @OptIn(ExperimentalNativeApi::class)
    private var containerView: WeakReference<UIView>? = null
    
    /**
     * 初始化扫码视图
     */
    @OptIn(ExperimentalNativeApi::class, ExperimentalForeignApi::class)
    fun initScanView(view: UIView) {
        containerView = WeakReference(view)
        
        // 创建AVCaptureSession
        captureSession = AVCaptureSession()
        
        // 获取后置摄像头
        val device = AVCaptureDevice.defaultDeviceWithMediaType(AVMediaTypeVideo)
        
        try {
            // 创建输入设备
            val input = device?.let { AVCaptureDeviceInput.deviceInputWithDevice(it, null) }
            if (input != null) {
                captureSession?.addInput(input)
            }
            
            // 创建输出并设置元数据类型
            val output = AVCaptureMetadataOutput()
            captureSession?.addOutput(output)
            output.setMetadataObjectTypes(listOf(AVMetadataObjectTypeQRCode))
            
            // 创建预览层
            val session = captureSession!!
            previewLayer = AVCaptureVideoPreviewLayer.layerWithSession(session)
            previewLayer?.videoGravity = AVLayerVideoGravityResizeAspectFill
            previewLayer?.frame = view.bounds
            view.layer.addSublayer(previewLayer!!)
            
        } catch (e: Throwable) {
            println("扫码器初始化错误: ${e.message}")
        }
    }
    
    /**
     * 启动扫描
     */
    actual fun startScanning(
        onResult: (String) -> Unit,
        onError: (Exception) -> Unit
    ) {
        captureSession?.startRunning()
    }
    
    /**
     * 停止扫描
     */
    actual fun stopScanning() {
        captureSession?.stopRunning()
    }
    
    /**
     * 切换闪光灯
     */
    @OptIn(ExperimentalForeignApi::class)
    actual fun toggleFlashlight(turnOn: Boolean) {
        val device = AVCaptureDevice.defaultDeviceWithMediaType(AVMediaTypeVideo)
        
        // 检查设备是否有闪光灯
        if (device?.hasTorch() == true) {
            try {
                device.lockForConfiguration(null)
                
                if (turnOn) {
                    device.torchMode = AVCaptureTorchModeOn
                } else {
                    device.torchMode = AVCaptureTorchModeOff
                }
                
                device.unlockForConfiguration()
            } catch (e: Throwable) {
                println("无法配置闪光灯: ${e.message}")
            }
        }
    }
} 