package com.ymx.photovoltaic.platform

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import kotlinx.cinterop.ExperimentalForeignApi
import platform.UIKit.UIApplication
import platform.UIKit.UIStatusBarStyleDarkContent
import platform.UIKit.UIStatusBarStyleLightContent
import platform.UIKit.setStatusBarStyle

/**
 * 多平台语言ViewModel
 * 使用expect/actual模式实现平台特定的语言切换逻辑
 */
@OptIn(ExperimentalForeignApi::class)
@Composable
actual fun SetStatusBar(color: Color, darkIcons: Boolean) {
    // iOS不能直接设置状态栏颜色，只能设置状态栏文字/图标颜色
    // 状态栏样式：darkIcons为true时使用深色图标（适合浅色背景），否则使用浅色图标（适合深色背景）
    val statusBarStyle = if (darkIcons) UIStatusBarStyleDarkContent else UIStatusBarStyleLightContent
    UIApplication.sharedApplication.setStatusBarStyle(statusBarStyle, false)
}