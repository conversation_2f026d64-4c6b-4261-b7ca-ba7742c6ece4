package com.ymx.photovoltaic.platform

import kotlinx.cinterop.ExperimentalForeignApi
import kotlinx.cinterop.addressOf
import kotlinx.cinterop.usePinned
import platform.CoreFoundation.CFStringConvertEncodingToNSStringEncoding
import platform.CoreFoundation.kCFStringEncodingGB_18030_2000
import platform.Foundation.NSData
import platform.Foundation.NSString
import platform.Foundation.NSUTF8StringEncoding
import platform.Foundation.create

/**
 * iOS平台特定的GBK解码实现
 *
 * 使用Core Foundation提供的编码支持来正确处理GBK编码
 * GBK编码被认为是GB18030编码的子集，因此可以使用kCFStringEncodingGB_18030_2000
 */
actual object GbkDecoder {
    
    /**
     * GB18030编码对应的NSStringEncoding值
     * 通过CFStringConvertEncodingToNSStringEncoding将Core Foundation编码转换为NSStringEncoding
     */
    private val gbkEncoding =
        CFStringConvertEncodingToNSStringEncoding(kCFStringEncodingGB_18030_2000.toUInt())
    
    /**
     * 在iOS平台上解码GBK编码的字节数组
     * 
     * 使用Core Foundation的GB18030编码支持
     * GBK编码是GB18030的子集，因此可以使用GB18030的编码
     * 
     * @param bytes 要解码的字节数组
     * @return 解码后的字符串
     */
    @OptIn(ExperimentalForeignApi::class)
    actual fun decodeGbk(bytes: ByteArray): String {
        // 创建NSData
        val data = bytes.usePinned { pinnedBytes ->
            NSData.create(
                bytes = pinnedBytes.addressOf(0),
                length = bytes.size.toULong()
            )
        }
        
        // 使用GB18030编码（GBK的超集）来解码数据
        return NSString.create(data = data, encoding = gbkEncoding)?.toString() ?: run {
            // 如果使用GB18030解码失败，尝试使用UTF-8作为后备方案
            println("警告: GB18030解码失败，尝试使用UTF-8")
            NSString.create(data = data, encoding = NSUTF8StringEncoding)?.toString() ?: ""
        }
    }
}