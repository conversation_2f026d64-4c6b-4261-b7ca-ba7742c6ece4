package com.ymx.photovoltaic.platform

import com.ymx.photovoltaic.data.local.CacheManager
import platform.Foundation.NSUserDefaults

/**
 * 多平台语言ViewModel
 * 使用expect/actual模式实现平台特定的语言切换逻辑
 */

actual fun updateLanguage()
{
    // 获取当前语言设置
    val languageCode = CacheManager.getLanguage()
    
    // 保存语言设置到NSUserDefaults
    // 更新NSLocale的当前语言设置
    // 这将触发iOS系统级别的语言变更通知
    val userDefaults = NSUserDefaults.standardUserDefaults
    userDefaults.setObject(listOf(languageCode), forKey = "AppleLanguages")
    userDefaults.setObject(languageCode, forKey = "AppleLocale")
    userDefaults.setObject(languageCode, forKey = "AppleLanguageCode")
    userDefaults.synchronize()
}