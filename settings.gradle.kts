rootProject.name = "Photovoltaic-KMP-APP"
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

pluginManagement {
    repositories {
        maven { url=uri( "https://maven.aliyun.com/repository/central") }
        maven { url=uri( "https://maven.aliyun.com/repository/public") }
        maven { url=uri( "https://maven.aliyun.com/repository/google") }
        maven { url=uri( "https://maven.aliyun.com/repository/gradle-plugin") }
        maven { url=uri( "https://developer.huawei.com/repo/") }
        maven { url=uri( "https://jitpack.io") }
        maven { url=uri( "https://repo.maven.apache.org/maven2") }
        google {
            mavenContent {
                includeGroupAndSubgroups("androidx")
                includeGroupAndSubgroups("com.android")
                includeGroupAndSubgroups("com.google")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositories {
        google()
        mavenCentral()
        maven { url=uri( "https://maven.aliyun.com/repository/central") }
        maven { url=uri( "https://maven.aliyun.com/repository/public") }
        maven { url=uri( "https://maven.aliyun.com/repository/google") }
        maven { url=uri( "https://maven.aliyun.com/repository/gradle-plugin") }
        maven { url=uri( "https://developer.huawei.com/repo/") }
        maven { url=uri( "https://jitpack.io") }
        maven { url=uri( "https://repo.maven.apache.org/maven2") }
        google {
            mavenContent {
                includeGroupAndSubgroups("androidx")
                includeGroupAndSubgroups("com.android")
                includeGroupAndSubgroups("com.google")
            }
        }
    }
}

include(":composeApp")
include(":app")
include(":lib_base")