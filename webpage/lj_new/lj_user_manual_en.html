<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>LONGi Smart PV APP User Manual</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: #252b3a;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            border-bottom: 1px solid #dfe1e6;
            margin-bottom: 24px;
            padding-bottom: 16px;
        }
        .title {
            font-size: 24px;
            font-weight: 500;
            margin: 0;
            padding: 0;
        }
        .toc {
            background: #f5f7fa;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 24px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        .toc > ul {
            padding-left: 0;
        }
        .toc a {
            color: #252b3a;
            text-decoration: none;
        }
        .toc a:hover {
            color: #0064cd;
        }
        h1 {
            font-size: 20px;
            font-weight: 500;
            margin: 24px 0 16px;
            padding-top: 24px;
            padding-bottom: 24px;
            /* border-top: 1px solid #dfe1e6; */
            text-align: center;
        }
        h2 {
            font-size: 18px;
            font-weight: 500;
            margin: 20px 0 12px;
        }
        h3 {
            font-size: 16px;
            font-weight: 500;
            margin: 16px 0 8px;
        }
        .content-section {
            margin-bottom: 24px;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #dfe1e6;
            border-radius: 4px;
            margin: 16px 0;
        }
        .image-pair {
            display: flex;
            gap: 20px;
            margin: 16px 0;
            flex-wrap: wrap;
            justify-content: center;
        }
        .image-pair img {
            margin: 0;
            max-width: 300px;
        }
        p {
            margin: 16px 0;
        }
    </style>
</head>
<body>
        <h1 class="title">LONGi Smart PV APP User Manual</h1>

    <div class="toc">
        <h2>Table of Contents</h2>
        <ul>
            <li><a href="#intro">1. Introduction</a>
                <ul>
                    <li><a href="#purpose">1.1 Purpose</a></li>
                </ul>
            </li>
            <li><a href="#features">2. Software Features</a>
                <ul>
                    <li><a href="#primary-interface">2.1 Primary Interface</a></li>
                    <li><a href="#secondary-interface">2.2 Secondary Interface</a></li>
                    <li><a href="#user-registration">2.3 User Registration</a></li>
                </ul>
            </li>
        </ul>
    </div>

    <div class="content-section">
        <h1 id="intro">1. Introduction</h1>
        <h2 id="purpose">1.1 Purpose</h2>
        <p>To help users quickly understand the system and operate it proficiently.</p>
    </div>

    <div class="content-section">
        <h1 id="features">2. Software Features</h1>

        <h2 id="primary-interface">2.1 Primary Interface</h2>

        <h3>2.1.1 Home Page</h3>
        <p>When you click the APP, if you haven't logged in before, you will first enter the login interface. Enter your registered account and password on the login interface, then click login. After successful login, you will enter the application home page.</p>

        <div class="image-pair">
            <img src="images/image2.png" alt="Login Interface">
            <img src="images/image3.png" alt="Home Interface">
        </div>

        <p>The search box at the top of the home page allows you to search by entering the power station name. Below the search box is the power station status bar, which displays the total, normal, abnormal, and offline power station counts in sequence. Below the power station status bar is the power station alarm bar, which displays recent alarm information. If there is alarm information, clicking on it will jump to the operation and maintenance interface.</p>

        <p>Below the alarm information is the power station list. Each power station displays information such as station name, location, operating time, and operating status.</p>

        <p>At the bottom of the home page are four tab buttons: besides the home page, they are Equipment, Operation & Maintenance, and Profile. Clicking the + button below will enter the interface for adding a new power station. After filling in the power station information, you can add a new power station.</p>

        <h3>2.1.2 Equipment Interface</h3>
        <p>Clicking the Equipment button will display a list of all the user's power stations.</p>

        <div class="image-pair">
            <img src="images/image5.png" alt="Equipment List Interface">
            <img src="images/image6.png" alt="Add Equipment Interface">
        </div>

        <p>Selecting and clicking on a power station will display all device types under this station, including smart gateways, repeaters, smart optimizers, and station strings.
            Clicking the + button below will pop up the device types that can be added to this power station. Currently, only one smart gateway can be added per power station. If the power station has not added a smart gateway, you need to add the smart gateway first before adding other devices. If a smart gateway has been added, you can add repeaters and strings.</p>

        <div class="image-pair">
            <img src="images/image7.png" alt="Collector Interface">
            <img src="images/image8.png" alt="Collector Edit Interface">
        </div>

        <p>Clicking the device configuration button of the smart gateway will enter the smart gateway management interface. Clicking the configure collector and configure repeater buttons on the smart gateway information bar will enter the configuration pages for the gateway and repeater. By connecting to the gateway's hotspot, you can perform query and configuration operations on the gateway and repeater respectively.</p>

        <div class="image-pair">
            <img src="images/image29.png" alt="String List Interface">
        </div>

        <p>Clicking the repeater device configuration button will display a list of all repeaters. Clicking the add optimizer and delete optimizer buttons for a specific repeater allows you to add and delete optimizers under this repeater.
            Clicking the smart optimizer device configuration button can display the power station's optimizer list. Clicking the device configuration button for station strings can display the string list. Selecting the add optimizer button for a specific string allows you to add optimizers for this string.</p>

        <div class="image-pair">
            <img src="images/image9.png" alt="String List Interface">
            <img src="images/image11.png" alt="Optimizer List Interface">
        </div>

        <h3>2.1.3 Operation & Maintenance Interface</h3>
        <p>Clicking the Operation & Maintenance tab will display a list of recent alarms. You can filter and search alarms by entering alarm content in the search box above.</p>

        <div class="image-pair">
            <img src="images/image4.png" alt="Message Interface">
        </div>

        <h3>2.1.4 Profile Interface</h3>
        <p>Clicking the Profile tab button will display the profile interface, which mainly contains functions related to user personal settings. Specifically, it includes Settings, Anti-counterfeiting Verification, Contact Us, User Manual, Feedback, Change Login Password, and Logout functions.</p>

        <p>Clicking Settings will jump to the settings sub-interface, where you can perform language switching operations.</p>

        <div class="image-pair">
            <img src="images/image13.png" alt="Profile Interface">
            <img src="images/image14.png" alt="Settings Interface">
        </div>

        <p>Clicking Feedback allows you to fill in and submit relevant feedback.</p>

        <div class="image-pair">
            <img src="images/image16.png" alt="Feedback Interface">
        </div>

        <p>Clicking Change Login Password allows you to change your password after entering the original password and new password. Clicking Logout allows you to log out of the system.</p>

        <div class="image-pair">
            <img src="images/image17.png" alt="Change Password Interface">
        </div>

        <h2 id="secondary-interface">2.2 Secondary Interface</h2>

        <h3>2.2.1 Report Interface</h3>
        <p>Clicking on a power station on the home page will enter the secondary interface of that power station. The secondary interface has three tabs at the bottom: Report, View, and Edit, which will be introduced in sequence below.</p>
        <p>Clicking the Report tab enters the report interface. Clicking Day, Month, Year, and All allows you to view the corresponding data reports in sequence.</p>

        <div class="image-pair">
            <img src="images/image21.png" alt="Report Interface - Daily">
            <img src="images/image22.png" alt="Report Interface - Monthly">
        </div>

        <h3>2.2.2 View Interface</h3>
        <p>The view interface can display multiple optimizers in a power station, arranging each optimizer according to their actual positions. The optimizers will display real-time power, temperature, and optimizer ID for each optimizer.</p>

        <p>Clicking the first floating button in the bottom right corner can refresh the data to get the latest information. You can use gestures to zoom the view, and clicking the second floating button can restore the zoom to its original scale and position.</p>

        <p>Clicking on a single optimizer will pop up a menu at the bottom, displaying the electrical parameters of this optimizer. Clicking the close button on the right can shut down this optimizer. If the optimizer is in a shutdown state, the right button will display "Open". Clicking "Open" can turn on this optimizer.</p>

        <div class="image-pair">
            <img src="images/image19.png" alt="View Interface">
            <img src="images/image20.png" alt="Optimizer Details">
        </div>

        <h3>2.2.3 Edit Interface</h3>
        <p>Clicking the Edit tab enters the edit interface, where you can set various power station-related information.</p>

        <div class="image-pair">
            <img src="images/image23.png" alt="Edit Interface">
        </div>

        <p>Clicking Power Station Information allows you to modify the basic information of the power station. Clicking Temperature Alarm allows you to set the temperature for optimizer high-temperature alarms and high-temperature shutdown.</p>

        <div class="image-pair">
            <img src="images/image25.png" alt="Power Station Information Edit">
            <img src="images/image26.png" alt="Temperature Alarm Settings">
        </div>

        <p>Clicking Shading Alarm allows you to set the shading ratio. The meaning of the shading ratio is that when an optimizer has shading and abnormal power generation, an alarm will be generated when the power reaches a certain percentage relative to the highest power of the same string. The delay time means how long to wait after reaching this ratio before generating an alarm.</p>

       <div class="image-pair">
        <img src="images/image27.png" alt="Shading Alarm Settings">
        </div>

    </div>
</body>
</html>