<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Smart PV APP User Manual</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: #252b3a;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            border-bottom: 1px solid #dfe1e6;
            margin-bottom: 24px;
            padding-bottom: 16px;
        }
        .title {
            font-size: 24px;
            font-weight: 500;
            margin: 0;
            padding: 0;
        }
        .toc {
            background: #f5f7fa;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 24px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        .toc > ul {
            padding-left: 0;
        }
        .toc a {
            color: #252b3a;
            text-decoration: none;
        }
        .toc a:hover {
            color: #0064cd;
        }
        h1 {
            font-size: 20px;
            font-weight: 500;
            margin: 24px 0 16px;
            padding-top: 24px;
            border-top: 1px solid #dfe1e6;
        }
        h2 {
            font-size: 18px;
            font-weight: 500;
            margin: 20px 0 12px;
        }
        h3 {
            font-size: 16px;
            font-weight: 500;
            margin: 16px 0 8px;
        }
        .content-section {
            margin-bottom: 24px;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #dfe1e6;
            border-radius: 4px;
            margin: 16px 0;
        }
        .image-pair {
            display: flex;
            gap: 20px;
            margin: 16px 0;
            flex-wrap: wrap;
        }
        .image-pair img {
            margin: 0;
            max-width: 300px;
        }
        p {
            margin: 16px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="title">Smart PV APP User Manual</h1>
    </div>

    <div class="toc">
        <h2>Table of Contents</h2>
        <ul>
            <li><a href="#intro">1. Introduction</a>
                <ul>
                    <li><a href="#purpose">1.1 Purpose</a></li>
                </ul>
            </li>
            <li><a href="#features">2. Software Features</a>
                <ul>
                    <li><a href="#primary-interface">2.1 Primary Interface</a></li>
                    <li><a href="#secondary-interface">2.2 Secondary Interface</a></li>
                    <li><a href="#user-registration">2.3 User Registration</a></li>
                </ul>
            </li>
        </ul>
    </div>

    <div class="content-section">
        <h1 id="intro">1. Introduction</h1>
        <h2 id="purpose">1.1 Purpose</h2>
        <p>To help users quickly understand the system and operate it proficiently.</p>
    </div>

    <div class="content-section">
        <h1 id="features">2. Software Features</h1>
        
        <h2 id="primary-interface">2.1 Primary Interface</h2>
        
        <h3>2.1.1 Homepage</h3>
        <p>When launching the APP, if you haven't logged in before, you'll first see the login screen. Enter your registered account and password on the login screen, then click login. After successful login, you'll enter the application homepage.</p>
        
        <div class="image-pair">
            <img src="images/image2.png" alt="Login Interface">
            <img src="images/image3.png" alt="Homepage Interface">
        </div>

        <p>The homepage displays four data cards at the top, showing the total, normal, abnormal, and offline power station counts respectively. Below is a search box where you can enter power station names. Recent alerts are displayed below the search box. Clicking on an alert will take you to the message interface.</p>
        
        <p>Below the alerts is the power station list. Each station displays its name, location, operating time, and status information.</p>
        
        <p>At the bottom of the homepage are four tab buttons: Home, Messages, Devices, and My Profile.</p>

        <h3>2.1.2 Message Interface</h3>
        <p>Click the Messages tab to view recent alert lists. Enter alert content in the top search box to filter and query alerts.</p>
        
        <img src="images/image4.png" alt="Message Interface">

        <h3>2.1.3 Device Interface</h3>
        <p>Click the Devices button to view all your power stations. Click the + button in the bottom right to enter the add station interface, where you can add a new station after filling in the information.</p>

        <div class="image-pair">
            <img src="images/image5.png" alt="Device List Interface">
            <img src="images/image6.png" alt="Add Device Interface">
        </div>

        <p>Select and click a power station to view its collectors. Currently, each station can only add one collector. Long press a collector to bring up the edit menu. Click the edit button to enter the collector information editing interface to modify collector information. The string and optimizer operations are similar - long press and click edit to enter the corresponding editing interface for information modification.</p>

        <div class="image-pair">
            <img src="images/image7.png" alt="Collector Interface">
            <img src="images/image8.png" alt="Collector Edit Interface">
        </div>

        <p>Click on a collector to view all its strings. Click the + button to enter the new string interface, fill in the relevant information, and click save to add a new string.</p>

        <div class="image-pair">
            <img src="images/image9.png" alt="String List Interface">
            <img src="images/image10.png" alt="Add String Interface">
        </div>

        <p>Click on a string to view all its optimizers. Click the + button to manually enter optimizer information and add a new optimizer. Click the scan button to enter the scanning interface and add an optimizer by scanning its QR code.</p>

        <div class="image-pair">
            <img src="images/image11.png" alt="Optimizer List Interface">
            <img src="images/image12.png" alt="Add Optimizer Interface">
        </div>

        <h3>2.1.4 My Profile Interface</h3>
        <p>Click the My Profile tab to view personal settings-related functions, including Settings, Messages, RID, Feedback, Password Modification, and Logout.</p>
        
        <p>Click Settings to jump to the settings sub-interface, where you can switch languages. If a new version is available for upgrade, the version number will show "new". Click the version number to download and upgrade to the new version. Screenshots are shown below.</p>

        <div class="image-pair">
            <img src="images/image13.png" alt="My Profile Interface">
            <img src="images/image14.png" alt="Settings Interface">
        </div>

        <p>Click RID to display the user's RID number, which is the unique identifier for JPush notifications. If users aren't receiving push notifications, they can provide their RID number for troubleshooting. Click Feedback to submit relevant feedback.</p>

        <div class="image-pair">
            <img src="images/image15.png" alt="RID Interface">
            <img src="images/image16.png" alt="Feedback Interface">
        </div>

        <p>Click Modify Login Password to change your password by entering the old and new passwords. Click Logout to log out of the application.</p>

        <div class="image-pair">
            <img src="images/image17.png" alt="Change Password Interface">
            <img src="images/image18.png" alt="Logout Interface">
        </div>

        <h2 id="secondary-interface">2.2 Secondary Interface</h2>
        
        <h3>2.2.1 View Interface</h3>
        <p>Click a power station on the homepage to enter its secondary interface. The secondary interface has three tabs: View, Report, and Edit.</p>
        
        <p>The View interface shows multiple optimizers in the power station, arranged according to their actual positions. Each optimizer displays its real-time power, temperature, and optimizer ID.</p>
        
        <p>Click the first floating button in the bottom right to refresh data and get the latest information. You can use gestures to zoom the view, and click the second floating button to restore the original scale and position.</p>
        
        <p>Click a single optimizer to bring up a bottom menu showing its electrical parameters. Click the close button on the right to shut down this optimizer. If the optimizer is shut down, the right button will show open. Click open to activate this optimizer.</p>

        <div class="image-pair">
            <img src="images/image19.png" alt="View Interface">
            <img src="images/image20.png" alt="Optimizer Details">
        </div>

        <h3>2.2.2 Report Interface</h3>
        <p>Click the Report tab to enter the report interface. Click Daily, Monthly, Yearly, or All to view corresponding data reports.</p>

        <div class="image-pair">
            <img src="images/image21.png" alt="Report Interface - Daily">
            <img src="images/image22.png" alt="Report Interface - Monthly">
        </div>

        <h3>2.2.3 Edit Interface</h3>
        <p>Click the Edit tab to enter the edit interface where you can set various power station information. Click Set WIFI to configure the power station collector's WIFI connection. First connect your phone to the collector's own WIFI, then enter the WIFI name and password and click save to set up the collector's server connection WIFI.</p>

        <div class="image-pair">
            <img src="images/image23.png" alt="Edit Interface">
            <img src="images/image24.png" alt="WIFI Settings Interface">
        </div>

        <p>Click Station Information to modify basic station information. Click Temperature Alert to set optimizer high-temperature alert and high-temperature shutdown temperatures.</p>

        <div class="image-pair">
            <img src="images/image25.png" alt="Station Information Edit">
            <img src="images/image26.png" alt="Temperature Alert Settings">
        </div>

        <p>Click Shading Alert to set the shading ratio. The shading ratio represents the power threshold relative to the highest power in the same string that triggers a power alert when the optimizer is shaded. The delay time indicates how long to wait after reaching this ratio before generating an alert.</p>

        <img src="images/image27.png" alt="Shading Alert Settings">

        <h2 id="user-registration">2.3 User Registration</h2>
        <p>The APP currently uses a registration code system. Users need to contact the backend administrator to generate a registration code first. Then users can register by entering their username, registration code, and password on the APP registration page.</p>

        <img src="images/image28.png" alt="User Registration Interface">
    </div>
</body>
</html>
