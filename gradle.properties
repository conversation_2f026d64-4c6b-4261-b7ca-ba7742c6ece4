#Kotlin
kotlin.code.style=official
kotlin.daemon.jvmargs=-Xmx2048M
kotlin.compiler.preciseCompilationResultsBackup=true
kotlin.incremental=true
kotlin.incremental.useClasspathSnapshot=true
kotlin.native.binary.memoryModel=experimental
kotlin.native.binary.freezing=disabled
kotlin.native.cacheKind=none
#kotlin.native.enableDependencyPropagation=false
kotlin.mpp.enableCInteropCommonization=true
kotlin.mpp.stability.nowarn=true

#Gradle
org.gradle.jvmargs=-Xmx4g -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configuration-cache=true

#Android
android.nonTransitiveRClass=true
android.useAndroidX=true

#org.jetbrains.compose.experimental.macos.enabled=true

#Kapt
#kapt.incremental.apt=true
#kapt.use.worker.api=true
#kapt.include.compile.classpath=false